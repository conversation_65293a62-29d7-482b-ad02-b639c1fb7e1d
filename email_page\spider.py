# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/9 13:15
File Name: spider.py
"""
import random
import requests
import threading
import pandas as pd
import os
from lxml import etree
from email_page.cookies import GoogleCookie
from proxy import ProxyManager


class GoogleSearch:
    def __init__(self, thread_id=0, cookie_file=None, proxy_manager=None, update_log=None):
        """
        Initialize the GoogleSearch class

        Args:
            thread_id: 线程ID,用于区分不同实例
            cookie_file: Cookie文件路径 (默认: 基于thread_id生成不同的文件名)
            proxy_manager: 代理管理器实例 (默认: 创建新的MyProxyManager)
        """
        # 如果没有提供cookie_file，则基于thread_id生成唯一文件名
        folder = 'cookies'
        if not os.path.exists(folder):
            os.makedirs(folder)
        if cookie_file is None:
            cookie_file = f"cookies/google_cookies_{thread_id}.json"
        self.update_log = update_log
        self.thread_id = thread_id
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)
        self.proxy_manager = proxy_manager

        # 添加失败计数器
        self.failure_count = 0
        self.max_failures = 10
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'

        ]
        self.base_headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'downlink': '10',
            'priority': 'u=0, i',
            'rtt': '50',
            'sec-ch-prefers-color-scheme': 'dark',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-arch': '"x86"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-form-factors': '"Desktop"',
            'sec-ch-ua-full-version': '"133.0.6943.142"',
            'sec-ch-ua-full-version-list': '"Not(A:Brand";v="99.0.0.0", "Google Chrome";v="133.0.6943.142", "Chromium";v="133.0.6943.142"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"15.0.0"',
            'sec-ch-ua-wow64': '?0',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'x-browser-channel': 'stable',
            'x-browser-copyright': 'Copyright 2025 Google LLC. All rights reserved.',
            'x-browser-year': '2025',
        }
        self.lock = threading.Lock()  # 添加线程锁用于同步操作

    def get_headers(self):
        """生成带有随机User-Agent的请求头"""
        headers = self.base_headers.copy()
        headers['user-agent'] = random.choice(self.user_agents)
        return headers

    def send_request(self, url, params, cookies, timeout=10, max_retries=2):
        """
        发送HTTP请求，带有重试机制

        Args:
            url: 目标URL
            params: URL参数
            cookies: 请求使用的Cookies
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数

        Returns:
            Response对象，失败则返回None
        """
        headers = self.get_headers()

        for attempt in range(max_retries):
            try:
                # proxy_ip_port = self.proxy_manager.get_random_proxy()
                proxy_ip_port = None
                if proxy_ip_port:
                    # proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
                    #     "user": 'A1LGFVXJ',
                    #     "password": 'DDC83DD434F5',
                    #     "server": proxy_ip_port,
                    # }
                    # proxy_server = {"http": proxyUrl}
                    proxy_server = {
                        "http": f"http://{proxy_ip_port}",
                        "https": f"http://{proxy_ip_port}"
                    }
                    print(f"[线程 {self.thread_id}] 使用代理: {proxy_ip_port}")
                else:
                    print(f"[线程 {self.thread_id}] 没有可用代理")
                    self.update_log.emit(f"[线程 {self.thread_id}] 没有可用代理")
                    proxy_server = None
                res = requests.get(
                    url,
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=timeout,
                    proxies=proxy_server
                )

                if res.status_code == 200:
                    return res
                elif res.status_code == 429:
                    print(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}\n")
                    self.update_log.emit(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}\n")
                else:
                    print(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}\n")
                    self.update_log.emit(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}\n")

            except Exception as e:
                print(f'[线程 {self.thread_id}] {url} 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次\n')
                self.update_log.emit(
                    f"[线程 {self.thread_id}] {url} 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次\n")

            # 重试前等待随机时间
            # time.sleep(random.uniform(1, 2))

        print(f"[线程 {self.thread_id}] 请求 {url} 失败，已重试 {max_retries} 次\n")
        self.update_log.emit(f"[线程 {self.thread_id}] 请求 {url} 失败，已重试 {max_retries} 次\n")
        return None

    def check_cookies_valid(self, page):
        h3_elements = page.xpath('//h3')
        return len(h3_elements) >= 1

    def search(self, keyword):
        # 搜索参数
        params = {
            'q': keyword,
            'oq': keyword,
            'gs_lcrp': '',
            'sourceid': 'chrome',
            'ie': 'UTF-8',
        }

        # 1. 检查cookies是否为空
        cookies = self.google_cookie.cookies
        if not cookies:
            print(f"[线程 {self.thread_id}] cookies为空，获取新cookies\n")
            self.update_log.emit(f"[线程 {self.thread_id}] cookies为空，获取新cookies\n")
            with self.lock:  # 使用线程锁保护cookie创建
                self.google_cookie.cookies = self.google_cookie.create_cookies()
                cookies = self.google_cookie.cookies

            # 如果获取cookies后仍为空，跳过此查询
            if not cookies:
                print(f"[线程 {self.thread_id}] 无法获取有效cookies，跳过查询: {keyword}\n")
                self.update_log.emit(f"[线程 {self.thread_id}] 无法获取有效cookies，跳过查询: {keyword}\n")
                self.failure_count += 1
                return None

        # 2. 发送请求
        response = self.send_request(
            url='https://www.google.com/search',
            params=params,
            cookies=cookies
        )

        # 3. 处理响应
        if not response:
            print(f"[线程 {self.thread_id}] 请求失败，跳过查询: {keyword}，当前失败次数: {self.failure_count + 1}\n")
            self.update_log.emit(f"[线程 {self.thread_id}] 请求失败，跳过查询: {keyword}，当前失败次数: {self.failure_count + 1}\n")
            self.failure_count += 1

            # 4. 检查是否需要更换cookies
            if self.failure_count >= self.max_failures:
                print(f"[线程 {self.thread_id}] 累计失败{self.max_failures}次，更换cookies\n")
                self.update_log.emit(f"[线程 {self.thread_id}] 累计失败{self.max_failures}次，更换cookies\n")
                with self.lock:  # 使用线程锁保护cookie创建
                    self.google_cookie.cookies = self.google_cookie.create_cookies()
                self.failure_count = 0  # 重置失败计数器

            return None

        # 5. 解析成功的响应
        try:
            page = etree.HTML(response.text)
            pages = 1
            pages_elem = page.xpath('//td[@class="NKTSme"][last()]/a/text()')
            if pages_elem:
                pages = int(''.join(pages_elem))
            print(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}\n')
            self.update_log.emit(f'[线程 {self.thread_id}] 查询语句：{keyword} 总页数: {pages}\n')
            return pages
        except Exception as e:
            print(f"[线程 {self.thread_id}] 解析响应失败: {keyword}, 错误: {e}\n")
            self.update_log.emit(f"[线程 {self.thread_id}] 解析响应失败: {keyword}, 错误: {e}\n")
            self.failure_count += 1

            # 检查是否需要更换cookies
            if self.failure_count >= self.max_failures:
                print(f"[线程 {self.thread_id}] 累计失败{self.max_failures}次，更换cookies\n")
                self.update_log.emit(f"[线程 {self.thread_id}] 累计失败{self.max_failures}次，更换cookies\n")
                with self.lock:  # 使用线程锁保护cookie创建
                    self.google_cookie.cookies = self.google_cookie.create_cookies()
                self.failure_count = 0  # 重置失败计数器

            return None


class GoogleSearchThreadManager:
    def __init__(self, num_threads=5, excel_file=None, update_log=None, browser_path=None):
        """
        初始化多线程搜索管理器

        Args:
            num_threads: 线程数
            excel_file: Excel文件路径，包含查询语句，同时也是结果文件
        """
        self.num_threads = num_threads
        self.excel_file = excel_file
        self.update_log = update_log
        self.result_lock = threading.Lock()
        self.threads = []
        self.thread_queries = []  # 每个线程负责的查询列表
        self.browser_path = browser_path

        # 进度跟踪
        self.total_queries = 0
        self.completed_queries = 0
        self.progress_callback = None

        # 初始化结果文件
        self.initialize_excel_file()

    def initialize_excel_file(self):
        """初始化Excel文件，确保有pages列"""
        if not self.excel_file or not os.path.exists(self.excel_file):
            raise FileNotFoundError(f"Excel文件不存在: {self.excel_file}")

        try:
            # 读取Excel文件
            self.results_df = pd.read_excel(self.excel_file)

            # 检查是否有pages列，如果没有则添加
            if 'pages' not in self.results_df.columns:
                self.results_df['pages'] = None
                # 保存回文件
                self.results_df.to_excel(self.excel_file, index=False)
                print(f"在Excel文件中添加了pages列")

            print(f"成功加载Excel文件: {self.excel_file}")

        except Exception as e:
            print(f"初始化Excel文件失败: {e}")
            raise

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def update_progress(self, thread_id, completed):
        """更新并报告当前进度"""
        with self.result_lock:
            self.completed_queries += completed
            if self.progress_callback and self.total_queries > 0:
                progress = (self.completed_queries / self.total_queries) * 100
                self.progress_callback(self.completed_queries, self.total_queries, progress)
            print(f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")
            self.update_log.emit(
                f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")

    def distribute_queries(self):
        """将查询分配给各个线程"""
        try:
            # 重新读取Excel文件，确保获取最新状态
            self.results_df = pd.read_excel(self.excel_file)
            print("总共有", len(self.results_df), "条数据")
            self.update_log.emit("总共有" + str(len(self.results_df)) + "条数据")
            # 获取所有未处理的查询
            unprocessed_queries = []
            for index, row in self.results_df.iterrows():
                query = row['query']
                pages = row['pages']
                if pd.isna(pages):  # 只添加未处理的查询
                    unprocessed_queries.append((index, query))

            self.total_queries = len(unprocessed_queries)
            print(f"共有 {self.total_queries} 个查询需要处理")
            self.update_log.emit(f"共有 {self.total_queries} 个查询需要处理")

            if self.total_queries == 0:
                return []

            # 将查询平均分配给各个线程
            self.thread_queries = [[] for _ in range(self.num_threads)]
            for i, (index, query) in enumerate(unprocessed_queries):
                thread_idx = i % self.num_threads
                self.thread_queries[thread_idx].append((index, query))

            # 打印每个线程分配的查询数量
            for i, queries in enumerate(self.thread_queries):
                print(f"线程 {i} 分配了 {len(queries)} 个查询")
                self.update_log.emit(f"线程 {i} 分配了 {len(queries)} 个查询")

            return self.thread_queries
        except Exception as e:
            print(f"分配查询失败: {e}")
            self.update_log.emit(f"分配查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def worker(self, thread_id, queries):
        """
        工作线程函数

        Args:
            thread_id: 线程ID
            queries: 该线程负责处理的查询列表，每个元素是(index, query)元组
        """
        if not queries:
            print(f"[线程 {thread_id}] 没有分配到查询任务")
            self.update_log.emit(f"[线程 {thread_id}] 没有分配到查询任务")
            return

        # 为每个线程创建一个GoogleSearch实例，使用唯一的cookie文件
        searcher = GoogleSearch(thread_id=thread_id, proxy_manager=ProxyManager(num=500), update_log=self.update_log)

        completed = 0
        local_results = {}  # 本地存储结果，减少文件访问 {index: pages}

        for index, query in queries:
            try:
                search_query = query

                # 执行搜索
                pages = searcher.search(search_query)

                # 存储结果到本地字典，失败的查询标记为-1
                if pages is None:
                    local_results[index] = -1  # 标记失败的查询
                    print(f"[线程 {thread_id}] 查询失败，标记为-1: {query}\n")
                    self.update_log.emit(f"[线程 {thread_id}] 查询失败，标记为-1: {query}\n")
                else:
                    local_results[index] = pages

                completed += 1

                # 每处理5个查询更新一次结果文件
                if completed % 5 == 0 or completed == len(queries):
                    self.update_results_file(local_results, thread_id)
                    local_results = {}  # 清空本地结果
                    self.update_progress(thread_id, 5 if completed % 5 == 0 else completed % 5)

                # 添加随机延迟，避免请求过于频繁
                # time.sleep(random.uniform(1, 2))

            except Exception as e:
                print(f"[线程 {thread_id}] 处理查询 {query} 时出错: {e}\n")
                self.update_log.emit(f"[线程 {thread_id}] 处理查询 {query} 时出错: {e}\n")
                # 即使出错也要标记为已处理，避免遗漏
                local_results[index] = -1
                completed += 1

        # 处理剩余的结果
        if local_results:
            self.update_results_file(local_results, thread_id)
            self.update_progress(thread_id, len(local_results))

        print(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")
        self.update_log.emit(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")

    def update_results_file(self, results_dict, thread_id):
        """
        更新结果文件中的特定查询结果

        Args:
            results_dict: 查询结果字典 {index: pages}
            thread_id: 线程ID
        """
        if not results_dict:
            return

        try:
            # 使用线程锁保护文件读写
            with self.result_lock:
                # 重新读取最新的Excel文件
                latest_df = pd.read_excel(self.excel_file)

                # 更新结果
                for index, pages in results_dict.items():
                    # 直接使用索引更新
                    latest_df.loc[index, 'pages'] = pages

                # 保存更新后的文件
                latest_df.to_excel(self.excel_file, index=False)

                # 更新内存中的DataFrame
                self.results_df = latest_df

            print(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到文件")
            self.update_log.emit(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到文件")
        except Exception as e:
            print(f"[线程 {thread_id}] 更新结果文件失败: {e}")
            self.update_log.emit(f"[线程 {thread_id}] 更新结果文件失败: {e}")
            import traceback
            traceback.print_exc()

    def start(self):
        """启动所有工作线程"""
        # 分配查询任务给各个线程
        thread_queries = self.distribute_queries()

        if not any(thread_queries):
            print("没有需要处理的新查询")
            return False

        # 创建并启动工作线程
        for i in range(self.num_threads):
            if i < len(thread_queries) and thread_queries[i]:
                thread = threading.Thread(target=self.worker, args=(i, thread_queries[i]))
                thread.daemon = True  # 设置为守护线程
                self.threads.append(thread)
                thread.start()
                print(f"启动线程 {i}")
                self.update_log.emit(f"启动线程 {i}")

        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        print("所有线程已完成!")
        self.update_log.emit("所有线程已完成!")

    def get_results(self):
        """获取搜索结果DataFrame"""
        # 重新读取Excel文件，确保获取最新结果
        try:
            return pd.read_excel(self.excel_file)
        except Exception as e:
            print(f"读取结果文件失败: {e}")
            return self.results_df


if __name__ == '__main__':
    # 配置参数（可以在这里直接修改，不需要命令行）
    excel_file = "测试.xlsx"  # Excel文件路径
    threads = 3  # 线程数量

    print(f"===== 谷歌搜索多线程工具 =====")
    print(f"Excel文件: {excel_file}")
    print(f"线程数量: {threads}")
    print("=" * 30)

    try:
        # 创建搜索管理器
        manager = GoogleSearchThreadManager(
            num_threads=threads,
            excel_file=excel_file
        )

        # 启动搜索
        print("开始分配查询任务...")
        started = manager.start()

        # 如果有任务需要执行
        if started:
            print("正在执行查询，请稍候...")
            manager.wait_completion()
            print("所有查询已完成!")
        else:
            print("没有需要处理的查询")

        print(f"\n处理完成: 共处理了 {manager.completed_queries} 个查询")
        print(f"结果已保存到 {excel_file}")

    except KeyboardInterrupt:
        print("\n程序被用户中断，当前结果已保存")
        print(f"您可以稍后重新运行程序继续执行")

    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback

        traceback.print_exc()

    # 程序结束前等待用户按键，方便查看输出
    input("\n按回车键退出...")
