('D:\\crawler\\email_page\\build\\GoogleSearchTool\\GoogleSearchTool.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('qt', 'D:\\crawler\\email_page\\email_page\\qt.py', 'PYSOURCE')],
 'python311.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
