!function(){"use strict";const t=new CSSStyleSheet;t.replaceSync('/*\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-size: 13px;\n  color: #222;\n}\n\nbody.platform-linux {\n  font-family: "Google Sans Text", "Google Sans", system-ui, sans-serif;\n}\n\nbody.platform-mac {\n  color: rgb(48 57 66);\n  font-family: system-ui, sans-serif;\n}\n\nbody.platform-windows {\n  font-family: system-ui, sans-serif;\n}\n\n.fill {\n  position: absolute;\n  inset: 0;\n}\n\n#canvas {\n  pointer-events: none;\n}\n\n.hidden {\n  display: none !important; /* stylelint-disable-line declaration-no-important */\n}\n');class n{viewportSize={width:800,height:600};viewportSizeForMediaQueries;deviceScaleFactor=1;emulationScaleFactor=1;pageScaleFactor=1;pageZoomFactor=1;scrollX=0;scrollY=0;style;canvas;canvasWidth=0;canvasHeight=0;platform;_window;_document;_context;_installed=!1;constructor(t,n=[]){this._window=t,this._document=t.document,Array.isArray(n)||(n=[n]),this.style=n}setCanvas(t){this.canvas=t,this._context=t.getContext("2d")}install(){for(const t of this.style)a(t);this._installed=!0}uninstall(){for(const t of this.style)document.adoptedStyleSheets=document.adoptedStyleSheets.filter((n=>n!==t));this._installed=!1}reset(t){t&&(this.viewportSize=t.viewportSize,this.viewportSizeForMediaQueries=t.viewportSizeForMediaQueries,this.deviceScaleFactor=t.deviceScaleFactor,this.pageScaleFactor=t.pageScaleFactor,this.pageZoomFactor=t.pageZoomFactor,this.emulationScaleFactor=t.emulationScaleFactor,this.scrollX=Math.round(t.scrollX),this.scrollY=Math.round(t.scrollY)),this.resetCanvas()}resetCanvas(){this.canvas&&this._context&&(this.canvas.width=this.deviceScaleFactor*this.viewportSize.width,this.canvas.height=this.deviceScaleFactor*this.viewportSize.height,this.canvas.style.width=this.viewportSize.width+"px",this.canvas.style.height=this.viewportSize.height+"px",this._context.scale(this.deviceScaleFactor,this.deviceScaleFactor),this.canvasWidth=this.viewportSize.width,this.canvasHeight=this.viewportSize.height)}setPlatform(t){this.platform=t,this.document.body.classList.add("platform-"+t),this._installed||this.install()}dispatch(t){this[t.shift()].apply(this,t)}eventHasCtrlOrMeta(t){return"mac"===this.platform?t.metaKey&&!t.ctrlKey:t.ctrlKey&&!t.metaKey}get context(){if(!this._context)throw new Error("Context object is missing");return this._context}get document(){if(!this._document)throw new Error("Document object is missing");return this._document}get window(){if(!this._window)throw new Error("Window object is missing");return this._window}get installed(){return this._installed}}function e(t,n,e){const o=i(n,e);return o.addEventListener("click",(function(t){t.stopPropagation()}),!1),t.appendChild(o),o}function o(t,n){const e=document.createTextNode(n);return t.appendChild(e),e}function i(t,n){const e=document.createElement(t);return n&&(e.className=n),e}function r(t,n){return t.length<=n?String(t):t.substr(0,n-1)+"…"}function s(t,n,e){return t<n?t=n:t>e&&(t=e),t}function a(t){document.adoptedStyleSheets=[...document.adoptedStyleSheets,t]}function l(t,n){const e=t[3];return[(1-e)*n[0]+e*t[0],(1-e)*n[1]+e*t[1],(1-e)*n[2]+e*t[2],e+n[3]*(1-e)]}function c([t,n,e]){const o=Math.max(t,n,e),i=Math.min(t,n,e),r=o-i;let s;return s=i===o?0:t===o?(1/6*(n-e)/r+1)%1:n===o?1/6*(e-t)/r+1/3:1/6*(t-n)/r+2/3,s}function d([t,n,e]){return.2126*(t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4))}const h=2.4,p=.56,u=.57,g=.65,m=.62,f=.022,x=1.414,b=1.14,y=1.14,v=.027,w=.1,A=5e-4;function M([t,n,e]){return.2126729*Math.pow(t,h)+.7151522*Math.pow(n,h)+.072175*Math.pow(e,h)}function C(t,n){return function(t,n){if(t=S(t),n=S(n),Math.abs(t-n)<A)return 0;let e=0;n>t?(e=(Math.pow(n,p)-Math.pow(t,u))*b,e=e<w?0:e-v):(e=(Math.pow(n,g)-Math.pow(t,m))*y,e=e>-w?0:e+v);return 100*e}(M(l(t,n)),M(n))}function S(t){return t>f?t:t+Math.pow(f-t,x)}const L=[[12,-1,-1,-1,-1,100,90,80,-1,-1],[14,-1,-1,-1,100,90,80,60,60,-1],[16,-1,-1,100,90,80,60,55,50,50],[18,-1,-1,90,80,60,55,50,40,40],[24,-1,100,80,60,55,50,40,38,35],[30,-1,90,70,55,50,40,38,35,40],[36,-1,80,60,50,40,38,35,30,25],[48,100,70,55,40,38,35,30,25,20],[60,90,60,50,38,35,30,25,20,20],[72,80,55,40,35,30,25,20,20,20],[96,70,50,35,30,25,20,20,20,20],[120,60,40,30,25,20,20,20,20,20]];L.reverse();const k={largeFont:{aa:3,aaa:4.5},normalFont:{aa:4.5,aaa:7}};function E(t,n){return function(t,n){const e=72*parseFloat(t.replace("px",""))/96;return(isNaN(Number(n))?["bold","bolder"].includes(n):Number(n)>=600)?e>=14:e>=18}(t,n)?k.largeFont:k.normalFont}function F(t,n,e,o=1){e&&e.color&&(t.save(),t.translate(.5,.5),t.lineWidth=o,"dashed"===e.pattern&&t.setLineDash([3,3]),"dotted"===e.pattern&&t.setLineDash([2,2]),t.strokeStyle=e.color,t.stroke(n),t.restore())}function P(t,n,e,o,i){i&&(t.save(),i.fillColor&&(t.fillStyle=i.fillColor,t.fill(n)),i.hatchColor&&U(t,n,e,10,i.hatchColor,o,!1),t.restore())}function H(t,n,e){let o=0;function i(i){const r=[];for(let s=0;s<i;++s){const i=Math.round(t[o++]*e);n.maxX=Math.max(n.maxX,i),n.minX=Math.min(n.minX,i);const s=Math.round(t[o++]*e);n.maxY=Math.max(n.maxY,s),n.minY=Math.min(n.minY,s),n.leftmostXForY[s]=Math.min(n.leftmostXForY[s]||Number.MAX_VALUE,i),n.rightmostXForY[s]=Math.max(n.rightmostXForY[s]||Number.MIN_VALUE,i),n.topmostYForX[i]=Math.min(n.topmostYForX[i]||Number.MAX_VALUE,s),n.bottommostYForX[i]=Math.max(n.bottommostYForX[i]||Number.MIN_VALUE,s),n.allPoints.push({x:i,y:s}),r.push(i,s)}return r}const r=t.length,s=new Path2D;for(;o<r;)switch(t[o++]){case"M":s.moveTo.apply(s,i(1));break;case"L":s.lineTo.apply(s,i(1));break;case"C":s.bezierCurveTo.apply(s,i(3));break;case"Q":s.quadraticCurveTo.apply(s,i(2));break;case"Z":s.closePath()}return s}function Y(){return{minX:Number.MAX_VALUE,minY:Number.MAX_VALUE,maxX:-Number.MAX_VALUE,maxY:-Number.MAX_VALUE,leftmostXForY:{},rightmostXForY:{},topmostYForX:{},bottommostYForX:{},allPoints:[]}}function z(t,n){let e=new DOMPoint(t.x,t.y);return e=e.matrixTransform(n),{x:e.x,y:e.y}}const B=5,W=3;let X,I="";function U(t,n,e,o,i,r,s){if((t.canvas.width<e.maxX-e.minX||t.canvas.height<e.maxY-e.minY)&&(e={minX:0,maxX:t.canvas.width,minY:0,maxY:t.canvas.height,allPoints:[]}),!X||i!==I){I=i;const n=document.createElement("canvas");n.width=o,n.height=B+W;const e=n.getContext("2d");e.clearRect(0,0,n.width,n.height),e.rect(0,0,1,B),e.fillStyle=i,e.fill(),X=t.createPattern(n,"repeat")}t.save();const a=new DOMMatrix;X.setTransform(a.scale(s?-1:1,1).rotate(0,0,-45+r)),t.fillStyle=X,t.fill(n),t.restore()}function D(t,n,e,o){let i=["M",t.p1.x,t.p1.y,"L",t.p2.x,t.p2.y,"L",t.p3.x,t.p3.y,"L",t.p4.x,t.p4.y];for(const e of n)i=[...i,"L",e.p4.x,e.p4.y,"L",e.p3.x,e.p3.y,"L",e.p2.x,e.p2.y,"L",e.p1.x,e.p1.y,"L",e.p4.x,e.p4.y,"L",t.p4.x,t.p4.y];return i.push("Z"),H(i,e,o)}function T(t){return(t.match(/#(\w\w)(\w\w)(\w\w)(\w\w)/)||[]).slice(1).map((t=>parseInt(t,16)/255))}function R(t,n){if("rgb"===n){const[n,e,o,i]=t;return`rgb(${(255*n).toFixed()} ${(255*e).toFixed()} ${(255*o).toFixed()}${1===i?"":" / "+Math.round(100*i)/100})`}if("hsl"===n){const[n,e,o,i]=function([t,n,e,o]){const i=Math.max(t,n,e),r=Math.min(t,n,e),s=i-r,a=i+r,l=.5*a;let d;return d=0===l||1===l?0:l<=.5?s/a:s/(2-a),[c([t,n,e]),d,l,o]}(t);return`hsl(${Math.round(360*n)}deg ${Math.round(100*e)} ${Math.round(100*o)}${1===i?"":" / "+Math.round(100*(i??1))/100})`}if("hwb"===n){const[n,e,o,i]=function([t,n,e,o]){const i=c([t,n,e]),r=Math.max(t,n,e);return[i,Math.min(t,n,e),1-r,o]}(t);return`hwb(${Math.round(360*n)}deg ${Math.round(100*e)} ${Math.round(100*o)}${1===i?"":" / "+Math.round(100*(i??1))/100})`}throw new Error("NOT_REACHED")}function O(t,n,e,o,i,r,s){t.save();const a=H(n,r,s);return e&&(t.fillStyle=e,t.fill(a)),o&&("dashed"===i&&t.setLineDash([3,3]),"dotted"===i&&t.setLineDash([2,2]),t.lineWidth=2,t.strokeStyle=o,t.stroke(a)),t.restore(),a}const Q=3,N=20,J=20,K=3,G="#1A73E8",Z="#121212";function V(t,n,o,i,r,s,a=new DOMMatrix){const l=`grid-${r.gridLayerCounter++}-labels`;let c=document.getElementById(l);if(!c){const t=document.getElementById("grid-label-container");if(!t)throw new Error("#grid-label-container is not found");c=e(t,"div"),c.id=l}const d=t.gridHighlightConfig&&t.gridHighlightConfig.rowLineColor?t.gridHighlightConfig.rowLineColor:G,h=gt(d);c.style.setProperty("--row-label-color",d),c.style.setProperty("--row-label-text-color",h);const p=t.gridHighlightConfig&&t.gridHighlightConfig.columnLineColor?t.gridHighlightConfig.columnLineColor:G,u=gt(p);c.style.setProperty("--column-label-color",p),c.style.setProperty("--column-label-text-color",u),c.innerText="";const g=e(c,"div","area-names"),m=e(c,"div","line-names"),f=e(c,"div","line-numbers"),x=e(c,"div","track-sizes"),b=function(t,n){const e=Math.round(n.maxX-n.minX),o=Math.round(n.maxY-n.minY),i={rows:{positive:{positions:[],hasFirst:!1,hasLast:!1},negative:{positions:[],hasFirst:!1,hasLast:!1}},columns:{positive:{positions:[],hasFirst:!1,hasLast:!1},negative:{positions:[],hasFirst:!1,hasLast:!1}},bounds:{minX:Math.round(n.minX),maxX:Math.round(n.maxX),minY:Math.round(n.minY),maxY:Math.round(n.maxY),allPoints:n.allPoints,width:e,height:o}};if(t.gridHighlightConfig&&t.gridHighlightConfig.showLineNames){const n=_(t.rowLineNameOffsets||[]),e={positions:n.positions,names:n.names,hasFirst:!!n.positions.length&&$(n.positions).y===i.bounds.minY,hasLast:!!n.positions.length&&q(n.positions).y===i.bounds.maxY};i.rows.positive=e;const o=_(t.columnLineNameOffsets||[]),r={positions:o.positions,names:o.names,hasFirst:!!o.positions.length&&$(o.positions).x===i.bounds.minX,hasLast:!!o.positions.length&&q(o.positions).x===i.bounds.maxX};i.columns.positive=r}else{const n=({x:t,y:n})=>({x:Math.round(t),y:Math.round(n)});t.positiveRowLineNumberPositions&&(i.rows.positive={positions:t.positiveRowLineNumberPositions.map(n),hasFirst:Math.round($(t.positiveRowLineNumberPositions).y)===i.bounds.minY,hasLast:Math.round(q(t.positiveRowLineNumberPositions).y)===i.bounds.maxY}),t.negativeRowLineNumberPositions&&(i.rows.negative={positions:t.negativeRowLineNumberPositions.map(n),hasFirst:Math.round($(t.negativeRowLineNumberPositions).y)===i.bounds.minY,hasLast:Math.round(q(t.negativeRowLineNumberPositions).y)===i.bounds.maxY}),t.positiveColumnLineNumberPositions&&(i.columns.positive={positions:t.positiveColumnLineNumberPositions.map(n),hasFirst:Math.round($(t.positiveColumnLineNumberPositions).x)===i.bounds.minX,hasLast:Math.round(q(t.positiveColumnLineNumberPositions).x)===i.bounds.maxX}),t.negativeColumnLineNumberPositions&&(i.columns.negative={positions:t.negativeColumnLineNumberPositions.map(n),hasFirst:Math.round($(t.negativeColumnLineNumberPositions).x)===i.bounds.minX,hasLast:Math.round(q(t.negativeColumnLineNumberPositions).x)===i.bounds.maxX})}return i}(t,n);t.gridHighlightConfig&&t.gridHighlightConfig.showLineNames?function(t,n,e,o,i=new DOMMatrix,r="horizontal-tb"){for(const[s,a]of n.columns.positive.positions.entries()){lt(et(t,nt(n.columns.positive.names[s]),"column"),z(a,i),n,r,e,o)}for(const[s,a]of n.rows.positive.positions.entries()){st(et(t,nt(n.rows.positive.names[s]),"row"),z(a,i),n,r,e,o)}}(m,b,i,s,a,t.writingMode):function(t,n,e,o,i=new DOMMatrix,r="horizontal-tb"){if(!n.columns.positive.names)for(const[s,a]of j(n.columns.positive.positions,"x")){lt(et(t,(s+1).toString(),"column"),z(a,i),n,r,e,o)}if(!n.rows.positive.names)for(const[s,a]of j(n.rows.positive.positions,"y")){st(et(t,(s+1).toString(),"row"),z(a,i),n,r,e,o)}for(const[s,a]of j(n.columns.negative.positions,"x")){ct(et(t,(-1*n.columns.negative.positions.length+s).toString(),"column"),z(a,i),n,r,e,o)}for(const[s,a]of j(n.rows.negative.positions,"y")){at(et(t,(-1*n.rows.negative.positions.length+s).toString(),"row"),z(a,i),n,r,e,o)}}(f,b,i,s,a,t.writingMode),function(t,n,e=new DOMMatrix,o="horizontal-tb"){for(const{name:i,bounds:r}of n){const n=et(t,i,"row"),{width:s,height:a}=ht(n,o),l=z("vertical-rl"===o||"sideways-rl"===o?r.allPoints[3]:"sideways-lr"===o?r.allPoints[1]:r.allPoints[0],e),c=r.allPoints[1].x<r.allPoints[0].x,d=r.allPoints[3].y<r.allPoints[0].y;n.style.left=l.x-(c?s:0)+"px",n.style.top=l.y-(d?a:0)+"px"}}(g,o,a,t.writingMode),t.columnTrackSizes&&tt(x,t.columnTrackSizes,"column",i,s,a,t.writingMode),t.rowTrackSizes&&tt(x,t.rowTrackSizes,"row",i,s,a,t.writingMode)}function*j(t,n){let e=null;for(const[o,i]of t.entries()){const r=0===o,s=o===t.length-1,a=Math.abs(i[n]-(e?e[n]:0))>J,l=!s&&Math.abs(t[t.length-1][n]-i[n])>J;(r||s||a&&l)&&(yield[o,i],e=i)}}const q=t=>t[t.length-1],$=t=>t[0];function _(t){const n=[],e=[];for(const{name:o,x:i,y:r}of t){const t=Math.round(i),s=Math.round(r),a=n.findIndex((({x:n,y:e})=>n===t&&e===s));a>-1?e[a].push(o):(n.push({x:t,y:s}),e.push([o]))}return{positions:n,names:e}}function tt(t,n,e,o,i,r=new DOMMatrix,s="horizontal-tb"){const{main:a,cross:l}=it(s),{crossSize:c}=rt(s,o);for(const{x:o,y:d,computedSize:h,authoredSize:p}of n){const n=z({x:o,y:d},r),u=h.toFixed(2),g=et(t,`${p?p+"·":""}${`${u.endsWith(".00")?u.slice(0,-3):u}px`}`,e),m=ht(g,s);let f=n[a]-m.mainSize<N;"column"===e&&(f="vertical-rl"===s?c-n[l]-m.crossSize<N:n[l]-m.crossSize<N);let x=ut("column"===e?"bottom-mid":"right-mid",s);x=pt(x,f),dt(g,x,n.x,n.y,m,i)}}function nt(t){const n=document.createElement("ul"),o=t.slice(0,K);for(const t of o)e(n,"li","line-name").textContent=t;return n}function et(t,n,o){const i=e(t,"div"),r=e(i,"div","grid-label-content");return r.dataset.direction=o,"string"==typeof n?r.textContent=n:r.appendChild(n),r}function ot(t,n,e){const[o,i,r,s]=t.allPoints;return"row"===n?"positive"===e?{start:o,end:s}:{start:i,end:r}:"positive"===e?{start:o,end:i}:{start:s,end:r}}function it(t){return mt(t)?{main:"x",cross:"y"}:{main:"y",cross:"x"}}function rt(t,n){return mt(t)?{mainSize:n.canvasWidth,crossSize:n.canvasHeight}:{mainSize:n.canvasHeight,crossSize:n.canvasWidth}}function st(t,n,e,o,i,r){const{start:s,end:a}=ot(e.bounds,"row","positive"),{main:l,cross:c}=it(o),{crossSize:d}=rt(o,i),h=ht(t,o),p=n[c]===s[c]&&e.columns&&e.columns.positive.hasFirst,u=n[c]===a[c]&&e.columns&&e.columns.negative.hasFirst,g=n[c]<N,m=d-n[c]<N,f=n[l]-h.mainSize<N;f&&(p||u)&&t.classList.add("inner-shared-corner");let x=ut("right-mid",o);g||p?x=ut("right-top",o):(m||u)&&(x=ut("right-bottom",o)),x=pt(x,f),dt(t,x,n.x,n.y,h,r)}function at(t,n,e,o,i,r){const{start:s,end:a}=ot(e.bounds,"row","negative"),{main:l,cross:c}=it(o),{mainSize:d,crossSize:h}=rt(o,i),p=ht(t,o),u=n[c]===s[c]&&e.columns&&e.columns.positive.hasLast,g=n[c]===a[c]&&e.columns&&e.columns.negative.hasLast,m=n[c]<N,f=h-n[c]<N,x=d-n[l]-p.mainSize<N;x&&(u||g)&&t.classList.add("inner-shared-corner");let b=ut("left-mid",o);m||u?b=ut("left-top",o):(f||g)&&(b=ut("left-bottom",o)),b=pt(b,x),dt(t,b,n.x,n.y,p,r)}function lt(t,n,e,o,i,r){const{start:s,end:a}=ot(e.bounds,"column","positive"),{main:l,cross:c}=it(o),{mainSize:d,crossSize:h}=rt(o,i),p=ht(t,o),u=n[l]===s[l]&&e.rows&&e.rows.positive.hasFirst,g=n[l]===a[l]&&e.rows&&e.rows.negative.hasFirst,m=n[l]<N,f=d-n[l]<N,x=ft(o)?h-n[c]-p.crossSize<N:n[c]-p.crossSize<N;x&&(u||g)&&t.classList.add("inner-shared-corner");let b=ut("bottom-mid",o);m?b=ut("bottom-left",o):f&&(b=ut("bottom-right",o)),b=pt(b,x),dt(t,b,n.x,n.y,p,r)}function ct(t,n,e,o,i,r){const{start:s,end:a}=ot(e.bounds,"column","negative"),{main:l,cross:c}=it(o),{mainSize:d,crossSize:h}=rt(o,i),p=ht(t,o),u=n[l]===s[l]&&e.rows&&e.rows.positive.hasLast,g=n[l]===a[l]&&e.rows&&e.rows.negative.hasLast,m=n[l]<N,f=d-n[l]<N,x=ft(o)?n[c]-p.crossSize<N:h-n[c]-p.crossSize<N;x&&(u||g)&&t.classList.add("inner-shared-corner");let b=ut("top-mid",o);m?b=ut("top-left",o):f&&(b=ut("top-right",o)),b=pt(b,x),dt(t,b,n.x,n.y,p,r)}function dt(t,n,e,o,i,r){const{contentLeft:s,contentTop:a}=function(t,n,e,o,i,r){let s=0,a=0;switch(n*=r,e*=r,t){case"left-top":s=e,a=n+Q;break;case"left-mid":s=e-i/2,a=n+Q;break;case"left-bottom":s=e-i,a=n+Q;break;case"right-top":s=e,a=n-Q-o;break;case"right-mid":s=e-i/2,a=n-Q-o;break;case"right-bottom":s=e-i,a=n-o-Q;break;case"top-left":s=e+Q,a=n;break;case"top-mid":s=e+Q,a=n-o/2;break;case"top-right":s=e+Q,a=n-o;break;case"bottom-left":s=e-Q-i,a=n;break;case"bottom-mid":s=e-Q-i,a=n-o/2;break;case"bottom-right":s=e-Q-i,a=n-o}return{contentTop:s,contentLeft:a}}(n,e,o,i.width,i.height,r);t.classList.add(n),t.style.left=s+"px",t.style.top=a+"px"}function ht(t,n){const e=function(t){let n=t.getBoundingClientRect().width;n%2==1&&(n+=1,t.style.width=n+"px");return n}(t),o=t.getBoundingClientRect().height,i=mt(n);return{width:e,height:o,mainSize:i?e:o,crossSize:i?o:e}}function pt(t,n){if(!n)return t;switch(t){case"left-top":return"right-top";case"left-mid":return"right-mid";case"left-bottom":return"right-bottom";case"right-top":return"left-top";case"right-mid":return"left-mid";case"right-bottom":return"left-bottom";case"top-left":return"bottom-left";case"top-mid":return"bottom-mid";case"top-right":return"bottom-right";case"bottom-left":return"top-left";case"bottom-mid":return"top-mid";case"bottom-right":return"top-right"}return t}function ut(t,n){if("vertical-lr"===n)switch(t){case"left-top":return"top-left";case"left-mid":return"top-mid";case"left-bottom":return"top-right";case"top-left":return"left-top";case"top-mid":return"left-mid";case"top-right":return"left-bottom";case"right-top":return"bottom-right";case"right-mid":return"bottom-mid";case"right-bottom":return"bottom-left";case"bottom-left":return"right-top";case"bottom-mid":return"right-mid";case"bottom-right":return"right-bottom"}if("vertical-rl"===n||"sideways-rl"===n)switch(t){case"left-top":return"top-right";case"left-mid":return"top-mid";case"left-bottom":return"top-left";case"top-left":return"right-top";case"top-mid":return"right-mid";case"top-right":return"right-bottom";case"right-top":return"bottom-right";case"right-mid":return"bottom-mid";case"right-bottom":return"bottom-left";case"bottom-left":return"left-top";case"bottom-mid":return"left-mid";case"bottom-right":return"left-bottom"}if("sideways-lr"===n)switch(t){case"left-top":return"bottom-left";case"left-mid":return"bottom-mid";case"left-bottom":return"bottom-right";case"top-left":return"left-bottom";case"top-mid":return"left-mid";case"top-right":return"left-top";case"right-top":return"top-left";case"right-mid":return"top-mid";case"right-bottom":return"top-right";case"bottom-left":return"right-bottom";case"bottom-mid":return"right-mid";case"bottom-right":return"right-top"}return t}function gt(t){let n=[];const e=T(t+"00");if(4===e.length)n=e.slice(0,3).map((t=>t));else{const e=t.match(/[0-9.]+/g);if(!e)return null;n=e.slice(0,3).map((t=>parseInt(t,10)/255))}return n.length?d(n)>.2?Z:"white":null}function mt(t){return t.startsWith("horizontal")}function ft(t){return"vertical-rl"===t||"sideways-rl"===t}function xt(t,n,e,o,i,r,s){const a=Y(),l=H(t.gridBorder,a,r);n.save(),function(t,n,e){if(mt(t))return;const o=n.allPoints[0],i=n.allPoints[1],r=n.allPoints[3];e.translate(o.x,o.y),("vertical-rl"===t||"sideways-rl"===t)&&(e.rotate(90*Math.PI/180),e.translate(0,-1*(r.y-o.y)));"vertical-lr"===t&&(e.rotate(90*Math.PI/180),e.scale(1,-1));"sideways-lr"===t&&(e.rotate(-90*Math.PI/180),e.translate(-1*(i.x-o.x),0));e.translate(-1*o.x,-1*o.y)}(t.writingMode,a,n),t.gridHighlightConfig.gridBackgroundColor&&(n.fillStyle=t.gridHighlightConfig.gridBackgroundColor,n.fill(l)),t.gridHighlightConfig.gridBorderColor&&(n.save(),n.translate(.5,.5),n.lineWidth=0,t.gridHighlightConfig.gridBorderDash&&n.setLineDash([3,3]),n.strokeStyle=t.gridHighlightConfig.gridBorderColor,n.stroke(l),n.restore());const c=bt(n,t,"row",r),d=bt(n,t,"column",r);vt(n,t.rowGaps,t.gridHighlightConfig.rowGapColor,t.gridHighlightConfig.rowHatchColor,t.rotationAngle,r,!0),vt(n,t.columnGaps,t.gridHighlightConfig.columnGapColor,t.gridHighlightConfig.columnHatchColor,t.rotationAngle,r,!1);const h=function(t,n,e,o){if(!n||!Object.keys(n).length)return[];t.save(),e&&(t.strokeStyle=e);t.lineWidth=2;const i=[];for(const e in n){const r=n[e],s=Y(),a=H(r,s,o);t.stroke(a),i.push({name:e,bounds:s})}return t.restore(),i}(n,t.areaNames,t.gridHighlightConfig.areaBorderColor,r),p=n.getTransform();p.scaleSelf(1/e),n.restore(),t.gridHighlightConfig.showGridExtensionLines&&(c&&yt(n,c,t.gridHighlightConfig.rowLineColor,t.gridHighlightConfig.rowLineDash,p,o,i),d&&yt(n,d,t.gridHighlightConfig.columnLineColor,t.gridHighlightConfig.columnLineDash,p,o,i)),V(t,a,h,{canvasWidth:o,canvasHeight:i},s,r,p)}function bt(t,n,e,o){const i=n[`${e}s`],r=n.gridHighlightConfig[`${e}LineColor`],s=n.gridHighlightConfig[`${e}LineDash`];if(!r)return null;const a=Y(),l=H(i,a,o);return t.save(),t.translate(.5,.5),s&&t.setLineDash([3,3]),t.lineWidth=0,t.strokeStyle=r,t.save(),t.stroke(l),t.restore(),t.restore(),a}function yt(t,n,e,o,i,r,s){t.save(),t.strokeStyle=e,t.lineWidth=1,t.translate(.5,.5),o&&t.setLineDash([3,3]);for(let e=0;e<n.allPoints.length;e+=2){let o,a,l=z(n.allPoints[e],i),c=z(n.allPoints[e+1],i);if(l.x===c.x)o={x:l.x,y:0},a={x:l.x,y:s},c.y<l.y&&([l,c]=[c,l]);else if(l.y===c.y)o={x:0,y:l.y},a={x:r,y:l.y},c.x<l.x&&([l,c]=[c,l]);else{const t=(c.y-l.y)/(c.x-l.x),n=(l.y*c.x-c.y*l.x)/(c.x-l.x);o={x:0,y:n},a={x:r,y:r*t+n},c.x<l.x&&([l,c]=[c,l])}t.beginPath(),t.moveTo(o.x,o.y),t.lineTo(l.x,l.y),t.moveTo(c.x,c.y),t.lineTo(a.x,a.y),t.stroke()}t.restore()}function vt(t,n,e,o,i,r,s){if(!e&&!o)return;t.save(),t.translate(.5,.5),t.lineWidth=0;const a=Y(),l=H(n,a,r);e&&(t.fillStyle=e,t.fill(l)),o&&U(t,l,a,10,o,i,s),t.restore()}const wt=new CSSStyleSheet;wt.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n@media (forced-colors: active) {\n  :root,\n  body {\n    background-color: transparent;\n    forced-color-adjust: none;\n  }\n}\n");const At=new CSSStyleSheet;function Mt(t,n,e){const o=t.containerQueryContainerHighlightConfig,i=Y();if(F(n,H(t.containerBorder,i,e),o.containerBorder,2),t.queryingDescendants)for(const i of t.queryingDescendants){const t=Y();F(n,H(i.descendantBorder,t,e),o.descendantBorder)}}At.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\nbody {\n  --arrow-width: 15px;\n  --arrow-height: 8px;\n  --shadow-up: 5px;\n  --shadow-down: -5px;\n  --shadow-direction: var(--shadow-up);\n  --arrow-down: polygon(0 0, 100% 0, 50% 100%);\n  --arrow-up: polygon(50% 0, 0 100%, 100% 100%);\n}\n\n.px {\n  color: rgb(128 128 128);\n}\n\n#element-title {\n  position: absolute;\n  z-index: 10;\n}\n/* Material */\n\n.tooltip-content {\n  position: absolute;\n  user-select: none;\n  background-color: #fff;\n  padding: 5px 8px;\n  border: 1px solid #fff;\n  border-radius: 3px;\n  box-sizing: border-box;\n  min-width: 100px;\n  max-width: min(300px, 100% - 4px);\n  z-index: 2;\n  background-clip: padding-box;\n  will-change: transform;\n  text-rendering: optimizelegibility;\n  pointer-events: none;\n  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 35%));\n}\n\n.tooltip-content::after {\n  content: "";\n  background: #fff;\n  width: var(--arrow-width);\n  height: var(--arrow-height);\n  clip-path: var(--arrow);\n  position: absolute;\n  top: var(--arrow-top);\n  left: var(--arrow-left);\n  visibility: var(--arrow-visibility);\n}\n\n.element-info-section {\n  margin-top: 12px;\n  margin-bottom: 6px;\n}\n\n.section-name {\n  color: #333;\n  font-weight: 500;\n  font-size: 10px;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  line-height: 12px;\n}\n\n.element-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.element-info-header {\n  display: flex;\n  align-items: center;\n}\n\n.element-info-body {\n  display: flex;\n  flex-direction: column;\n  padding-top: 2px;\n  margin-top: 2px;\n}\n\n.element-info-row {\n  display: flex;\n  line-height: 19px;\n}\n\n.separator-container {\n  display: flex;\n  align-items: center;\n  flex: auto;\n  margin-left: 7px;\n}\n\n.separator {\n  border-top: 1px solid #ddd;\n  width: 100%;\n}\n\n.element-info-name {\n  flex-shrink: 0;\n  color: #666;\n}\n\n.element-info-gap {\n  flex: auto;\n}\n\n.element-info-value-color {\n  display: flex;\n  color: rgb(48 57 66);\n  margin-left: 10px;\n  align-items: baseline;\n}\n\n.a11y-icon {\n  width: 16px;\n  height: 16px;\n  background-repeat: no-repeat;\n  display: inline-block;\n}\n\n.element-info-value-contrast {\n  display: flex;\n  align-items: center;\n  text-align: right;\n  color: rgb(48 57 66);\n  margin-left: 10px;\n}\n\n.element-info-value-contrast .a11y-icon {\n  margin-left: 8px;\n}\n\n.element-info-value-icon {\n  display: flex;\n  align-items: center;\n}\n\n.element-info-value-text {\n  text-align: right;\n  color: rgb(48 57 66);\n  margin-left: 10px;\n  align-items: baseline;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.color-swatch {\n  display: flex;\n  margin-right: 2px;\n  width: 10px;\n  height: 10px;\n  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==");\n  line-height: 10px;\n}\n\n.color-swatch-inner {\n  flex: auto;\n  border: 1px solid rgb(128 128 128 / 60%);\n}\n\n.element-layout-type {\n  margin-right: 10px;\n  width: 16px;\n  height: 16px;\n}\n\n.element-layout-type.grid {\n  background-image: url(\'data:image/svg+xml,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="2.5" y="2.5" width="4" height="4" stroke="%231A73E8"/><rect x="9.5" y="2.5" width="4" height="4" stroke="%231A73E8"/><rect x="9.5" y="9.5" width="4" height="4" stroke="%231A73E8"/><rect x="2.5" y="9.5" width="4" height="4" stroke="%231A73E8"/></svg>\');\n}\n\n.element-layout-type.flex {\n  background-image: url(\'data:image/svg+xml,<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 3.5h8v3H1v-3zm-1 0a1 1 0 011-1h8a1 1 0 011 1v3a1 1 0 01-1 1H1a1 1 0 01-1-1v-3zm12 0h3v3h-3v-3zm-1 0a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-3zm-7 6H1v3h3v-3zm-3-1a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1v-3a1 1 0 00-1-1H1zm6 4v-3h8v3H7zm-1-3a1 1 0 011-1h8a1 1 0 011 1v3a1 1 0 01-1 1H7a1 1 0 01-1-1v-3z" fill="%231A73E8"/></svg>\');\n}\n\n.element-description {\n  flex: 1 1;\n  font-weight: bold;\n  word-wrap: break-word;\n  word-break: break-all;\n}\n\n.dimensions {\n  color: var(--sys-color-outline);\n  text-align: right;\n  margin-left: 10px;\n}\n\n.material-node-width {\n  margin-right: 2px;\n}\n\n.material-node-height {\n  margin-left: 2px;\n}\n\n.material-tag-name {\n  /* Keep this in sync with inspectorSyntaxHighlight.css (--override-dom-tag-name-color) */\n  color: rgb(136 18 128);\n}\n\n.material-class-name,\n.material-node-id {\n  /* Keep this in sync with inspectorSyntaxHighlight.css (.webkit-html-attribute-value) */\n  color: rgb(26 26 166);\n}\n\n.contrast-text {\n  width: 16px;\n  height: 16px;\n  text-align: center;\n  line-height: 16px;\n  margin-right: 8px;\n  border: 1px solid rgb(0 0 0 / 10%);\n  padding: 0 1px;\n}\n\n.a11y-icon-not-ok {\n  background-image: url(\'data:image/svg+xml,<svg fill="none" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><path d="m9 1.5c-4.14 0-7.5 3.36-7.5 7.5s3.36 7.5 7.5 7.5 7.5-3.36 7.5-7.5-3.36-7.5-7.5-7.5zm0 13.5c-3.315 0-6-2.685-6-6 0-1.3875.4725-2.6625 1.2675-3.675l8.4075 8.4075c-1.0125.795-2.2875 1.2675-3.675 1.2675zm4.7325-2.325-8.4075-8.4075c1.0125-.795 2.2875-1.2675 3.675-1.2675 3.315 0 6 2.685 6 6 0 1.3875-.4725 2.6625-1.2675 3.675z" fill="%239e9e9e"/></svg>\');\n}\n\n.a11y-icon-warning {\n  background-image: url(\'data:image/svg+xml,<svg fill="none" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><path d="m8.25 11.25h1.5v1.5h-1.5zm0-6h1.5v4.5h-1.5zm.7425-3.75c-4.14 0-7.4925 3.36-7.4925 7.5s3.3525 7.5 7.4925 7.5c4.1475 0 7.5075-3.36 7.5075-7.5s-3.36-7.5-7.5075-7.5zm.0075 13.5c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6z" fill="%23e37400"/></svg>\');\n}\n\n.a11y-icon-ok {\n  background-image: url(\'data:image/svg+xml,<svg fill="none" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><path d="m9 1.5c-4.14 0-7.5 3.36-7.5 7.5s3.36 7.5 7.5 7.5 7.5-3.36 7.5-7.5-3.36-7.5-7.5-7.5zm0 13.5c-3.3075 0-6-2.6925-6-6s2.6925-6 6-6 6 2.6925 6 6-2.6925 6-6 6zm-1.5-4.35-1.95-1.95-1.05 1.05 3 3 6-6-1.05-1.05z" fill="%230ca40c"/></svg>\');\n}\n\n@media (forced-colors: active) {\n  :root,\n  body {\n    background-color: transparent;\n    forced-color-adjust: none;\n  }\n\n  .tooltip-content {\n    border-color: Highlight;\n    background-color: canvas;\n    color: text;\n    forced-color-adjust: none;\n  }\n\n  .tooltip-content::after {\n    background-color: Highlight;\n  }\n\n  .color-swatch-inner,\n  .contrast-text,\n  .separator {\n    border-color: Highlight;\n  }\n\n  .section-name {\n    color: Highlight;\n  }\n\n  .dimensions,\n  .element-info-name,\n  .element-info-value-color,\n  .element-info-value-contrast,\n  .element-info-value-icon,\n  .element-info-value-text,\n  .material-tag-name,\n  .material-class-name,\n  .material-node-id {\n    color: canvastext;\n  }\n}\n');const Ct=2,St=5,Lt=5,kt=6,Et=11,Ft=2,Pt=1,Ht=5;function Yt(t,n,e,o,i,r,s){const{baseSize:a,isHorizontalFlow:l}=t,c=Tt(n),d=l?{p1:c.p1,p2:Kt(c.p1,c.p2,a),p3:Kt(c.p4,c.p3,a),p4:c.p4}:{p1:c.p1,p2:c.p2,p3:Kt(c.p2,c.p3,a),p4:Kt(c.p1,c.p4,a)};!function(t,n,e,o,i){const r=t.flexItemHighlightConfig,s=Y(),a=H((c=e,["M",c.p1.x,c.p1.y,"L",c.p2.x,c.p2.y,"L",c.p3.x,c.p3.y,"L",c.p4.x,c.p4.y,"Z"]),s,i),l=Math.atan2(n.p4.y-n.p1.y,n.p4.x-n.p1.x)+45*Math.PI/180;var c;P(o,a,s,l,r.baseSizeBox),F(o,a,r.baseSizeBorder)}(t,c,d,e,s),function(t,n,e,o,i){const{isHorizontalFlow:r}=t,s=t.flexItemHighlightConfig;if(!s.flexibilityArrow)return;const a=r?{x:(e.p2.x+e.p3.x)/2,y:(e.p2.y+e.p3.y)/2}:{x:(e.p4.x+e.p3.x)/2,y:(e.p4.y+e.p3.y)/2},l=r?{x:(n.p2.x+n.p3.x)/2,y:(n.p2.y+n.p3.y)/2}:{x:(n.p4.x+n.p3.x)/2,y:(n.p4.y+n.p3.y)/2};if(l.x===a.x&&l.y===a.y)return;const c=Dt([a,l]);if(F(o,H(c,Y(),i),s.flexibilityArrow,Pt),!s.flexibilityArrow.color)return;const d=H(["M",l.x-Ht,l.y-Ht,"L",l.x,l.y,"L",l.x-Ht,l.y+Ht],Y(),i),h=Math.atan2(l.y-a.y,l.x-a.x);o.save(),o.translate(l.x+.5,l.y+.5),o.rotate(h),o.translate(-l.x-.5,-l.y-.5),F(o,d,s.flexibilityArrow,Pt),o.restore()}(t,c,d,e,s)}function zt(t,n,e,o,i,r){const s=t.flexContainerHighlightConfig,a=Y(),l=H(t.containerBorder,a,r),{isHorizontalFlow:c,isReverse:d,lines:h}=t;if(F(n,l,s.containerBorder),!h||!h.length)return;const p=function(t,n,e,o){const i=Tt(t),r=[];for(const t of n){if(!t.length)continue;let s=Tt(t[0].itemBorder);const a=[];for(const{itemBorder:n}of t){const t=Tt(n);s=s?Rt(s,t,e,o):t,a.push(t)}const l=1===n.length?i:Ot(s,i,e),c=a.map((t=>Ot(t,l,!e)));r.push({quad:l,items:a,extendedItems:c})}return r}(t.containerBorder,h,c,d);!function(t,n,e,o,i){const r=t.flexContainerHighlightConfig,s=o.map(((t,n)=>{const e=o[n+1]&&o[n+1].quad;return{path:i?It(t.quad,e):Ut(t.quad,e),items:t.extendedItems.map(((n,e)=>{const o=t.extendedItems[e+1]&&t.extendedItems[e+1];return i?Ut(n,o):It(n,o)}))}})),a=s.length>1;for(const{path:t,items:o}of s){for(const t of o)F(n,H(t,Y(),e),r.itemSeparator);a&&F(n,H(t,Y(),e),r.lineSeparator)}}(t,n,r,p,c),function(t,n,e,o,i){const{isHorizontalFlow:r}=t,{mainDistributedSpace:s,crossDistributedSpace:a,rowGapSpace:l,columnGapSpace:c}=t.flexContainerHighlightConfig,d=r?c:l,h=r?l:c,p=s&&Boolean(s.fillColor||s.hatchColor),u=i.length>1&&a&&Boolean(a.fillColor||a.hatchColor),g=d&&Boolean(d.fillColor||d.hatchColor),m=i.length>1&&h&&Boolean(h.fillColor||h.hatchColor),f=s&&a&&d&&h&&s.fillColor===a.fillColor&&s.hatchColor===a.hatchColor&&s.fillColor===d.fillColor&&s.hatchColor===d.hatchColor&&s.fillColor===h.fillColor&&s.hatchColor===h.hatchColor,x=Tt(o);if(f){return void Wt(x,i.map((t=>t.extendedItems)).flat().map((t=>t)),s,n,e)}const b=function(t,n){const{crossGap:e,mainGap:o,isHorizontalFlow:i,isReverse:r}=t,s=[],a=[];if(e&&n.length>1)for(let t=0,o=t+1;t<n.length-1;t++,o=t+1){const r=n[t].quad,s=n[o].quad;a.push(Xt(r,s,e,i))}for(const{extendedItems:t}of n){const n=[];if(o)for(let e=0,s=e+1;e<t.length-1;e++,s=e+1){const a=t[e],l=t[s];n.push(Xt(a,l,o,!i,r))}s.push(n)}return{mainGaps:s,crossGaps:a}}(t,i);if(u){Wt(x,[...i.map((t=>t.quad)),...m?b.crossGaps:[]],a,n,e)}if(p)for(const[t,o]of i.entries()){const i=[...o.extendedItems,...g?b.mainGaps[t]:[]];Wt(o.quad,i,s,n,e)}if(m)for(const t of b.crossGaps)Wt(t,[],h,n,e);if(g)for(const t of b.mainGaps)for(const o of t)Wt(o,[],d,n,e)}(t,n,r,t.containerBorder,p),function(t,n,e,o,i){o.forEach((({quad:o,items:r},s)=>{!function(t,n,e,o,i,r){const{alignItemsStyle:s,isHorizontalFlow:a}=t,{crossAlignment:l}=t.flexContainerHighlightConfig;if(!l||!l.color)return;const c=[];switch(s){case"flex-start":c.push([a?o.p1:o.p4,a?o.p2:o.p1]);break;case"flex-end":c.push([a?o.p3:o.p2,a?o.p4:o.p3]);break;case"center":a?(c.push([{x:(o.p1.x+o.p4.x)/2,y:(o.p1.y+o.p4.y)/2},{x:(o.p2.x+o.p3.x)/2,y:(o.p2.y+o.p3.y)/2}]),c.push([{x:(o.p2.x+o.p3.x)/2,y:(o.p2.y+o.p3.y)/2},{x:(o.p1.x+o.p4.x)/2,y:(o.p1.y+o.p4.y)/2}])):(c.push([{x:(o.p1.x+o.p2.x)/2,y:(o.p1.y+o.p2.y)/2},{x:(o.p3.x+o.p4.x)/2,y:(o.p3.y+o.p4.y)/2}]),c.push([{x:(o.p3.x+o.p4.x)/2,y:(o.p3.y+o.p4.y)/2},{x:(o.p1.x+o.p2.x)/2,y:(o.p1.y+o.p2.y)/2}]));break;case"stretch":case"normal":c.push([a?o.p1:o.p4,a?o.p2:o.p1]),c.push([a?o.p3:o.p2,a?o.p4:o.p3]);break;case"baseline":if(a){const t=i[0],n=Qt([t.p1,t.p2],[o.p2,o.p3]),e=Qt([t.p1,t.p2],[o.p1,o.p4]),s=r[0],a=Math.atan2(t.p4.y-t.p1.y,t.p4.x-t.p1.x);c.push([{x:n.x+s*Math.cos(a),y:n.y+s*Math.sin(a)},{x:e.x+s*Math.cos(a),y:e.y+s*Math.sin(a)}])}}for(const o of c){F(n,H(Dt(o),Y(),e),l,Ct),Bt(t,n,e,o[0],o[1])}}(t,n,e,o,r,i[s])}))}(t,n,r,p,h.map((t=>t.map((t=>t.baseline)))))}function Bt(t,n,e,o,i){const{crossAlignment:r}=t.flexContainerHighlightConfig;if(!r||!r.color)return;const s=Math.atan2(i.y-o.y,i.x-o.x),a={x:-Ft*Math.cos(s-.5*Math.PI)+(o.x+i.x)/2,y:-Ft*Math.sin(s-.5*Math.PI)+(o.y+i.y)/2},l=H(["M",a.x,a.y,"L",a.x+Et/2,a.y+kt,"L",a.x+Lt/2,a.y+kt,"L",a.x+Lt/2,a.y+kt+St,"L",a.x-Lt/2,a.y+kt+St,"L",a.x-Lt/2,a.y+kt,"L",a.x-Et/2,a.y+kt,"Z"],Y(),e);n.save(),n.translate(a.x,a.y),n.rotate(s),n.translate(-a.x,-a.y),n.fillStyle=r.color,n.fill(l),n.lineWidth=1,n.strokeStyle="white",n.stroke(l),n.restore()}function Wt(t,n,e,o,i){if(e){if(e.fillColor){const r=D(t,n,Y(),i);o.fillStyle=e.fillColor,o.fill(r)}if(e.hatchColor){const r=180*Math.atan2(t.p2.y-t.p1.y,t.p2.x-t.p1.x)/Math.PI,s=Y();U(o,D(t,n,s,i),s,10,e.hatchColor,r,!1)}}}function Xt(t,n,e,o,i){i&&([t,n]=[n,t]);const r=o?Math.atan2(t.p4.y-t.p1.y,t.p4.x-t.p1.x):Math.atan2(t.p2.y-t.p1.y,t.p2.x-t.p1.x),s=Jt(o?t.p4:t.p2,n.p1),a=s/2-e/2,l=s/2+e/2;return o?{p1:{x:Math.round(t.p4.x+a*Math.cos(r)),y:Math.round(t.p4.y+a*Math.sin(r))},p2:{x:Math.round(t.p3.x+a*Math.cos(r)),y:Math.round(t.p3.y+a*Math.sin(r))},p3:{x:Math.round(t.p3.x+l*Math.cos(r)),y:Math.round(t.p3.y+l*Math.sin(r))},p4:{x:Math.round(t.p4.x+l*Math.cos(r)),y:Math.round(t.p4.y+l*Math.sin(r))}}:{p1:{x:Math.round(t.p2.x+a*Math.cos(r)),y:Math.round(t.p2.y+a*Math.sin(r))},p2:{x:Math.round(t.p2.x+l*Math.cos(r)),y:Math.round(t.p2.y+l*Math.sin(r))},p3:{x:Math.round(t.p3.x+l*Math.cos(r)),y:Math.round(t.p3.y+l*Math.sin(r))},p4:{x:Math.round(t.p3.x+a*Math.cos(r)),y:Math.round(t.p3.y+a*Math.sin(r))}}}function It(t,n){const e=n&&t.p4.y===n.p1.y,o=["M",t.p1.x,t.p1.y,"L",t.p2.x,t.p2.y];return e?o:[...o,"M",t.p3.x,t.p3.y,"L",t.p4.x,t.p4.y]}function Ut(t,n){const e=n&&t.p2.x===n.p1.x,o=["M",t.p1.x,t.p1.y,"L",t.p4.x,t.p4.y];return e?o:[...o,"M",t.p3.x,t.p3.y,"L",t.p2.x,t.p2.y]}function Dt(t){return["M",t[0].x,t[0].y,"L",t[1].x,t[1].y]}function Tt(t){return{p1:{x:t[1],y:t[2]},p2:{x:t[4],y:t[5]},p3:{x:t[7],y:t[8]},p4:{x:t[10],y:t[11]}}}function Rt(t,n,e,o){o&&([t,n]=[n,t]);const i=e?[t.p1,t.p4]:[t.p1,t.p2],r=e?[n.p2,n.p3]:[n.p4,n.p3],s=e?[t.p1,t.p2]:[t.p1,t.p4],a=e?[t.p4,t.p3]:[t.p2,t.p3],l=e?[n.p1,n.p2]:[n.p1,n.p4],c=e?[n.p4,n.p3]:[n.p2,n.p3];let d,h,p,u;return e?(d=Qt(i,l),Nt(i,d)&&(d=t.p1),h=Qt(r,s),Nt(r,h)&&(h=n.p2),p=Qt(r,a),Nt(r,p)&&(p=n.p3),u=Qt(i,c),Nt(i,u)&&(u=t.p4)):(d=Qt(i,l),Nt(i,d)&&(d=t.p1),h=Qt(i,c),Nt(i,h)&&(h=t.p2),p=Qt(r,a),Nt(r,p)&&(p=n.p3),u=Qt(r,s),Nt(r,u)&&(u=n.p4)),{p1:d,p2:h,p3:p,p4:u}}function Ot(t,n,e){return{p1:e?Qt([n.p1,n.p4],[t.p1,t.p2]):Qt([n.p1,n.p2],[t.p1,t.p4]),p2:e?Qt([n.p2,n.p3],[t.p1,t.p2]):Qt([n.p1,n.p2],[t.p2,t.p3]),p3:e?Qt([n.p2,n.p3],[t.p3,t.p4]):Qt([n.p3,n.p4],[t.p2,t.p3]),p4:e?Qt([n.p1,n.p4],[t.p3,t.p4]):Qt([n.p3,n.p4],[t.p1,t.p4])}}function Qt([t,n],[e,o]){const i=((t.x*n.y-t.y*n.x)*(e.x-o.x)-(t.x-n.x)*(e.x*o.y-e.y*o.x))/((t.x-n.x)*(e.y-o.y)-(t.y-n.y)*(e.x-o.x)),r=((t.x*n.y-t.y*n.x)*(e.y-o.y)-(t.y-n.y)*(e.x*o.y-e.y*o.x))/((t.x-n.x)*(e.y-o.y)-(t.y-n.y)*(e.x-o.x));return{x:Object.is(i,-0)?0:i,y:Object.is(r,-0)?0:r}}function Nt([t,n],e){return!(t.x<n.x&&(e.x<t.x||e.x>n.x))&&(!(t.x>n.x&&(e.x>t.x||e.x<n.x))&&(!(t.y<n.y&&(e.y<t.y||e.y>n.y))&&(!(t.y>n.y&&(e.y>t.y||e.y<n.y))&&(e.y-t.y)*(n.x-t.x)==(n.y-t.y)*(e.x-t.x))))}function Jt(t,n){return Math.sqrt(Math.pow(n.x-t.x,2)+Math.pow(n.y-t.y,2))}function Kt(t,n,e){const o=(n.y-t.y)/(n.x-t.x),i=Math.atan(o);return{x:t.x+e*Math.cos(i),y:t.y+e*Math.sin(i)}}const Gt=new Map([["width","ew-resize"],["height","ns-resize"],["bidirection","nwse-resize"]]);class Zt{document;delegate;originX;originY;boundMousemove;boundMousedown;constructor(t,n){this.document=t,this.delegate=n,this.boundMousemove=this.onMousemove.bind(this),this.boundMousedown=this.onMousedown.bind(this)}install(){this.document.body.addEventListener("mousemove",this.boundMousemove),this.document.body.addEventListener("mousedown",this.boundMousedown)}uninstall(){this.document.body.removeEventListener("mousemove",this.boundMousemove),this.document.body.removeEventListener("mousedown",this.boundMousedown)}onMousemove(t){const n=this.delegate.getDraggable(t.clientX,t.clientY);this.document.body.style.cursor=n&&Gt.get(n.type)||"default"}onMousedown(t){const n=this.delegate.getDraggable(t.clientX,t.clientY);if(!n)return;const e=this.onDrag.bind(this,n);t.stopPropagation(),t.preventDefault(),void 0===n.initialWidth||"width"!==n.type&&"bidirection"!==n.type||(this.originX={coord:Math.round(t.clientX),value:n.initialWidth}),void 0===n.initialHeight||"height"!==n.type&&"bidirection"!==n.type||(this.originY={coord:Math.round(t.clientY),value:n.initialHeight}),this.document.body.removeEventListener("mousemove",this.boundMousemove),this.document.body.style.cursor=Gt.get(n.type)||"default";const o=t=>{t.stopPropagation(),t.preventDefault(),this.originX=void 0,this.originY=void 0,this.document.body.style.cursor="default",this.document.body.removeEventListener("mousemove",e),this.document.body.addEventListener("mousemove",this.boundMousemove)};this.document.body.addEventListener("mouseup",o,{once:!0}),window.addEventListener("mouseout",o,{once:!0}),this.document.body.addEventListener("mousemove",e)}onDrag(t,n){if(!this.originX&&!this.originY)return;let e,o;if(this.originX){const t=this.originX.coord-n.clientX;e=Math.round(this.originX.value-t)}if(this.originY){const t=this.originY.coord-n.clientY;o=Math.round(this.originY.value-t)}t.update({width:e,height:o})}}function Vt(t,n){return"start"===n?{x:(t.minX+t.maxX)/2,y:t.minY}:"center"===n?{x:(t.minX+t.maxX)/2,y:(t.minY+t.maxY)/2}:"end"===n?{x:(t.minX+t.maxX)/2,y:t.maxY}:void 0}const jt=5,qt="white",$t=6,_t="#4585f6",tn=4;function nn(t,n,e){let o=0,i=!0;n.x===e.minX?(o=-.5*Math.PI,i=!1):n.x===e.maxX?(o=.5*Math.PI,i=!1):n.y===e.minY?(o=0,i=!1):n.y===e.maxY&&(o=Math.PI,i=!1);const r=o+(i?2*Math.PI:Math.PI);t.save(),t.beginPath(),t.lineWidth=jt,t.strokeStyle=qt,t.arc(n.x,n.y,$t,o,r),t.stroke(),t.fillStyle=_t,t.arc(n.x,n.y,tn,o,r),t.fill(),t.restore()}function en(t,n,e){!function(t,n,e){O(n,t.paddingBox,t.scrollPaddingColor,void 0,void 0,Y(),e),n.save(),n.globalCompositeOperation="destination-out",O(n,t.snapport,"white",void 0,void 0,Y(),e),n.restore()}(t,n,e);const o=function(t,n,e){const o=[];for(const i of t.snapAreas){const r=Y();O(n,i.path,t.scrollMarginColor,t.snapAreaBorder.color,t.snapAreaBorder.pattern,r,e),n.save(),n.globalCompositeOperation="destination-out",O(n,i.borderBox,"white",void 0,void 0,Y(),e),n.restore(),o.push(r)}return o}(t,n,e);!function(t,n,e){O(n,t.snapport,void 0,t.snapportBorder.color,void 0,Y(),e)}(t,n,e),function(t,n,e){for(let r=0;r<n.snapAreas.length;r++){const s=n.snapAreas[r],a=s.alignInline?(o=t[r],"start"===(i=s.alignInline)?{x:o.minX,y:(o.minY+o.maxY)/2}:"center"===i?{x:(o.minX+o.maxX)/2,y:(o.minY+o.maxY)/2}:"end"===i?{x:o.maxX,y:(o.minY+o.maxY)/2}:void 0):null,l=s.alignBlock?Vt(t[r],s.alignBlock):null;a&&nn(e,a,t[r]),l&&nn(e,l,t[r])}var o,i}(o,t,n)}class on extends n{gridLabelState={gridLayerCounter:0};gridLabels;draggableBorders=new Map;dragHandler;reset(t){super.reset(t),this.gridLabelState.gridLayerCounter=0,this.gridLabels.innerHTML=""}renderGridMarkup(){const t=this.document.createElement("div");t.id="grid-label-container",this.document.body.append(t),this.gridLabels=t}install(){this.document.body.classList.add("fill");const t=this.document.createElement("canvas");t.id="canvas",t.classList.add("fill"),this.document.body.append(t),this.renderGridMarkup(),this.setCanvas(t),super.install(),this.dragHandler?.install()}uninstall(){this.document.body.classList.remove("fill"),this.document.body.innerHTML="",this.draggableBorders=new Map,super.uninstall(),this.dragHandler?.uninstall()}drawGridHighlight(t){this.context.save(),xt(t,this.context,this.deviceScaleFactor,this.canvasWidth,this.canvasHeight,this.emulationScaleFactor,this.gridLabelState),this.context.restore()}drawFlexContainerHighlight(t){this.context.save(),zt(t,this.context,this.deviceScaleFactor,this.canvasWidth,this.canvasHeight,this.emulationScaleFactor),this.context.restore()}drawScrollSnapHighlight(t){this.context.save(),en(t,this.context,this.emulationScaleFactor),this.context.restore()}drawContainerQueryHighlight(t){this.context.save(),Mt(t,this.context,this.emulationScaleFactor),this.context.restore()}drawIsolatedElementHighlight(t){var n;this.dragHandler||(this.dragHandler=new Zt(this.document,(n=this,{getDraggable:(t,e)=>{const o=n.isPointInDraggablePath(t,e);if(o)return{type:o.type,initialWidth:o.initialWidth,initialHeight:o.initialHeight,id:o.highlightIndex,update:({width:t,height:n})=>{window.InspectorOverlayHost.send({highlightType:"isolatedElement",highlightIndex:o.highlightIndex,newWidth:`${t}px`,newHeight:`${n}px`,resizerType:o.type})}}}})),this.dragHandler.install()),this.context.save();const{widthPath:e,heightPath:o,bidirectionPath:i,currentWidth:r,currentHeight:s,highlightIndex:a}=function(t,n,e,o,i){const{currentX:r,currentY:s,currentWidth:a,currentHeight:l,highlightIndex:c}=t;n.save(),n.fillStyle=t.isolationModeHighlightConfig.maskColor,n.fillRect(0,0,e,o),n.clearRect(r,s,a,l),n.restore();const d=Y(),h=H(t.widthResizerBorder,d,i);P(n,h,d,0,{fillColor:t.isolationModeHighlightConfig.resizerColor});const p=H(t.heightResizerBorder,d,i);P(n,p,d,0,{fillColor:t.isolationModeHighlightConfig.resizerColor});const u=H(t.bidirectionResizerBorder,d,i);return P(n,u,d,0,{fillColor:t.isolationModeHighlightConfig.resizerColor}),{widthPath:h,heightPath:p,bidirectionPath:u,currentWidth:a,currentHeight:l,highlightIndex:c}}(t,this.context,this.canvasWidth,this.canvasHeight,this.emulationScaleFactor);this.draggableBorders.set(a,{widthPath:e,heightPath:o,bidirectionPath:i,highlightIndex:a,initialWidth:r,initialHeight:s}),this.context.restore()}isPointInDraggablePath(t,n){for(const{widthPath:e,heightPath:o,bidirectionPath:i,highlightIndex:r,initialWidth:s,initialHeight:a}of this.draggableBorders.values()){if(this.context.isPointInPath(e,t,n))return{type:"width",highlightIndex:r,initialWidth:s};if(this.context.isPointInPath(o,t,n))return{type:"height",highlightIndex:r,initialHeight:a};if(this.context.isPointInPath(i,t,n))return{type:"bidirection",highlightIndex:r,initialWidth:s,initialHeight:a}}}}function rn(t){return 0===t[3]}const sn="rgba(0,0,0,0.2)",an="rgba(0,0,0,0.7)",ln="rgba(255, 255, 255, 0.8)";const cn="rgba(128, 128, 128, 0.3)";const dn=new CSSStyleSheet;dn.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\nbody {\n  background-color: rgb(0 0 0 / 31%);\n}\n\n.controls-line {\n  display: flex;\n  justify-content: center;\n  margin: 10px 0;\n}\n\n.message-box {\n  padding: 2px 4px;\n  display: flex;\n  align-items: center;\n  cursor: default;\n  overflow: hidden;\n}\n\n#paused-in-debugger {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n\n.controls-line > * {\n  background-color: rgb(255 255 194);\n  border: 1px solid rgb(202 202 202);\n  height: 22px;\n  box-sizing: border-box;\n}\n\n.controls-line .button {\n  width: 26px;\n  margin-left: -1px;\n  margin-right: 0;\n  padding: 0;\n  flex-shrink: 0;\n  flex-grow: 0;\n  cursor: pointer;\n}\n\n.controls-line .button .glyph {\n  width: 100%;\n  height: 100%;\n  background-color: rgb(0 0 0 / 75%);\n  opacity: 80%;\n  mask-repeat: no-repeat;\n  mask-position: center;\n  position: relative;\n}\n\n.controls-line .button:active .glyph {\n  top: 1px;\n  left: 1px;\n}\n\n#resume-button .glyph {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAKCAYAAABv7tTEAAAAAXNSR0IArs4c6QAAAFJJREFUKM+10bEJgGAMBeEPbR3BLRzEVdzEVRzELRzBVohVwEJ+iODBlQfhBeJhsmHU4C0KnFjQV6J0x1SNAhdWDJUoPTB3PvLLeaUhypM3n3sD/qc7lDrdpIEAAAAASUVORK5CYII=");\n  mask-size: 13px 10px;\n  background-color: rgb(66 129 235);\n}\n\n#step-over-button .glyph {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAKCAYAAAC5Sw6hAAAAAXNSR0IArs4c6QAAAOFJREFUKM+N0j8rhXEUB/DPcxW35CqhvIBrtqibkklhV8qkTHe4ZbdblcXgPVhuMdqUTUl5A2KRRCF5LGc4PT1P7qnfcr5/zu/8KdTHLFaxjHnc4RZXKI0QYxjgLQTVd42l/0wmg5iFX3iq5H6w22RS4DyRH7CB8cAXcBTGJT6xUmd0mEwuMdFQcA3fwXvGTAan8BrgPabTL9fRRyfx91PRMwyjGwcJ2EyCfsrfpPw2Pipz24NT/MZciiQYVshzOKnZ5Hturxt3k2MnCpS4SPkeHpPR8Sh3tYgttBoW9II2/AHiaEqvD2Fc0wAAAABJRU5ErkJggg==");\n  mask-size: 18px 10px;\n}\n');const hn=new CSSStyleSheet;hn.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\nbody {\n  cursor: crosshair;\n}\n\n#zone {\n  background-color: #0003;\n  border: 1px solid #fffd;\n  display: none;\n  position: absolute;\n}\n");let pn=null,un=null;function gn(){if(!pn)throw new Error("Error calculating currentRect: no anchor was defined.");if(!un)throw new Error("Error calculating currentRect: no position was defined.");return{x:Math.min(pn.x,un.x),y:Math.min(pn.y,un.y),width:Math.abs(pn.x-un.x),height:Math.abs(pn.y-un.y)}}function mn(){pn=null,un=null}const fn=new CSSStyleSheet;fn.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:root {\n  --border-radius: 4px;\n}\n\n.source-order-label-container {\n  display: block;\n  min-width: 20px;\n  position: absolute;\n  text-align: center;\n  align-items: center;\n  background-color: #fff;\n  font-family: Menlo, Consolas, monospace;\n  font-size: 12px;\n  font-weight: bold;\n  padding: 2px;\n  border: 1.5px solid;\n}\n\n.top-corner {\n  border-bottom-right-radius: var(--border-radius);\n}\n\n.bottom-corner {\n  border-top-right-radius: var(--border-radius);\n}\n\n.above-element {\n  border-top-right-radius: var(--border-radius);\n  border-top-left-radius: var(--border-radius);\n}\n\n.below-element {\n  border-bottom-right-radius: var(--border-radius);\n  border-bottom-left-radius: var(--border-radius);\n}\n\n.above-element-wider {\n  border-top-right-radius: var(--border-radius);\n  border-top-left-radius: var(--border-radius);\n  border-bottom-right-radius: var(--border-radius);\n}\n\n.below-element-wider {\n  border-bottom-right-radius: var(--border-radius);\n  border-bottom-left-radius: var(--border-radius);\n  border-top-right-radius: var(--border-radius);\n}\n\n.bottom-corner-wider {\n  border-top-right-radius: var(--border-radius);\n  border-bottom-right-radius: var(--border-radius);\n}\n\n.bottom-corner-taller {\n  border-top-right-radius: var(--border-radius);\n  border-top-left-radius: var(--border-radius);\n}\n\n.bottom-corner-wider-taller {\n  border-top-left-radius: var(--border-radius);\n  border-top-right-radius: var(--border-radius);\n  border-bottom-right-radius: var(--border-radius);\n}\n");const xn=300,bn={topCorner:"top-corner",aboveElement:"above-element",belowElement:"below-element",aboveElementWider:"above-element-wider",belowElementWider:"below-element-wider",bottomCornerWider:"bottom-corner-wider",bottomCornerTaller:"bottom-corner-taller",bottomCornerWiderTaller:"bottom-corner-wider-taller"};function yn(t){return t%1?t.toFixed(2):String(t)}const vn=new CSSStyleSheet;vn.replaceSync('/*\n * Copyright 2023 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:root {\n  --wco-theme-color: #121212;\n  --wco-icon-color: #fff;\n}\n\n.image-group {\n  display: flex;\n  background-color: var(--wco-theme-color);\n  align-items: center;\n}\n\n.image-group-left {\n  float: left;\n  justify-content: flex-start;\n  gap: 4px;\n  padding-left: 12px;\n}\n\n.image-group-right {\n  float: right;\n  justify-content: flex-end;\n  gap: 2px;\n  padding-right: 17px;\n}\n\n.windows-right-image-group {\n  width: 238px;\n  height: 33px;\n}\n\n.linux-right-image-group {\n  width: 196px;\n  height: 34px;\n}\n\n.mac-left-image-group {\n  width: 74px;\n  height: 40px;\n}\n\n.mac-right-image-group {\n  width: 100px;\n  height: 40px;\n}\n\n.image {\n  width: 33px;\n  height: 33px;\n  background-color: var(--wco-icon-color);\n}\n\n#mac-chevron,\n#mac-ellipsis {\n  width: 40px;\n  height: 40px;\n  background-color: var(--wco-icon-color);\n}\n\n#close {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAfCAYAAACPvW/2AAABhWlDQ1BJQ0MgcHJvZmlsZQAAKJF9kT1Iw0AcxV9TpSJVBytIcchQneyiIuJUqlgEC6Wt0KqDyaUfQpOGJMXFUXAtOPixWHVwcdbVwVUQBD9AXF2cFF2kxP8lhRYxHhz34929x907QGhUmGp2xQBVs4x0Ii7m8iti4BUBhDGEfsxKzNSTmYUsPMfXPXx8vYvyLO9zf44+pWAywCcSx5huWMTrxNObls55nzjEypJCfE48btAFiR+5Lrv8xrnksMAzQ0Y2PUccIhZLHSx3MCsbKvEUcURRNcoXci4rnLc4q5Uaa92TvzBY0JYzXKc5ggQWkUQKImTUsIEKLERp1Ugxkab9uIc/7PhT5JLJtQFGjnlUoUJy/OB/8Ltbszg54SYF40D3i21/jAKBXaBZt+3vY9tungD+Z+BKa/urDWDmk/R6W4scAQPbwMV1W5P3gMsdYPhJlwzJkfw0hWIReD+jb8oDg7dA76rbW2sfpw9AlrpaugEODoGxEmWveby7p7O3f8+0+vsB9f9y2zZ6P+8AAAAGYktHRAD/AP8A/6C9p5MAAAAJcEhZcwAALiMAAC4jAXilP3YAAAAHdElNRQfnBxsWBAcQDgJxAAAAGXRFWHRDb21tZW50AENyZWF0ZWQgd2l0aCBHSU1QV4EOFwAAAPlJREFUWMPtlTFOxDAQRd8k5gYb+i1W6Si4/ym22wpatJHogWQo+JaMlAjZgS3QPClSIk/s55mxDEEQBME3rPYHdzcgAbOZLRsxnWLezcxr5u8aNpGAR2Bw935FpgfuFZNqJ28RmoFnYAQOpZTeB409KbZ6t3U1NlvcfVK5R4lMGs4yF2DaKumvCklqdverPkdl2oCTZK5mNt+kqTf65UFznYGXVpnWHlrblAGuZxdpZ3YGleksmdPXkDeXLO2UyQ2c+2kpGr1JKjXIdMChlMkLF6dtLDK1/HWGeuC4dpp0+rLUEXgF3m5xddwBHz9cHb1inCAIguAf8QkteHDWohPAIAAAAABJRU5ErkJggg==");\n}\n\n#maximize {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAfCAYAAACPvW/2AAABhWlDQ1BJQ0MgcHJvZmlsZQAAKJF9kT1Iw0AcxV9TpSJVBytIcchQneyiIuJUqlgEC6Wt0KqDyaUfQpOGJMXFUXAtOPixWHVwcdbVwVUQBD9AXF2cFF2kxP8lhRYxHhz34929x907QGhUmGp2xQBVs4x0Ii7m8iti4BUBhDGEfsxKzNSTmYUsPMfXPXx8vYvyLO9zf44+pWAywCcSx5huWMTrxNObls55nzjEypJCfE48btAFiR+5Lrv8xrnksMAzQ0Y2PUccIhZLHSx3MCsbKvEUcURRNcoXci4rnLc4q5Uaa92TvzBY0JYzXKc5ggQWkUQKImTUsIEKLERp1Ugxkab9uIc/7PhT5JLJtQFGjnlUoUJy/OB/8Ltbszg54SYF40D3i21/jAKBXaBZt+3vY9tungD+Z+BKa/urDWDmk/R6W4scAQPbwMV1W5P3gMsdYPhJlwzJkfw0hWIReD+jb8oDg7dA76rbW2sfpw9AlrpaugEODoGxEmWveby7p7O3f8+0+vsB9f9y2zZ6P+8AAAAGYktHRAD/AP8A/6C9p5MAAAAJcEhZcwAALiMAAC4jAXilP3YAAAAHdElNRQfnBxsWBACOapfSAAAAGXRFWHRDb21tZW50AENyZWF0ZWQgd2l0aCBHSU1QV4EOFwAAAGJJREFUWMPt07sNgDAMhOEzQhkHxqHK0GEcmp8NiGTJQHFf64culiKZmdm/RWYI2CVtk7YzIsYrrwA60B7qDeiZ3Uv6tBFXplYWqIoDOdBngWbfPrt3Tc4NSQcw6zEzM6t2A1K/HsQFSWEQAAAAAElFTkSuQmCC");\n}\n\n#minimize {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAfCAYAAACPvW/2AAABhWlDQ1BJQ0MgcHJvZmlsZQAAKJF9kT1Iw0AcxV9TpSJVBytIcchQneyiIuJUqlgEC6Wt0KqDyaUfQpOGJMXFUXAtOPixWHVwcdbVwVUQBD9AXF2cFF2kxP8lhRYxHhz34929x907QGhUmGp2xQBVs4x0Ii7m8iti4BUBhDGEfsxKzNSTmYUsPMfXPXx8vYvyLO9zf44+pWAywCcSx5huWMTrxNObls55nzjEypJCfE48btAFiR+5Lrv8xrnksMAzQ0Y2PUccIhZLHSx3MCsbKvEUcURRNcoXci4rnLc4q5Uaa92TvzBY0JYzXKc5ggQWkUQKImTUsIEKLERp1Ugxkab9uIc/7PhT5JLJtQFGjnlUoUJy/OB/8Ltbszg54SYF40D3i21/jAKBXaBZt+3vY9tungD+Z+BKa/urDWDmk/R6W4scAQPbwMV1W5P3gMsdYPhJlwzJkfw0hWIReD+jb8oDg7dA76rbW2sfpw9AlrpaugEODoGxEmWveby7p7O3f8+0+vsB9f9y2zZ6P+8AAAAGYktHRAD/AP8A/6C9p5MAAAAJcEhZcwAALiMAAC4jAXilP3YAAAAHdElNRQfnBxsWAzIJ/FCVAAAAGXRFWHRDb21tZW50AENyZWF0ZWQgd2l0aCBHSU1QV4EOFwAAADNJREFUWMPt0LERACEMA0E5/+ZolO4+NiUwEDK78SlRAgC8rW5G3T2SfJvsr6rpYgCAMwvylgUCKbPyMgAAAABJRU5ErkJggg==");\n}\n\n#mac-ellipsis,\n#ellipsis {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAfCAYAAACPvW/2AAABhWlDQ1BJQ0MgcHJvZmlsZQAAKJF9kT1Iw0AcxV9TpSJVBytIcchQneyiIuJUqlgEC6Wt0KqDyaUfQpOGJMXFUXAtOPixWHVwcdbVwVUQBD9AXF2cFF2kxP8lhRYxHhz34929x907QGhUmGp2xQBVs4x0Ii7m8iti4BUBhDGEfsxKzNSTmYUsPMfXPXx8vYvyLO9zf44+pWAywCcSx5huWMTrxNObls55nzjEypJCfE48btAFiR+5Lrv8xrnksMAzQ0Y2PUccIhZLHSx3MCsbKvEUcURRNcoXci4rnLc4q5Uaa92TvzBY0JYzXKc5ggQWkUQKImTUsIEKLERp1Ugxkab9uIc/7PhT5JLJtQFGjnlUoUJy/OB/8Ltbszg54SYF40D3i21/jAKBXaBZt+3vY9tungD+Z+BKa/urDWDmk/R6W4scAQPbwMV1W5P3gMsdYPhJlwzJkfw0hWIReD+jb8oDg7dA76rbW2sfpw9AlrpaugEODoGxEmWveby7p7O3f8+0+vsB9f9y2zZ6P+8AAAAGYktHRAD/AP8A/6C9p5MAAAAJcEhZcwAALiMAAC4jAXilP3YAAAAHdElNRQfnBxsTEiHYUPCwAAAAGXRFWHRDb21tZW50AENyZWF0ZWQgd2l0aCBHSU1QV4EOFwAAAEBJREFUWMPt0aENACAQBMENEnqj/24OjwISAmJHrrrPgyRJeitJS9JO2oqyOwboQE9Sd9qVQb++rM5XrzZJkiQY1Fw4YEmaUfMAAAAASUVORK5CYII=");\n}\n\n#mac-chevron,\n#chevron {\n  mask-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAfCAYAAACPvW/2AAABhWlDQ1BJQ0MgcHJvZmlsZQAAKJF9kT1Iw0AcxV9TpSJVBytIcchQneyiIuJUqlgEC6Wt0KqDyaUfQpOGJMXFUXAtOPixWHVwcdbVwVUQBD9AXF2cFF2kxP8lhRYxHhz34929x907QGhUmGp2xQBVs4x0Ii7m8iti4BUBhDGEfsxKzNSTmYUsPMfXPXx8vYvyLO9zf44+pWAywCcSx5huWMTrxNObls55nzjEypJCfE48btAFiR+5Lrv8xrnksMAzQ0Y2PUccIhZLHSx3MCsbKvEUcURRNcoXci4rnLc4q5Uaa92TvzBY0JYzXKc5ggQWkUQKImTUsIEKLERp1Ugxkab9uIc/7PhT5JLJtQFGjnlUoUJy/OB/8Ltbszg54SYF40D3i21/jAKBXaBZt+3vY9tungD+Z+BKa/urDWDmk/R6W4scAQPbwMV1W5P3gMsdYPhJlwzJkfw0hWIReD+jb8oDg7dA76rbW2sfpw9AlrpaugEODoGxEmWveby7p7O3f8+0+vsB9f9y2zZ6P+8AAAAGYktHRAD/AP8A/6C9p5MAAAAJcEhZcwAALiMAAC4jAXilP3YAAAAHdElNRQfnBxsTEjCy4NBCAAAAGXRFWHRDb21tZW50AENyZWF0ZWQgd2l0aCBHSU1QV4EOFwAAAKBJREFUWMPtk7sKAkEMRU/WRrtFv0hZ9J9VXD9psdFCiM0IFruYeZT3VDMh4R4yDAghhBBZWLTR3U9AB4xm9grOrIED8Dazc2Smy5BfATtgSEERmQHYpllaC92ACeiBo7tvAjI98ADuzZ9sIehiZs/cnmZC/wJrZYqEloIBr5UpFpqRmlL5e75Gf2IzoRkpajbTROhHap+uY+lmhBBCiEI+sBxN3vpZhO0AAAAASUVORK5CYII=");\n}\n\n#mac-close,\n#mac-minimize,\n#mac-maximize {\n  width: 14px;\n  height: 14px;\n  border-radius: 50%;\n}\n\n#mac-close {\n  background-color: #ff5f57;\n}\n\n#mac-minimize {\n  background-color: #ffbd2e;\n}\n\n#mac-maximize {\n  background-color: #28c941;\n}\n');function wn(t){t.classList.add("hidden")}function An(t){t.classList.remove("hidden")}function Mn(t,n,e){const o=function(t){const n=i("div");for(const e of t){const t=i("div");t.id=e,t.classList.add("image"),n.append(t)}return n}(e);return o.classList.add("image-group"),o.classList.add(`image-group-${n}`),o.classList.add(`${t}-${n}-image-group`),o.classList.add("hidden"),o}a(t);const Cn=new CSSStyleSheet;Cn.replaceSync('\n/* Grid row and column labels */\n.grid-label-content {\n  position: absolute;\n  -webkit-user-select: none;\n  padding: 2px;\n  font-family: Menlo, monospace;\n  font-size: 10px;\n  min-width: 17px;\n  min-height: 15px;\n  border-radius: 2px;\n  box-sizing: border-box;\n  z-index: 1;\n  background-clip: padding-box;\n  pointer-events: none;\n  text-align: center;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.grid-label-content[data-direction=row] {\n  background-color: var(--row-label-color, #1A73E8);\n  color: var(--row-label-text-color, #121212);\n}\n\n.grid-label-content[data-direction=column] {\n  background-color: var(--column-label-color, #1A73E8);\n  color: var(--column-label-text-color,#121212);\n}\n\n.line-names ul,\n.line-names .line-name {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n\n.line-names .line-name {\n  max-width: 100px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.line-names .grid-label-content,\n.line-numbers .grid-label-content,\n.track-sizes .grid-label-content {\n  border: 1px solid white;\n  --inner-corner-avoid-distance: 15px;\n}\n\n.grid-label-content.top-left.inner-shared-corner,\n.grid-label-content.top-right.inner-shared-corner {\n  transform: translateY(var(--inner-corner-avoid-distance));\n}\n\n.grid-label-content.bottom-left.inner-shared-corner,\n.grid-label-content.bottom-right.inner-shared-corner {\n  transform: translateY(calc(var(--inner-corner-avoid-distance) * -1));\n}\n\n.grid-label-content.left-top.inner-shared-corner,\n.grid-label-content.left-bottom.inner-shared-corner {\n  transform: translateX(var(--inner-corner-avoid-distance));\n}\n\n.grid-label-content.right-top.inner-shared-corner,\n.grid-label-content.right-bottom.inner-shared-corner {\n  transform: translateX(calc(var(--inner-corner-avoid-distance) * -1));\n}\n\n.line-names .grid-label-content::before,\n.line-numbers .grid-label-content::before,\n.track-sizes .grid-label-content::before {\n  position: absolute;\n  z-index: 1;\n  pointer-events: none;\n  content: "";\n  width: 3px;\n  height: 3px;\n  border: 1px solid white;\n  border-width: 0 1px 1px 0;\n}\n\n.line-names .grid-label-content[data-direction=row]::before,\n.line-numbers .grid-label-content[data-direction=row]::before,\n.track-sizes .grid-label-content[data-direction=row]::before {\n  background: var(--row-label-color, #1A73E8);\n}\n\n.line-names .grid-label-content[data-direction=column]::before,\n.line-numbers .grid-label-content[data-direction=column]::before,\n.track-sizes .grid-label-content[data-direction=column]::before {\n  background: var(--column-label-color, #1A73E8);\n}\n\n.grid-label-content.bottom-mid::before {\n  transform: translateY(-1px) rotate(45deg);\n  top: 100%;\n}\n\n.grid-label-content.top-mid::before {\n  transform: translateY(-3px) rotate(-135deg);\n  top: 0%;\n}\n\n.grid-label-content.left-mid::before {\n  transform: translateX(-3px) rotate(135deg);\n  left: 0%\n}\n\n.grid-label-content.right-mid::before {\n  transform: translateX(3px) rotate(-45deg);\n  right: 0%;\n}\n\n.grid-label-content.right-top::before {\n  transform: translateX(3px) translateY(-1px) rotate(-90deg) skewY(30deg);\n  right: 0%;\n  top: 0%;\n}\n\n.grid-label-content.right-bottom::before {\n  transform: translateX(3px) translateY(-3px) skewX(30deg);\n  right: 0%;\n  top: 100%;\n}\n\n.grid-label-content.bottom-right::before {\n  transform:  translateX(1px) translateY(-1px) skewY(30deg);\n  right: 0%;\n  top: 100%;\n}\n\n.grid-label-content.bottom-left::before {\n  transform:  translateX(-1px) translateY(-1px) rotate(90deg) skewX(30deg);\n  left: 0%;\n  top: 100%;\n}\n\n.grid-label-content.left-top::before {\n  transform: translateX(-3px) translateY(-1px) rotate(180deg) skewX(30deg);\n  left: 0%;\n  top: 0%;\n}\n\n.grid-label-content.left-bottom::before {\n  transform: translateX(-3px) translateY(-3px) rotate(90deg) skewY(30deg);\n  left: 0%;\n  top: 100%;\n}\n\n.grid-label-content.top-right::before {\n  transform:  translateX(1px) translateY(-3px) rotate(-90deg) skewX(30deg);\n  right: 0%;\n  top: 0%;\n}\n\n.grid-label-content.top-left::before {\n  transform:  translateX(-1px) translateY(-3px) rotate(180deg) skewY(30deg);\n  left: 0%;\n  top: 0%;\n}\n\n@media (forced-colors: active) {\n  .grid-label-content {\n      border-color: Highlight;\n      background-color: Canvas;\n      color: Text;\n      forced-color-adjust: none;\n  }\n  .grid-label-content::before {\n    background-color: Canvas;\n    border-color: Highlight;\n  }\n}');const Sn=new class extends n{tooltip;persistentOverlay;gridLabelState={gridLayerCounter:0};reset(t){super.reset(t),this.tooltip.innerHTML="",this.gridLabelState.gridLayerCounter=0,this.persistentOverlay&&this.persistentOverlay.reset(t)}install(){this.document.body.classList.add("fill");const t=this.document.createElement("canvas");t.id="canvas",t.classList.add("fill"),this.document.body.append(t);const n=this.document.createElement("div");n.id="tooltip-container",this.document.body.append(n),this.tooltip=n,this.persistentOverlay=new on(this.window),this.persistentOverlay.renderGridMarkup(),this.persistentOverlay.setCanvas(t),this.setCanvas(t),super.install()}uninstall(){this.document.body.classList.remove("fill"),this.document.body.innerHTML="",super.uninstall()}drawHighlight(t){this.context.save();const n=Y();let a=null,c=null;for(let e=t.paths.slice();e.length;){const t=e.pop();t&&(this.context.save(),O(this.context,t.path,t.fillColor,t.outlineColor,void 0,n,this.emulationScaleFactor),e.length&&(this.context.globalCompositeOperation="destination-out",O(this.context,e[e.length-1].path,"red",void 0,void 0,n,this.emulationScaleFactor)),this.context.restore(),"content"===t.name&&(a=t.path),"border"===t.name&&(c=t.path))}this.context.restore(),this.context.save();const h=Boolean(t.paths.length&&t.showRulers&&n.minX<20&&n.maxX+20<this.canvasWidth),p=Boolean(t.paths.length&&t.showRulers&&n.minY<20&&n.maxY+20<this.canvasHeight);if(t.showRulers&&this.drawAxis(this.context,h,p),t.paths.length&&(t.showExtensionLines&&function(t,n,e,o,i,r,s,a){t.save();const l=s,c=a;if(t.strokeStyle=cn,t.lineWidth=1,t.translate(.5,.5),e)for(const e in n.rightmostXForY)t.beginPath(),t.moveTo(l,Number(e)),t.lineTo(n.rightmostXForY[e],Number(e)),t.stroke();else for(const e in n.leftmostXForY)t.beginPath(),t.moveTo(0,Number(e)),t.lineTo(n.leftmostXForY[e],Number(e)),t.stroke();if(o)for(const e in n.bottommostYForX)t.beginPath(),t.moveTo(Number(e),c),t.lineTo(Number(e),n.topmostYForX[e]),t.stroke();else for(const e in n.topmostYForX)t.beginPath(),t.moveTo(Number(e),0),t.lineTo(Number(e),n.topmostYForX[e]),t.stroke();t.restore()}(this.context,n,h,p,0,0,this.canvasWidth,this.canvasHeight),t.elementInfo&&function(t,n,a,c,h){const p=document.getElementById("tooltip-container");if(!p)throw new Error("#tooltip-container is not found");p.innerHTML="";const u=e(p,"div"),g=e(u,"div","tooltip-content"),m=function(t,n){const s=i("div","element-info"),a=e(s,"div","element-info-header"),c=function(t){if(t.layoutObjectName&&t.layoutObjectName.endsWith("Grid"))return"grid";if(t.layoutObjectName&&t.layoutObjectName.endsWith("FlexibleBox"))return"flex";return null}(t);c&&e(a,"div",`element-layout-type ${c}`);const h=e(a,"div","element-description monospace");e(h,"span","material-tag-name").textContent=t.tagName;const p=e(h,"span","material-node-id"),u=80;p.textContent=t.idValue?"#"+r(t.idValue,u):"",p.classList.toggle("hidden",!t.idValue);const g=e(h,"span","material-class-name");p.textContent.length<u&&(g.textContent=r(t.className||"",u-p.textContent.length));g.classList.toggle("hidden",!t.className);const m=e(a,"div","dimensions");e(m,"span","material-node-width").textContent=String(Math.round(100*t.nodeWidth)/100),o(m,"×"),e(m,"span","material-node-height").textContent=String(Math.round(100*t.nodeHeight)/100);const f=t.style||{};let x;t.isLockedAncestor&&W("Showing content-visibility ancestor","");t.isLocked&&W("Descendants are skipped due to content-visibility","");const b=f.color,y=f["color-unclamped-rgba"];b&&y&&!rn(y)&&X("Color",f["color-css-text"]??b,f["color-css-text"]?"original":n);const v=f["font-family"],w=f["font-size"];v&&"0px"!==w&&W("Font",`${w} ${v}`);const A=f["background-color"],M=f["background-color-unclamped-rgba"];A&&M&&!rn(M)&&X("Background",f["background-color-css-text"]??A,f["background-color-css-text"]?"original":n);const S=f.margin;S&&"0px"!==S&&W("Margin",S);const k=f.padding;k&&"0px"!==k&&W("Padding",k);const F=t.contrast?t.contrast.backgroundColorUnclampedRgba:null,P=y&&!rn(y)&&F&&!rn(F);t.showAccessibilityInfo&&(Y("Accessibility"),P&&f["color-unclamped-rgba"]&&t.contrast&&I(f["color-unclamped-rgba"],t.contrast),W("Name",t.accessibleName),W("Role",t.accessibleRole),B("Keyboard-focusable",t.isKeyboardFocusable?"a11y-icon a11y-icon-ok":"a11y-icon a11y-icon-not-ok"));function H(){x||(x=e(s,"div","element-info-body"))}function Y(t){H();const n=e(x,"div","element-info-row element-info-section");e(n,"div","section-name").textContent=t,e(e(n,"div","separator-container"),"div","separator")}function z(t,n,o){H();const i=e(x,"div","element-info-row");return e(i,"div","element-info-name").textContent=t,e(i,"div","element-info-gap"),e(i,"div",o||"")}function B(t,n){e(z(t,"","element-info-value-icon"),"div",n)}function W(t,n){o(z(t,"","element-info-value-text"),n)}function X(t,n,i){const r=z(t,"","element-info-value-color"),s=e(r,"div","color-swatch");e(s,"div","color-swatch-inner").style.backgroundColor=n,o(r,function(t,n){return"rgb"===n||"hsl"===n||"hwb"===n?R(T(t),n):t.endsWith("FF")?t.substr(0,7):t}(n,i))}function I(t,n){const o=t.slice(),i=n.backgroundColorUnclampedRgba.slice();o[3]*=n.textOpacity;const r=z("Contrast","","element-info-value-contrast"),s=e(r,"div","contrast-text");s.style.color=R(o,"rgb"),s.style.backgroundColor=n.backgroundColorCssText,s.textContent="Aa";const a=e(r,"span");if("apca"===n.contrastAlgorithm){const t=C(o,i),s=function(t,n){const e=parseFloat(t.replace("px","")),o=parseFloat(n);for(const[t,...n]of L)if(e>=t)for(const[t,e]of[900,800,700,600,500,400,300,200,100].entries())if(o>=e){const e=n[n.length-1-t];return-1===e?null:e}return null}(n.fontSize,n.fontWeight);a.textContent=String(Math.floor(100*t)/100)+"%",e(r,"div",null===s||Math.abs(t)<s?"a11y-icon a11y-icon-warning":"a11y-icon a11y-icon-ok")}else if("aa"===n.contrastAlgorithm||"aaa"===n.contrastAlgorithm){const t=function(t,n){const e=d(l(t,n)),o=d(n);return(Math.max(e,o)+.05)/(Math.min(e,o)+.05)}(o,i),s=E(n.fontSize,n.fontWeight)[n.contrastAlgorithm];a.textContent=String(Math.floor(100*t)/100),e(r,"div",t<s?"a11y-icon a11y-icon-warning":"a11y-icon a11y-icon-ok")}}return s}(t,n);g.appendChild(m);const f=g.offsetWidth,x=g.offsetHeight,b=8,y=2,v=2*b,w=b+2,A=y+w,M=c-y-w-v,S=a.maxX-a.minX<v+2*w;let k;if(S)k=.5*(a.minX+a.maxX)-b;else{const t=a.minX+w,n=a.maxX-w-v;k=t>A&&t<M?t:s(A,t,n)}const F=k<A||k>M;let P=k-w;P=s(P,y,c-f-y);let H=a.minY-b-x,Y=!0;H<0?(H=Math.min(h-x,a.maxY+b),Y=!1):a.minY>h&&(H=h-b-x);const z=P>=a.minX&&P+f<=a.maxX&&H>=a.minY&&H+x<=a.maxY,B=P<a.maxX&&P+f>a.minX&&H<a.maxY&&H+x>a.minY;if(B&&!z)return void(g.style.display="none");if(g.style.top=H+"px",g.style.left=P+"px",g.style.setProperty("--arrow-visibility",F||z?"hidden":"visible"),F)return;g.style.setProperty("--arrow",Y?"var(--arrow-down)":"var(--arrow-up)"),g.style.setProperty("--shadow-direction",Y?"var(--shadow-up)":"var(--shadow-down)"),g.style.setProperty("--arrow-top",(Y?x-1:-b)+"px"),g.style.setProperty("--arrow-left",k-P+"px")}(t.elementInfo,t.colorFormat,n,this.canvasWidth,this.canvasHeight)),t.gridInfo)for(const n of t.gridInfo)xt(n,this.context,this.deviceScaleFactor,this.canvasWidth,this.canvasHeight,this.emulationScaleFactor,this.gridLabelState);if(t.flexInfo)for(const n of t.flexInfo)zt(n,this.context,this.deviceScaleFactor,this.canvasWidth,this.canvasHeight,this.emulationScaleFactor);if(t.containerQueryInfo)for(const n of t.containerQueryInfo)Mt(n,this.context,this.emulationScaleFactor);const u=t.flexInfo?.length&&t.flexInfo.some((t=>Object.keys(t.flexContainerHighlightConfig).length>0));if(t.flexItemInfo&&!u)for(const n of t.flexItemInfo){const t="content"===n.boxSizing?a:c;t&&Yt(n,t,this.context,this.deviceScaleFactor,this.canvasWidth,this.canvasHeight,this.emulationScaleFactor)}return this.context.restore(),{bounds:n}}drawGridHighlight(t){this.persistentOverlay&&this.persistentOverlay.drawGridHighlight(t)}drawFlexContainerHighlight(t){this.persistentOverlay&&this.persistentOverlay.drawFlexContainerHighlight(t)}drawScrollSnapHighlight(t){this.persistentOverlay?.drawScrollSnapHighlight(t)}drawContainerQueryHighlight(t){this.persistentOverlay?.drawContainerQueryHighlight(t)}drawIsolatedElementHighlight(t){this.persistentOverlay?.drawIsolatedElementHighlight(t)}drawAxis(t,n,e){t.save();const o=this.pageZoomFactor*this.pageScaleFactor*this.emulationScaleFactor,i=this.scrollX*this.pageScaleFactor,r=this.scrollY*this.pageScaleFactor;function s(t){return Math.round(t*o)}function a(t){return Math.round(t/o)}const l=this.canvasWidth/o,c=this.canvasHeight/o,d=50;t.save(),t.fillStyle=ln,e?t.fillRect(0,s(c)-15,s(l),s(c)):t.fillRect(0,0,s(l),15),t.globalCompositeOperation="destination-out",t.fillStyle="red",n?t.fillRect(s(l)-15,0,s(l),s(c)):t.fillRect(0,0,15,s(c)),t.restore(),t.fillStyle=ln,n?t.fillRect(s(l)-15,0,s(l),s(c)):t.fillRect(0,0,15,s(c)),t.lineWidth=1,t.strokeStyle=an,t.fillStyle=an;{t.save(),t.translate(-i,.5-r);const o=c+a(r);for(let e=100;e<o;e+=100)t.save(),t.translate(i,s(e)),t.rotate(-Math.PI/2),t.fillText(String(e),2,n?s(l)-7:13),t.restore();t.translate(.5,-.5);const d=l+a(i);for(let n=100;n<d;n+=100)t.save(),t.fillText(String(n),s(n)+2,e?r+s(c)-7:r+13),t.restore();t.restore()}{t.save(),n&&(t.translate(s(l),0),t.scale(-1,1)),t.translate(-i,.5-r);const e=c+a(r);for(let n=d;n<e;n+=d){t.beginPath(),t.moveTo(i,s(n));const e=n%100?5:8;t.lineTo(i+e,s(n)),t.stroke()}t.strokeStyle=sn;for(let n=5;n<e;n+=5)n%d&&(t.beginPath(),t.moveTo(i,s(n)),t.lineTo(i+5,s(n)),t.stroke());t.restore()}{t.save(),e&&(t.translate(0,s(c)),t.scale(1,-1)),t.translate(.5-i,-r);const n=l+a(i);for(let e=d;e<n;e+=d){t.beginPath(),t.moveTo(s(e),r);const n=e%100?5:8;t.lineTo(s(e),r+n),t.stroke()}t.strokeStyle=sn;for(let e=5;e<n;e+=5)e%d&&(t.beginPath(),t.moveTo(s(e),r),t.lineTo(s(e),r+5),t.stroke());t.restore()}t.restore()}}(window,[At,Cn]),Ln=new on(window,[wt,Cn]),kn=new class extends n{drawDistances({distanceInfo:t}){if(!t)return;const n=(e=function(t){const n=t.style;return e(n)?t.border:o(n)?t.padding:t.content;function e(t){const n=["top","right","bottom","left"];for(const e of n){const n=t[`border-${e}-width`],o=t[`border-${e}-style`],i=t[`border-${e}-color`];if("0px"!==n&&"none"!==o&&!i.endsWith("00"))return!0}const e=t["outline-width"],o=t["outline-style"],i=t["outline-color"];return"0px"!==e&&"none"!==o&&!i.endsWith("00")||"none"!==t["box-shadow"]}function o(t){const n=t["background-color"],e=t["background-image"];return!n.startsWith("#FFFFFF")&&!n.endsWith("00")||"none"!==e}}(t),{x:e[0],y:e[1],w:e[4]-e[0],h:e[5]-e[1]});var e;this.context.save(),this.context.strokeStyle="#ccc";for(const n of t.boxes)this.context.strokeRect(n[0],n[1],n[2],n[3]);this.context.strokeStyle="#f00",this.context.lineWidth=1,this.context.rect(n.x-.5,n.y-.5,n.w+1,n.h+1),this.context.stroke(),this.context.restore()}install(){this.document.body.classList.add("fill");const t=this.document.createElement("canvas");t.id="canvas",t.classList.add("fill"),this.document.body.append(t),this.setCanvas(t),super.install()}uninstall(){this.document.body.classList.remove("fill"),this.document.body.innerHTML="",super.uninstall()}}(window),En=new class extends n{container;constructor(t,n=[]){super(t,n),this.onKeyDown=this.onKeyDown.bind(this)}onKeyDown(t){"F8"===t.key||this.eventHasCtrlOrMeta(t)&&"\\"===t.key?this.window.InspectorOverlayHost.send("resume"):("F10"===t.key||this.eventHasCtrlOrMeta(t)&&"'"===t.key)&&this.window.InspectorOverlayHost.send("stepOver")}install(){const t=this.document.createElement("div");t.classList.add("controls-line");const n=this.document.createElement("div");n.classList.add("message-box");const e=this.document.createElement("div");e.id="paused-in-debugger",this.container=e,n.append(e),t.append(n);const o=this.document.createElement("div");o.id="resume-button",o.title="Resume script execution (F8).",o.classList.add("button");const i=this.document.createElement("div");i.classList.add("glyph"),o.append(i),t.append(o);const r=this.document.createElement("div");r.id="step-over-button",r.title="Step over next function call (F10).",r.classList.add("button");const s=this.document.createElement("div");s.classList.add("glyph"),r.append(s),t.append(r),this.document.body.append(t),this.document.addEventListener("keydown",this.onKeyDown),o.addEventListener("click",(()=>this.window.InspectorOverlayHost.send("resume"))),r.addEventListener("click",(()=>this.window.InspectorOverlayHost.send("stepOver"))),super.install()}uninstall(){this.document.body.innerHTML="",this.document.removeEventListener("keydown",this.onKeyDown),super.uninstall()}drawPausedInDebuggerMessage(t){this.container.textContent=t}}(window,dn),Fn=new class extends n{zone;constructor(t,n=[]){super(t,n),this.onMouseDown=this.onMouseDown.bind(this),this.onMouseUp=this.onMouseUp.bind(this),this.onMouseMove=this.onMouseMove.bind(this),this.onKeyDown=this.onKeyDown.bind(this)}install(){const t=this.document.documentElement;t.addEventListener("mousedown",this.onMouseDown,!0),t.addEventListener("mouseup",this.onMouseUp,!0),t.addEventListener("mousemove",this.onMouseMove,!0),t.addEventListener("keydown",this.onKeyDown,!0);const n=this.document.createElement("div");n.id="zone",this.document.body.append(n),this.zone=n,super.install()}uninstall(){this.document.body.innerHTML="";const t=this.document.documentElement;t.removeEventListener("mousedown",this.onMouseDown,!0),t.removeEventListener("mouseup",this.onMouseUp,!0),t.removeEventListener("mousemove",this.onMouseMove,!0),t.removeEventListener("keydown",this.onKeyDown,!0),super.uninstall()}onMouseDown(t){pn={x:t.pageX,y:t.pageY},un=pn,this.updateZone(),t.stopPropagation(),t.preventDefault()}onMouseUp(t){if(pn&&un){const t=gn();t.width>=5&&t.height>=5&&this.window.InspectorOverlayHost.send(t)}mn(),this.updateZone(),t.stopPropagation(),t.preventDefault()}onMouseMove(t){pn&&1===t.buttons?un={x:t.pageX,y:t.pageY}:pn=null,this.updateZone(),t.stopPropagation(),t.preventDefault()}onKeyDown(t){pn&&"Escape"===t.key&&(mn(),this.updateZone(),t.stopPropagation(),t.preventDefault())}updateZone(){const t=this.zone;if(!un||!pn)return void(t.style.display="none");t.style.display="block";const n=gn();t.style.left=n.x+"px",t.style.top=n.y+"px",t.style.width=n.width+"px",t.style.height=n.height+"px"}}(window,hn),Pn=new class extends n{sourceOrderContainer;reset(t){super.reset(t),this.sourceOrderContainer.textContent=""}install(){this.document.body.classList.add("fill");const t=this.document.createElement("canvas");t.id="canvas",t.classList.add("fill"),this.document.body.append(t);const n=this.document.createElement("div");n.id="source-order-container",this.document.body.append(n),this.sourceOrderContainer=n,this.setCanvas(t),super.install()}uninstall(){this.document.body.classList.remove("fill"),this.document.body.innerHTML="",super.uninstall()}drawSourceOrder(t){const n=t.sourceOrder||0,e=t.paths.slice().pop();if(!e)throw new Error("No path provided");this.context.save();const o=Y(),i=e.outlineColor;return this.context.save(),function(t,n,e,o,i,r){t.save();const s=H(n,i,r);e&&(t.strokeStyle=e,t.lineWidth=2,o||t.setLineDash([3,3]),t.stroke(s));t.restore()}(this.context,e.path,i,Boolean(n),o,this.emulationScaleFactor),this.context.restore(),this.context.save(),Boolean(n)&&this.drawSourceOrderLabel(n,i,o),this.context.restore(),{bounds:o}}drawSourceOrderLabel(t,n,o){const i=this.sourceOrderContainer,r=i.children,s=e(i,"div","source-order-label-container");s.style.color=n,s.textContent=String(t);const a=s.offsetHeight,l=function(t,n,e,o,i){let r;const s=t.minX+e>t.maxX,a=t.minY+n>t.maxY;if(!s&&!a||o.length>=xn)return bn.topCorner;let l=!1;for(let i=0;i<o.length;i++){const r=o[i],s=r.getBoundingClientRect();if(""===r.style.top&&""===r.style.left)continue;const a=t.minY-n<=s.top+s.height&&t.minY-n>=s.top,c=t.minY<=s.top+s.height&&t.minY>=s.top,d=t.minX>=s.left&&t.minX<=s.left+s.width,h=t.minX+e>=s.left&&t.minX+e<=s.left+s.width;if((d||h)&&(a||c)){l=!0;break}}t.minY-n>0&&!l?(r=bn.aboveElement,s&&(r=bn.aboveElementWider)):t.maxY+n<i?(r=bn.belowElement,s&&(r=bn.belowElementWider)):r=s&&a?bn.bottomCornerWiderTaller:s?bn.bottomCornerWider:bn.bottomCornerTaller;return r}(o,a,s.offsetWidth,r,this.canvasHeight),c=function(t,n,e){let o=0;switch(t){case bn.topCorner:o=n.minY;break;case bn.aboveElement:case bn.aboveElementWider:o=n.minY-e;break;case bn.belowElement:case bn.belowElementWider:o=n.maxY;break;case bn.bottomCornerWider:case bn.bottomCornerTaller:case bn.bottomCornerWiderTaller:o=n.maxY-e}return{contentTop:o,contentLeft:n.minX}}(l,o,a);s.classList.add(l),s.style.top=c.contentTop+"px",s.style.left=c.contentLeft+"px"}}(window,fn),Hn=new class extends n{install(){this.document.body.classList.add("fill");const t=this.document.createElement("canvas");t.id="canvas",t.classList.add("fill"),this.document.body.append(t),this.setCanvas(t),super.install()}uninstall(){this.document.body.classList.remove("fill"),this.document.body.innerHTML="",super.uninstall()}drawViewSize(){const t=this.viewportSizeForMediaQueries||this.viewportSize,n=`${yn(t.width)}px × ${yn(t.height)}px`,e=this.canvasWidth||0;this.context.save(),this.context.font=`14px ${this.window.getComputedStyle(document.body).fontFamily}`;const o=this.context.measureText(n).width;this.context.fillStyle="rgba(255 255 255 / 0.8)",this.context.fillRect(e-o-12,0,e,25),this.context.fillStyle="rgba(0 0 0 / 0.7)",this.context.fillText(n,e-o-6,18),this.context.restore()}}(window),Yn=new class extends n{windowsToolBar;linuxToolBar;macToolbarRight;macToolbarLeft;constructor(t,n=[]){super(t,n)}install(){const t=["chevron","ellipsis","minimize","maximize","close"];this.windowsToolBar=Mn("windows","right",t),this.linuxToolBar=Mn("linux","right",t),this.macToolbarRight=Mn("mac","right",["mac-chevron","mac-ellipsis"]),this.macToolbarLeft=Mn("mac","left",["mac-close","mac-minimize","mac-maximize"]),this.document.body.append(this.windowsToolBar,this.linuxToolBar,this.macToolbarLeft,this.macToolbarRight),super.install()}uninstall(){this.document.body.innerHTML="",super.uninstall()}drawWindowControlsOverlay(t){this.clearOverlays(),"Windows"===t.selectedPlatform?An(this.windowsToolBar):"Linux"===t.selectedPlatform?An(this.linuxToolBar):"Mac"===t.selectedPlatform&&(An(this.macToolbarLeft),An(this.macToolbarRight)),this.document.documentElement.style.setProperty("--wco-theme-color",t.themeColor),this.document.documentElement.style.setProperty("--wco-icon-color",gt(t.themeColor))}clearOverlays(){wn(this.linuxToolBar),wn(this.windowsToolBar),wn(this.macToolbarLeft),wn(this.macToolbarRight)}}(window,[vn]),zn={distances:kn,highlight:Sn,persistent:Ln,paused:En,screenshot:Fn,sourceOrder:Pn,viewportSize:Hn,windowControlsOverlay:Yn};let Bn,Wn;window.dispatch=t=>{const n=t[0];if("setOverlay"===n){const n=t[1];Bn&&Bn.uninstall(),Bn=zn[n],Bn.setPlatform(Wn),Bn.installed||Bn.install()}else"setPlatform"===n?Wn=t[1]:"drawingFinished"===n||Bn.dispatch(t)}}();
