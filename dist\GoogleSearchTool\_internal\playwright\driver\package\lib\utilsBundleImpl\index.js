"use strict";var Hb=Object.create;var ms=Object.defineProperty;var Gb=Object.getOwnPropertyDescriptor;var Yb=Object.getOwnPropertyNames;var Wb=Object.getPrototypeOf,zb=Object.prototype.hasOwnProperty;var w=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Ef=(t,e)=>{for(var i in e)ms(t,i,{get:e[i],enumerable:!0})},kf=(t,e,i,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Yb(e))!zb.call(t,n)&&n!==i&&ms(t,n,{get:()=>e[n],enumerable:!(r=Gb(e,n))||r.enumerable});return t};var De=(t,e,i)=>(i=t!=null?Hb(Wb(t)):{},kf(e||!t||!t.__esModule?ms(i,"default",{value:t,enumerable:!0}):i,t)),Kb=t=>kf(ms({},"__esModule",{value:!0}),t);var Af=w((AI,Tf)=>{var Cf={};Tf.exports=Cf;var Of={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(Of).forEach(function(t){var e=Of[t],i=Cf[t]=[];i.open="\x1B["+e[0]+"m",i.close="\x1B["+e[1]+"m"})});var Nf=w((II,If)=>{"use strict";If.exports=function(t,e){e=e||process.argv;var i=e.indexOf("--"),r=/^-{1,2}/.test(t)?"":"--",n=e.indexOf(r+t);return n!==-1&&(i===-1?!0:n<i)}});var Bf=w((NI,Lf)=>{"use strict";var Jb=require("os"),Pt=Nf(),ot=process.env,dr=void 0;Pt("no-color")||Pt("no-colors")||Pt("color=false")?dr=!1:(Pt("color")||Pt("colors")||Pt("color=true")||Pt("color=always"))&&(dr=!0);"FORCE_COLOR"in ot&&(dr=ot.FORCE_COLOR.length===0||parseInt(ot.FORCE_COLOR,10)!==0);function Zb(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function Qb(t){if(dr===!1)return 0;if(Pt("color=16m")||Pt("color=full")||Pt("color=truecolor"))return 3;if(Pt("color=256"))return 2;if(t&&!t.isTTY&&dr!==!0)return 0;var e=dr?1:0;if(process.platform==="win32"){var i=Jb.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in ot)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(n){return n in ot})||ot.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in ot)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(ot.TEAMCITY_VERSION)?1:0;if("TERM_PROGRAM"in ot){var r=parseInt((ot.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(ot.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(ot.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(ot.TERM)||"COLORTERM"in ot?1:(ot.TERM==="dumb",e)}function wa(t){var e=Qb(t);return Zb(e)}Lf.exports={supportsColor:wa,stdout:wa(process.stdout),stderr:wa(process.stderr)}});var Pf=w((LI,Rf)=>{Rf.exports=function(e,i){var r="";e=e||"Run the trap, drop the bass",e=e.split("");var n={a:["@","\u0104","\u023A","\u0245","\u0394","\u039B","\u0414"],b:["\xDF","\u0181","\u0243","\u026E","\u03B2","\u0E3F"],c:["\xA9","\u023B","\u03FE"],d:["\xD0","\u018A","\u0500","\u0501","\u0502","\u0503"],e:["\xCB","\u0115","\u018E","\u0258","\u03A3","\u03BE","\u04BC","\u0A6C"],f:["\u04FA"],g:["\u0262"],h:["\u0126","\u0195","\u04A2","\u04BA","\u04C7","\u050A"],i:["\u0F0F"],j:["\u0134"],k:["\u0138","\u04A0","\u04C3","\u051E"],l:["\u0139"],m:["\u028D","\u04CD","\u04CE","\u0520","\u0521","\u0D69"],n:["\xD1","\u014B","\u019D","\u0376","\u03A0","\u048A"],o:["\xD8","\xF5","\xF8","\u01FE","\u0298","\u047A","\u05DD","\u06DD","\u0E4F"],p:["\u01F7","\u048E"],q:["\u09CD"],r:["\xAE","\u01A6","\u0210","\u024C","\u0280","\u042F"],s:["\xA7","\u03DE","\u03DF","\u03E8"],t:["\u0141","\u0166","\u0373"],u:["\u01B1","\u054D"],v:["\u05D8"],w:["\u0428","\u0460","\u047C","\u0D70"],x:["\u04B2","\u04FE","\u04FC","\u04FD"],y:["\xA5","\u04B0","\u04CB"],z:["\u01B5","\u0240"]};return e.forEach(function(s){s=s.toLowerCase();var o=n[s]||[" "],a=Math.floor(Math.random()*o.length);typeof n[s]!="undefined"?r+=n[s][a]:r+=s}),r}});var Ff=w((BI,Mf)=>{Mf.exports=function(e,i){e=e||"   he is here   ";var r={up:["\u030D","\u030E","\u0304","\u0305","\u033F","\u0311","\u0306","\u0310","\u0352","\u0357","\u0351","\u0307","\u0308","\u030A","\u0342","\u0313","\u0308","\u034A","\u034B","\u034C","\u0303","\u0302","\u030C","\u0350","\u0300","\u0301","\u030B","\u030F","\u0312","\u0313","\u0314","\u033D","\u0309","\u0363","\u0364","\u0365","\u0366","\u0367","\u0368","\u0369","\u036A","\u036B","\u036C","\u036D","\u036E","\u036F","\u033E","\u035B","\u0346","\u031A"],down:["\u0316","\u0317","\u0318","\u0319","\u031C","\u031D","\u031E","\u031F","\u0320","\u0324","\u0325","\u0326","\u0329","\u032A","\u032B","\u032C","\u032D","\u032E","\u032F","\u0330","\u0331","\u0332","\u0333","\u0339","\u033A","\u033B","\u033C","\u0345","\u0347","\u0348","\u0349","\u034D","\u034E","\u0353","\u0354","\u0355","\u0356","\u0359","\u035A","\u0323"],mid:["\u0315","\u031B","\u0300","\u0301","\u0358","\u0321","\u0322","\u0327","\u0328","\u0334","\u0335","\u0336","\u035C","\u035D","\u035E","\u035F","\u0360","\u0362","\u0338","\u0337","\u0361"," \u0489"]},n=[].concat(r.up,r.down,r.mid);function s(l){var c=Math.floor(Math.random()*l);return c}function o(l){var c=!1;return n.filter(function(u){c=u===l}),c}function a(l,c){var u="",f,d;c=c||{},c.up=typeof c.up!="undefined"?c.up:!0,c.mid=typeof c.mid!="undefined"?c.mid:!0,c.down=typeof c.down!="undefined"?c.down:!0,c.size=typeof c.size!="undefined"?c.size:"maxi",l=l.split("");for(d in l)if(!o(d)){switch(u=u+l[d],f={up:0,down:0,mid:0},c.size){case"mini":f.up=s(8),f.mid=s(2),f.down=s(8);break;case"maxi":f.up=s(16)+3,f.mid=s(4)+1,f.down=s(64)+3;break;default:f.up=s(8)+1,f.mid=s(6)/2,f.down=s(8)+1;break}var g=["up","mid","down"];for(var m in g)for(var v=g[m],b=0;b<=f[v];b++)c[v]&&(u=u+r[v][s(r[v].length)])}return u}return a(e,i)}});var Df=w((RI,qf)=>{qf.exports=function(t){return function(e,i,r){if(e===" ")return e;switch(i%3){case 0:return t.red(e);case 1:return t.white(e);case 2:return t.blue(e)}}}});var Uf=w((PI,jf)=>{jf.exports=function(t){return function(e,i,r){return i%2===0?e:t.inverse(e)}}});var Vf=w((MI,$f)=>{$f.exports=function(t){var e=["red","yellow","green","blue","magenta"];return function(i,r,n){return i===" "?i:t[e[r++%e.length]](i)}}});var Gf=w((FI,Hf)=>{Hf.exports=function(t){var e=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(i,r,n){return i===" "?i:t[e[Math.round(Math.random()*(e.length-2))]](i)}}});var Zf=w((DI,Jf)=>{var ye={};Jf.exports=ye;ye.themes={};var Xb=require("util"),Di=ye.styles=Af(),Wf=Object.defineProperties,e_=new RegExp(/[\r\n]+/g);ye.supportsColor=Bf().supportsColor;typeof ye.enabled=="undefined"&&(ye.enabled=ye.supportsColor()!==!1);ye.enable=function(){ye.enabled=!0};ye.disable=function(){ye.enabled=!1};ye.stripColors=ye.strip=function(t){return(""+t).replace(/\x1B\[\d+m/g,"")};var qI=ye.stylize=function(e,i){if(!ye.enabled)return e+"";var r=Di[i];return!r&&i in ye?ye[i](e):r.open+e+r.close},t_=/[|\\{}()[\]^$+*?.]/g,i_=function(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(t_,"\\$&")};function zf(t){var e=function i(){return n_.apply(i,arguments)};return e._styles=t,e.__proto__=r_,e}var Kf=function(){var t={};return Di.grey=Di.gray,Object.keys(Di).forEach(function(e){Di[e].closeRe=new RegExp(i_(Di[e].close),"g"),t[e]={get:function(){return zf(this._styles.concat(e))}}}),t}(),r_=Wf(function(){},Kf);function n_(){var t=Array.prototype.slice.call(arguments),e=t.map(function(o){return o!=null&&o.constructor===String?o:Xb.inspect(o)}).join(" ");if(!ye.enabled||!e)return e;for(var i=e.indexOf(`
`)!=-1,r=this._styles,n=r.length;n--;){var s=Di[r[n]];e=s.open+e.replace(s.closeRe,s.open)+s.close,i&&(e=e.replace(e_,function(o){return s.close+o+s.open}))}return e}ye.setTheme=function(t){if(typeof t=="string"){console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");return}for(var e in t)(function(i){ye[i]=function(r){if(typeof t[i]=="object"){var n=r;for(var s in t[i])n=ye[t[i][s]](n);return n}return ye[t[i]](r)}})(e)};function s_(){var t={};return Object.keys(Kf).forEach(function(e){t[e]={get:function(){return zf([e])}}}),t}var o_=function(e,i){var r=i.split("");return r=r.map(e),r.join("")};ye.trap=Pf();ye.zalgo=Ff();ye.maps={};ye.maps.america=Df()(ye);ye.maps.zebra=Uf()(ye);ye.maps.rainbow=Vf()(ye);ye.maps.random=Gf()(ye);for(Yf in ye.maps)(function(t){ye[t]=function(e){return o_(ye.maps[t],e)}})(Yf);var Yf;Wf(ye,s_())});var Xf=w((jI,Qf)=>{var a_=Zf();Qf.exports=a_});var th=w((UI,eh)=>{var mr=1e3,gr=mr*60,vr=gr*60,ji=vr*24,l_=ji*7,c_=ji*365.25;eh.exports=function(t,e){e=e||{};var i=typeof t;if(i==="string"&&t.length>0)return u_(t);if(i==="number"&&isFinite(t))return e.long?h_(t):f_(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function u_(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var i=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return i*c_;case"weeks":case"week":case"w":return i*l_;case"days":case"day":case"d":return i*ji;case"hours":case"hour":case"hrs":case"hr":case"h":return i*vr;case"minutes":case"minute":case"mins":case"min":case"m":return i*gr;case"seconds":case"second":case"secs":case"sec":case"s":return i*mr;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}function f_(t){var e=Math.abs(t);return e>=ji?Math.round(t/ji)+"d":e>=vr?Math.round(t/vr)+"h":e>=gr?Math.round(t/gr)+"m":e>=mr?Math.round(t/mr)+"s":t+"ms"}function h_(t){var e=Math.abs(t);return e>=ji?gs(t,e,ji,"day"):e>=vr?gs(t,e,vr,"hour"):e>=gr?gs(t,e,gr,"minute"):e>=mr?gs(t,e,mr,"second"):t+" ms"}function gs(t,e,i,r){var n=e>=i*1.5;return Math.round(t/i)+" "+r+(n?"s":"")}});var xa=w(($I,ih)=>{function p_(t){i.debug=i,i.default=i,i.coerce=l,i.disable=s,i.enable=n,i.enabled=o,i.humanize=th(),i.destroy=c,Object.keys(t).forEach(u=>{i[u]=t[u]}),i.names=[],i.skips=[],i.formatters={};function e(u){let f=0;for(let d=0;d<u.length;d++)f=(f<<5)-f+u.charCodeAt(d),f|=0;return i.colors[Math.abs(f)%i.colors.length]}i.selectColor=e;function i(u){let f,d=null,g,m;function v(...b){if(!v.enabled)return;let _=v,S=Number(new Date),O=S-(f||S);_.diff=O,_.prev=f,_.curr=S,f=S,b[0]=i.coerce(b[0]),typeof b[0]!="string"&&b.unshift("%O");let k=0;b[0]=b[0].replace(/%([a-zA-Z%])/g,(R,T)=>{if(R==="%%")return"%";k++;let A=i.formatters[T];if(typeof A=="function"){let C=b[k];R=A.call(_,C),b.splice(k,1),k--}return R}),i.formatArgs.call(_,b),(_.log||i.log).apply(_,b)}return v.namespace=u,v.useColors=i.useColors(),v.color=i.selectColor(u),v.extend=r,v.destroy=i.destroy,Object.defineProperty(v,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(g!==i.namespaces&&(g=i.namespaces,m=i.enabled(u)),m),set:b=>{d=b}}),typeof i.init=="function"&&i.init(v),v}function r(u,f){let d=i(this.namespace+(typeof f=="undefined"?":":f)+u);return d.log=this.log,d}function n(u){i.save(u),i.namespaces=u,i.names=[],i.skips=[];let f,d=(typeof u=="string"?u:"").split(/[\s,]+/),g=d.length;for(f=0;f<g;f++)d[f]&&(u=d[f].replace(/\*/g,".*?"),u[0]==="-"?i.skips.push(new RegExp("^"+u.slice(1)+"$")):i.names.push(new RegExp("^"+u+"$")))}function s(){let u=[...i.names.map(a),...i.skips.map(a).map(f=>"-"+f)].join(",");return i.enable(""),u}function o(u){if(u[u.length-1]==="*")return!0;let f,d;for(f=0,d=i.skips.length;f<d;f++)if(i.skips[f].test(u))return!1;for(f=0,d=i.names.length;f<d;f++)if(i.names[f].test(u))return!0;return!1}function a(u){return u.toString().substring(2,u.toString().length-2).replace(/\.\*\?$/,"*")}function l(u){return u instanceof Error?u.stack||u.message:u}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return i.enable(i.load()),i}ih.exports=p_});var rh=w((_t,vs)=>{_t.formatArgs=m_;_t.save=g_;_t.load=v_;_t.useColors=d_;_t.storage=y_();_t.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();_t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function d_(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function m_(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+vs.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let i=0,r=0;t[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(i++,n==="%c"&&(r=i))}),t.splice(r,0,e)}_t.log=console.debug||console.log||(()=>{});function g_(t){try{t?_t.storage.setItem("debug",t):_t.storage.removeItem("debug")}catch{}}function v_(){let t;try{t=_t.storage.getItem("debug")}catch{}return!t&&typeof process!="undefined"&&"env"in process&&(t=process.env.DEBUG),t}function y_(){try{return localStorage}catch{}}vs.exports=xa()(_t);var{formatters:b_}=vs.exports;b_.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var sh=w((VI,nh)=>{"use strict";nh.exports=(t,e)=>{e=e||process.argv;let i=t.startsWith("-")?"":t.length===1?"-":"--",r=e.indexOf(i+t),n=e.indexOf("--");return r!==-1&&(n===-1?!0:r<n)}});var ah=w((HI,oh)=>{"use strict";var __=require("os"),Mt=sh(),tt=process.env,yr;Mt("no-color")||Mt("no-colors")||Mt("color=false")?yr=!1:(Mt("color")||Mt("colors")||Mt("color=true")||Mt("color=always"))&&(yr=!0);"FORCE_COLOR"in tt&&(yr=tt.FORCE_COLOR.length===0||parseInt(tt.FORCE_COLOR,10)!==0);function w_(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function x_(t){if(yr===!1)return 0;if(Mt("color=16m")||Mt("color=full")||Mt("color=truecolor"))return 3;if(Mt("color=256"))return 2;if(t&&!t.isTTY&&yr!==!0)return 0;let e=yr?1:0;if(process.platform==="win32"){let i=__.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in tt)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(i=>i in tt)||tt.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in tt)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(tt.TEAMCITY_VERSION)?1:0;if(tt.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in tt){let i=parseInt((tt.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(tt.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(tt.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(tt.TERM)||"COLORTERM"in tt?1:(tt.TERM==="dumb",e)}function Sa(t){let e=x_(t);return w_(e)}oh.exports={supportsColor:Sa,stdout:Sa(process.stdout),stderr:Sa(process.stderr)}});var ch=w((Ze,bs)=>{var S_=require("tty"),ys=require("util");Ze.init=I_;Ze.log=C_;Ze.formatArgs=k_;Ze.save=T_;Ze.load=A_;Ze.useColors=E_;Ze.destroy=ys.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");Ze.colors=[6,2,3,4,5,1];try{let t=ah();t&&(t.stderr||t).level>=2&&(Ze.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}Ze.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let i=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),t[i]=r,t},{});function E_(){return"colors"in Ze.inspectOpts?!!Ze.inspectOpts.colors:S_.isatty(process.stderr.fd)}function k_(t){let{namespace:e,useColors:i}=this;if(i){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),s=`  ${n};1m${e} \x1B[0m`;t[0]=s+t[0].split(`
`).join(`
`+s),t.push(n+"m+"+bs.exports.humanize(this.diff)+"\x1B[0m")}else t[0]=O_()+e+" "+t[0]}function O_(){return Ze.inspectOpts.hideDate?"":new Date().toISOString()+" "}function C_(...t){return process.stderr.write(ys.format(...t)+`
`)}function T_(t){t?process.env.DEBUG=t:delete process.env.DEBUG}function A_(){return process.env.DEBUG}function I_(t){t.inspectOpts={};let e=Object.keys(Ze.inspectOpts);for(let i=0;i<e.length;i++)t.inspectOpts[e[i]]=Ze.inspectOpts[e[i]]}bs.exports=xa()(Ze);var{formatters:lh}=bs.exports;lh.o=function(t){return this.inspectOpts.colors=this.useColors,ys.inspect(t,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};lh.O=function(t){return this.inspectOpts.colors=this.useColors,ys.inspect(t,this.inspectOpts)}});var br=w((GI,Ea)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?Ea.exports=rh():Ea.exports=ch()});var Rh=w((YI,hw)=>{hw.exports={name:"dotenv",version:"16.4.5",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard","lint-readme":"standard-markdown",pretest:"npm run lint && npm run dts-check",test:"tap tests/*.js --100 -Rspec","test:coverage":"tap --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@definitelytyped/dtslint":"^0.0.133","@types/node":"^18.11.3",decache:"^4.6.1",sinon:"^14.0.1",standard:"^17.0.0","standard-markdown":"^7.1.0","standard-version":"^9.5.0",tap:"^16.3.0",tar:"^6.1.11",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var qh=w((WI,ii)=>{var Ra=require("fs"),Pa=require("path"),pw=require("os"),dw=require("crypto"),mw=Rh(),Ma=mw.version,gw=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function vw(t){let e={},i=t.toString();i=i.replace(/\r\n?/mg,`
`);let r;for(;(r=gw.exec(i))!=null;){let n=r[1],s=r[2]||"";s=s.trim();let o=s[0];s=s.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),o==='"'&&(s=s.replace(/\\n/g,`
`),s=s.replace(/\\r/g,"\r")),e[n]=s}return e}function yw(t){let e=Fh(t),i=$e.configDotenv({path:e});if(!i.parsed){let o=new Error(`MISSING_DATA: Cannot parse ${e} for an unknown reason`);throw o.code="MISSING_DATA",o}let r=Mh(t).split(","),n=r.length,s;for(let o=0;o<n;o++)try{let a=r[o].trim(),l=ww(i,a);s=$e.decrypt(l.ciphertext,l.key);break}catch(a){if(o+1>=n)throw a}return $e.parse(s)}function bw(t){console.log(`[dotenv@${Ma}][INFO] ${t}`)}function _w(t){console.log(`[dotenv@${Ma}][WARN] ${t}`)}function Ts(t){console.log(`[dotenv@${Ma}][DEBUG] ${t}`)}function Mh(t){return t&&t.DOTENV_KEY&&t.DOTENV_KEY.length>0?t.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function ww(t,e){let i;try{i=new URL(e)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let r=i.password;if(!r){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let n=i.searchParams.get("environment");if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let s=`DOTENV_VAULT_${n.toUpperCase()}`,o=t.parsed[s];if(!o){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:o,key:r}}function Fh(t){let e=null;if(t&&t.path&&t.path.length>0)if(Array.isArray(t.path))for(let i of t.path)Ra.existsSync(i)&&(e=i.endsWith(".vault")?i:`${i}.vault`);else e=t.path.endsWith(".vault")?t.path:`${t.path}.vault`;else e=Pa.resolve(process.cwd(),".env.vault");return Ra.existsSync(e)?e:null}function Ph(t){return t[0]==="~"?Pa.join(pw.homedir(),t.slice(1)):t}function xw(t){bw("Loading env from encrypted .env.vault");let e=$e._parseVault(t),i=process.env;return t&&t.processEnv!=null&&(i=t.processEnv),$e.populate(i,e,t),{parsed:e}}function Sw(t){let e=Pa.resolve(process.cwd(),".env"),i="utf8",r=!!(t&&t.debug);t&&t.encoding?i=t.encoding:r&&Ts("No encoding is specified. UTF-8 is used by default");let n=[e];if(t&&t.path)if(!Array.isArray(t.path))n=[Ph(t.path)];else{n=[];for(let l of t.path)n.push(Ph(l))}let s,o={};for(let l of n)try{let c=$e.parse(Ra.readFileSync(l,{encoding:i}));$e.populate(o,c,t)}catch(c){r&&Ts(`Failed to load ${l} ${c.message}`),s=c}let a=process.env;return t&&t.processEnv!=null&&(a=t.processEnv),$e.populate(a,o,t),s?{parsed:o,error:s}:{parsed:o}}function Ew(t){if(Mh(t).length===0)return $e.configDotenv(t);let e=Fh(t);return e?$e._configVault(t):(_w(`You set DOTENV_KEY but you are missing a .env.vault file at ${e}. Did you forget to build it?`),$e.configDotenv(t))}function kw(t,e){let i=Buffer.from(e.slice(-64),"hex"),r=Buffer.from(t,"base64"),n=r.subarray(0,12),s=r.subarray(-16);r=r.subarray(12,-16);try{let o=dw.createDecipheriv("aes-256-gcm",i,n);return o.setAuthTag(s),`${o.update(r)}${o.final()}`}catch(o){let a=o instanceof RangeError,l=o.message==="Invalid key length",c=o.message==="Unsupported state or unable to authenticate data";if(a||l){let u=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw u.code="INVALID_DOTENV_KEY",u}else if(c){let u=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw u.code="DECRYPTION_FAILED",u}else throw o}}function Ow(t,e,i={}){let r=!!(i&&i.debug),n=!!(i&&i.override);if(typeof e!="object"){let s=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw s.code="OBJECT_REQUIRED",s}for(let s of Object.keys(e))Object.prototype.hasOwnProperty.call(t,s)?(n===!0&&(t[s]=e[s]),r&&Ts(n===!0?`"${s}" is already defined and WAS overwritten`:`"${s}" is already defined and was NOT overwritten`)):t[s]=e[s]}var $e={configDotenv:Sw,_configVault:xw,_parseVault:yw,config:Ew,decrypt:kw,parse:vw,populate:Ow};ii.exports.configDotenv=$e.configDotenv;ii.exports._configVault=$e._configVault;ii.exports._parseVault=$e._parseVault;ii.exports.config=$e.config;ii.exports.decrypt=$e.decrypt;ii.exports.parse=$e.parse;ii.exports.populate=$e.populate;ii.exports=$e});var jh=w(Dh=>{"use strict";var Cw=require("url").parse,Tw={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},Aw=String.prototype.endsWith||function(t){return t.length<=this.length&&this.indexOf(t,this.length-t.length)!==-1};function Iw(t){var e=typeof t=="string"?Cw(t):t||{},i=e.protocol,r=e.host,n=e.port;if(typeof r!="string"||!r||typeof i!="string"||(i=i.split(":",1)[0],r=r.replace(/:\d*$/,""),n=parseInt(n)||Tw[i]||0,!Nw(r,n)))return"";var s=wr("npm_config_"+i+"_proxy")||wr(i+"_proxy")||wr("npm_config_proxy")||wr("all_proxy");return s&&s.indexOf("://")===-1&&(s=i+"://"+s),s}function Nw(t,e){var i=(wr("npm_config_no_proxy")||wr("no_proxy")).toLowerCase();return i?i==="*"?!1:i.split(/[,\s]/).every(function(r){if(!r)return!0;var n=r.match(/^(.+):(\d+)$/),s=n?n[1]:r,o=n?parseInt(n[2]):0;return o&&o!==e?!0:/^[.*]/.test(s)?(s.charAt(0)==="*"&&(s=s.slice(1)),!Aw.call(t,s)):t!==s}):!0}function wr(t){return process.env[t.toLowerCase()]||process.env[t.toUpperCase()]||""}Dh.getProxyForUrl=Iw});var Uh=w(Fa=>{"use strict";Object.defineProperty(Fa,"__esModule",{value:!0});function Lw(t){return function(e,i){return new Promise((r,n)=>{t.call(this,e,i,(s,o)=>{s?n(s):r(o)})})}}Fa.default=Lw});var ja=w((Da,Vh)=>{"use strict";var $h=Da&&Da.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Bw=require("events"),Rw=$h(br()),Pw=$h(Uh()),nn=Rw.default("agent-base");function Mw(t){return!!t&&typeof t.addRequest=="function"}function qa(){let{stack:t}=new Error;return typeof t!="string"?!1:t.split(`
`).some(e=>e.indexOf("(https.js:")!==-1||e.indexOf("node:https:")!==-1)}function As(t,e){return new As.Agent(t,e)}(function(t){class e extends Bw.EventEmitter{constructor(r,n){super();let s=n;typeof r=="function"?this.callback=r:r&&(s=r),this.timeout=null,s&&typeof s.timeout=="number"&&(this.timeout=s.timeout),this.maxFreeSockets=1,this.maxSockets=1,this.maxTotalSockets=1/0,this.sockets={},this.freeSockets={},this.requests={},this.options={}}get defaultPort(){return typeof this.explicitDefaultPort=="number"?this.explicitDefaultPort:qa()?443:80}set defaultPort(r){this.explicitDefaultPort=r}get protocol(){return typeof this.explicitProtocol=="string"?this.explicitProtocol:qa()?"https:":"http:"}set protocol(r){this.explicitProtocol=r}callback(r,n,s){throw new Error('"agent-base" has no default implementation, you must subclass and override `callback()`')}addRequest(r,n){let s=Object.assign({},n);typeof s.secureEndpoint!="boolean"&&(s.secureEndpoint=qa()),s.host==null&&(s.host="localhost"),s.port==null&&(s.port=s.secureEndpoint?443:80),s.protocol==null&&(s.protocol=s.secureEndpoint?"https:":"http:"),s.host&&s.path&&delete s.path,delete s.agent,delete s.hostname,delete s._defaultAgent,delete s.defaultPort,delete s.createConnection,r._last=!0,r.shouldKeepAlive=!1;let o=!1,a=null,l=s.timeout||this.timeout,c=g=>{r._hadError||(r.emit("error",g),r._hadError=!0)},u=()=>{a=null,o=!0;let g=new Error(`A "socket" was not created for HTTP request before ${l}ms`);g.code="ETIMEOUT",c(g)},f=g=>{o||(a!==null&&(clearTimeout(a),a=null),c(g))},d=g=>{if(o)return;if(a!=null&&(clearTimeout(a),a=null),Mw(g)){nn("Callback returned another Agent instance %o",g.constructor.name),g.addRequest(r,s);return}if(g){g.once("free",()=>{this.freeSocket(g,s)}),r.onSocket(g);return}let m=new Error(`no Duplex stream was returned to agent-base for \`${r.method} ${r.path}\``);c(m)};if(typeof this.callback!="function"){c(new Error("`callback` is not defined"));return}this.promisifiedCallback||(this.callback.length>=3?(nn("Converting legacy callback function to promise"),this.promisifiedCallback=Pw.default(this.callback)):this.promisifiedCallback=this.callback),typeof l=="number"&&l>0&&(a=setTimeout(u,l)),"port"in s&&typeof s.port!="number"&&(s.port=Number(s.port));try{nn("Resolving socket for %o request: %o",s.protocol,`${r.method} ${r.path}`),Promise.resolve(this.promisifiedCallback(r,s)).then(d,f)}catch(g){Promise.reject(g).catch(f)}}freeSocket(r,n){nn("Freeing socket %o %o",r.constructor.name,n),r.destroy()}destroy(){nn("Destroying agent %o",this.constructor.name)}}t.Agent=e,t.prototype=t.Agent.prototype})(As||(As={}));Vh.exports=As});var Hh=w(on=>{"use strict";var Fw=on&&on.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(on,"__esModule",{value:!0});var qw=Fw(br()),sn=qw.default("https-proxy-agent:parse-proxy-response");function Dw(t){return new Promise((e,i)=>{let r=0,n=[];function s(){let f=t.read();f?u(f):t.once("readable",s)}function o(){t.removeListener("end",l),t.removeListener("error",c),t.removeListener("close",a),t.removeListener("readable",s)}function a(f){sn("onclose had error %o",f)}function l(){sn("onend")}function c(f){o(),sn("onerror %o",f),i(f)}function u(f){n.push(f),r+=f.length;let d=Buffer.concat(n,r);if(d.indexOf(`\r
\r
`)===-1){sn("have not received end of HTTP headers yet..."),s();return}let m=d.toString("ascii",0,d.indexOf(`\r
`)),v=+m.split(" ")[1];sn("got proxy server response: %o",m),e({statusCode:v,buffered:d})}t.on("error",c),t.on("close",a),t.on("end",l),s()})}on.default=Dw});var Wh=w($i=>{"use strict";var jw=$i&&$i.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function a(u){try{c(r.next(u))}catch(f){o(f)}}function l(u){try{c(r.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((r=r.apply(t,e||[])).next())})},xr=$i&&$i.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty($i,"__esModule",{value:!0});var Gh=xr(require("net")),Yh=xr(require("tls")),Uw=xr(require("url")),$w=xr(require("assert")),Vw=xr(br()),Hw=ja(),Gw=xr(Hh()),an=Vw.default("https-proxy-agent:agent"),Ua=class extends Hw.Agent{constructor(e){let i;if(typeof e=="string"?i=Uw.default.parse(e):i=e,!i)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");an("creating new HttpsProxyAgent instance: %o",i),super(i);let r=Object.assign({},i);this.secureProxy=i.secureProxy||zw(r.protocol),r.host=r.hostname||r.host,typeof r.port=="string"&&(r.port=parseInt(r.port,10)),!r.port&&r.host&&(r.port=this.secureProxy?443:80),this.secureProxy&&!("ALPNProtocols"in r)&&(r.ALPNProtocols=["http 1.1"]),r.host&&r.path&&(delete r.path,delete r.pathname),this.proxy=r}callback(e,i){return jw(this,void 0,void 0,function*(){let{proxy:r,secureProxy:n}=this,s;n?(an("Creating `tls.Socket`: %o",r),s=Yh.default.connect(r)):(an("Creating `net.Socket`: %o",r),s=Gh.default.connect(r));let o=Object.assign({},r.headers),l=`CONNECT ${`${i.host}:${i.port}`} HTTP/1.1\r
`;r.auth&&(o["Proxy-Authorization"]=`Basic ${Buffer.from(r.auth).toString("base64")}`);let{host:c,port:u,secureEndpoint:f}=i;Ww(u,f)||(c+=`:${u}`),o.Host=c,o.Connection="close";for(let b of Object.keys(o))l+=`${b}: ${o[b]}\r
`;let d=Gw.default(s);s.write(`${l}\r
`);let{statusCode:g,buffered:m}=yield d;if(g===200){if(e.once("socket",Yw),i.secureEndpoint){an("Upgrading socket connection to TLS");let b=i.servername||i.host;return Yh.default.connect(Object.assign(Object.assign({},Kw(i,"host","hostname","path","port")),{socket:s,servername:b}))}return s}s.destroy();let v=new Gh.default.Socket({writable:!1});return v.readable=!0,e.once("socket",b=>{an("replaying proxy buffer for failed request"),$w.default(b.listenerCount("data")>0),b.push(m),b.push(null)}),v})}};$i.default=Ua;function Yw(t){t.resume()}function Ww(t,e){return!!(!e&&t===80||e&&t===443)}function zw(t){return typeof t=="string"?/^https:?$/i.test(t):!1}function Kw(t,...e){let i={},r;for(r in t)e.includes(r)||(i[r]=t[r]);return i}});var Kh=w((Ha,zh)=>{"use strict";var Jw=Ha&&Ha.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},$a=Jw(Wh());function Va(t){return new $a.default(t)}(function(t){t.HttpsProxyAgent=$a.default,t.prototype=$a.default.prototype})(Va||(Va={}));zh.exports=Va});var Qh=w((QI,Is)=>{var Zh=Zh||function(t){return Buffer.from(t).toString("base64")};function Zw(t){var e=this,i=Math.round,r=Math.floor,n=new Array(64),s=new Array(64),o=new Array(64),a=new Array(64),l,c,u,f,d=new Array(65535),g=new Array(65535),m=new Array(64),v=new Array(64),b=[],_=0,S=7,O=new Array(64),k=new Array(64),E=new Array(64),R=new Array(256),T=new Array(2048),A,C=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],P=[0,1,2,3,4,5,6,7,8,9,10,11],U=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],q=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],H=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],j=[0,1,2,3,4,5,6,7,8,9,10,11],V=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],W=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function Q(I){for(var Z=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],te=0;te<64;te++){var ee=r((Z[te]*I+50)/100);ee<1?ee=1:ee>255&&(ee=255),n[C[te]]=ee}for(var le=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],ce=0;ce<64;ce++){var _e=r((le[ce]*I+50)/100);_e<1?_e=1:_e>255&&(_e=255),s[C[ce]]=_e}for(var we=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Be=0,Te=0;Te<8;Te++)for(var D=0;D<8;D++)o[Be]=1/(n[C[Be]]*we[Te]*we[D]*8),a[Be]=1/(s[C[Be]]*we[Te]*we[D]*8),Be++}function Y(I,Z){for(var te=0,ee=0,le=new Array,ce=1;ce<=16;ce++){for(var _e=1;_e<=I[ce];_e++)le[Z[ee]]=[],le[Z[ee]][0]=te,le[Z[ee]][1]=ce,ee++,te++;te*=2}return le}function de(){l=Y(L,P),c=Y(H,j),u=Y(U,q),f=Y(V,W)}function ae(){for(var I=1,Z=2,te=1;te<=15;te++){for(var ee=I;ee<Z;ee++)g[32767+ee]=te,d[32767+ee]=[],d[32767+ee][1]=te,d[32767+ee][0]=ee;for(var le=-(Z-1);le<=-I;le++)g[32767+le]=te,d[32767+le]=[],d[32767+le][1]=te,d[32767+le][0]=Z-1+le;I<<=1,Z<<=1}}function ne(){for(var I=0;I<256;I++)T[I]=19595*I,T[I+256>>0]=38470*I,T[I+512>>0]=7471*I+32768,T[I+768>>0]=-11059*I,T[I+1024>>0]=-21709*I,T[I+1280>>0]=32768*I+8421375,T[I+1536>>0]=-27439*I,T[I+1792>>0]=-5329*I}function ue(I){for(var Z=I[0],te=I[1]-1;te>=0;)Z&1<<te&&(_|=1<<S),te--,S--,S<0&&(_==255?(N(255),N(0)):N(_),S=7,_=0)}function N(I){b.push(I)}function X(I){N(I>>8&255),N(I&255)}function ke(I,Z){var te,ee,le,ce,_e,we,Be,Te,D=0,J,se=8,Ie=64;for(J=0;J<se;++J){te=I[D],ee=I[D+1],le=I[D+2],ce=I[D+3],_e=I[D+4],we=I[D+5],Be=I[D+6],Te=I[D+7];var oe=te+Te,me=te-Te,Ee=ee+Be,ie=ee-Be,xe=le+we,Ue=le-we,Ae=ce+_e,ht=ce-_e,Et=oe+Ae,Qt=oe-Ae,ui=Ee+xe,fi=Ee-xe;I[D]=Et+ui,I[D+4]=Et-ui;var Ni=(fi+Qt)*.707106781;I[D+2]=Qt+Ni,I[D+6]=Qt-Ni,Et=ht+Ue,ui=Ue+ie,fi=ie+me;var Li=(Et-fi)*.382683433,fr=.5411961*Et+Li,Bi=1.306562965*fi+Li,Ri=ui*.707106781,Pi=me+Ri,Mi=me-Ri;I[D+5]=Mi+fr,I[D+3]=Mi-fr,I[D+1]=Pi+Bi,I[D+7]=Pi-Bi,D+=8}for(D=0,J=0;J<se;++J){te=I[D],ee=I[D+8],le=I[D+16],ce=I[D+24],_e=I[D+32],we=I[D+40],Be=I[D+48],Te=I[D+56];var es=te+Te,Xr=te-Te,ts=ee+Be,is=ee-Be,rs=le+we,ns=le-we,ss=ce+_e,ga=ce-_e,Fi=es+ss,Xt=es-ss,qi=ts+rs,hr=ts-rs;I[D]=Fi+qi,I[D+32]=Fi-qi;var os=(hr+Xt)*.707106781;I[D+16]=Xt+os,I[D+48]=Xt-os,Fi=ga+ns,qi=ns+is,hr=is+Xr;var as=(Fi-hr)*.382683433,ls=.5411961*Fi+as,cs=1.306562965*hr+as,$t=qi*.707106781,us=Xr+$t,fs=Xr-$t;I[D+40]=fs+ls,I[D+24]=fs-ls,I[D+8]=us+cs,I[D+56]=us-cs,D++}var pr;for(J=0;J<Ie;++J)pr=I[J]*Z[J],m[J]=pr>0?pr+.5|0:pr-.5|0;return m}function be(){X(65504),X(16),N(74),N(70),N(73),N(70),N(0),N(1),N(1),N(0),X(1),X(1),N(0),N(0)}function ge(I){if(I){X(65505),I[0]===69&&I[1]===120&&I[2]===105&&I[3]===102?X(I.length+2):(X(I.length+5+2),N(69),N(120),N(105),N(102),N(0));for(var Z=0;Z<I.length;Z++)N(I[Z])}}function ve(I,Z){X(65472),X(17),N(8),X(Z),X(I),N(3),N(1),N(17),N(0),N(2),N(17),N(1),N(3),N(17),N(1)}function fe(){X(65499),X(132),N(0);for(var I=0;I<64;I++)N(n[I]);N(1);for(var Z=0;Z<64;Z++)N(s[Z])}function K(){X(65476),X(418),N(0);for(var I=0;I<16;I++)N(L[I+1]);for(var Z=0;Z<=11;Z++)N(P[Z]);N(16);for(var te=0;te<16;te++)N(U[te+1]);for(var ee=0;ee<=161;ee++)N(q[ee]);N(1);for(var le=0;le<16;le++)N(H[le+1]);for(var ce=0;ce<=11;ce++)N(j[ce]);N(17);for(var _e=0;_e<16;_e++)N(V[_e+1]);for(var we=0;we<=161;we++)N(W[we])}function $(I){typeof I=="undefined"||I.constructor!==Array||I.forEach(Z=>{if(typeof Z=="string"){X(65534);var te=Z.length;X(te+2);var ee;for(ee=0;ee<te;ee++)N(Z.charCodeAt(ee))}})}function Ce(){X(65498),X(12),N(3),N(1),N(0),N(2),N(17),N(3),N(17),N(0),N(63),N(0)}function re(I,Z,te,ee,le){for(var ce=le[0],_e=le[240],we,Be=16,Te=63,D=64,J=ke(I,Z),se=0;se<D;++se)v[C[se]]=J[se];var Ie=v[0]-te;te=v[0],Ie==0?ue(ee[0]):(we=32767+Ie,ue(ee[g[we]]),ue(d[we]));for(var oe=63;oe>0&&v[oe]==0;oe--);if(oe==0)return ue(ce),te;for(var me=1,Ee;me<=oe;){for(var ie=me;v[me]==0&&me<=oe;++me);var xe=me-ie;if(xe>=Be){Ee=xe>>4;for(var Ue=1;Ue<=Ee;++Ue)ue(_e);xe=xe&15}we=32767+v[me],ue(le[(xe<<4)+g[we]]),ue(d[we]),me++}return oe!=Te&&ue(ce),te}function he(){for(var I=String.fromCharCode,Z=0;Z<256;Z++)R[Z]=I(Z)}this.encode=function(I,Z){var te=new Date().getTime();Z&&ft(Z),b=new Array,_=0,S=7,X(65496),be(),$(I.comments),ge(I.exifBuffer),fe(),ve(I.width,I.height),K(),Ce();var ee=0,le=0,ce=0;_=0,S=7,this.encode.displayName="_encode_";for(var _e=I.data,we=I.width,Be=I.height,Te=we*4,D=we*3,J,se=0,Ie,oe,me,Ee,ie,xe,Ue,Ae;se<Be;){for(J=0;J<Te;){for(Ee=Te*se+J,ie=Ee,xe=-1,Ue=0,Ae=0;Ae<64;Ae++)Ue=Ae>>3,xe=(Ae&7)*4,ie=Ee+Ue*Te+xe,se+Ue>=Be&&(ie-=Te*(se+1+Ue-Be)),J+xe>=Te&&(ie-=J+xe-Te+4),Ie=_e[ie++],oe=_e[ie++],me=_e[ie++],O[Ae]=(T[Ie]+T[oe+256>>0]+T[me+512>>0]>>16)-128,k[Ae]=(T[Ie+768>>0]+T[oe+1024>>0]+T[me+1280>>0]>>16)-128,E[Ae]=(T[Ie+1280>>0]+T[oe+1536>>0]+T[me+1792>>0]>>16)-128;ee=re(O,o,ee,l,u),le=re(k,a,le,c,f),ce=re(E,a,ce,c,f),J+=32}se+=8}if(S>=0){var ht=[];ht[1]=S+1,ht[0]=(1<<S+1)-1,ue(ht)}if(X(65497),typeof Is=="undefined")return new Uint8Array(b);return Buffer.from(b);var Et,Qt};function ft(I){if(I<=0&&(I=1),I>100&&(I=100),A!=I){var Z=0;I<50?Z=Math.floor(5e3/I):Z=Math.floor(200-I*2),Q(Z),A=I}}function vt(){var I=new Date().getTime();t||(t=50),he(),de(),ae(),ne(),ft(t);var Z=new Date().getTime()-I}vt()}typeof Is!="undefined"?Is.exports=Jh:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].encode=Jh);function Jh(t,e){typeof e=="undefined"&&(e=50);var i=new Zw(e),r=i.encode(t,e);return{data:r,width:t.width,height:t.height}}});var ep=w((XI,Ya)=>{var Ga=function(){"use strict";var e=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),i=4017,r=799,n=3406,s=2276,o=1567,a=3784,l=5793,c=2896;function u(){}function f(S,O){for(var k=0,E=[],R,T,A=16;A>0&&!S[A-1];)A--;E.push({children:[],index:0});var C=E[0],L;for(R=0;R<A;R++){for(T=0;T<S[R];T++){for(C=E.pop(),C.children[C.index]=O[k];C.index>0;){if(E.length===0)throw new Error("Could not recreate Huffman Table");C=E.pop()}for(C.index++,E.push(C);E.length<=R;)E.push(L={children:[],index:0}),C.children[C.index]=L.children,C=L;k++}R+1<A&&(E.push(L={children:[],index:0}),C.children[C.index]=L.children,C=L)}return E[0].children}function d(S,O,k,E,R,T,A,C,L,P){var U=k.precision,q=k.samplesPerLine,H=k.scanLines,j=k.mcusPerLine,V=k.progressive,W=k.maxH,Q=k.maxV,Y=O,de=0,ae=0;function ne(){if(ae>0)return ae--,de>>ae&1;if(de=S[O++],de==255){var D=S[O++];if(D)throw new Error("unexpected marker: "+(de<<8|D).toString(16))}return ae=7,de>>>7}function ue(D){for(var J=D,se;(se=ne())!==null;){if(J=J[se],typeof J=="number")return J;if(typeof J!="object")throw new Error("invalid huffman sequence")}return null}function N(D){for(var J=0;D>0;){var se=ne();if(se===null)return;J=J<<1|se,D--}return J}function X(D){var J=N(D);return J>=1<<D-1?J:J+(-1<<D)+1}function ke(D,J){var se=ue(D.huffmanTableDC),Ie=se===0?0:X(se);J[0]=D.pred+=Ie;for(var oe=1;oe<64;){var me=ue(D.huffmanTableAC),Ee=me&15,ie=me>>4;if(Ee===0){if(ie<15)break;oe+=16;continue}oe+=ie;var xe=e[oe];J[xe]=X(Ee),oe++}}function be(D,J){var se=ue(D.huffmanTableDC),Ie=se===0?0:X(se)<<L;J[0]=D.pred+=Ie}function ge(D,J){J[0]|=ne()<<L}var ve=0;function fe(D,J){if(ve>0){ve--;return}for(var se=T,Ie=A;se<=Ie;){var oe=ue(D.huffmanTableAC),me=oe&15,Ee=oe>>4;if(me===0){if(Ee<15){ve=N(Ee)+(1<<Ee)-1;break}se+=16;continue}se+=Ee;var ie=e[se];J[ie]=X(me)*(1<<L),se++}}var K=0,$;function Ce(D,J){for(var se=T,Ie=A,oe=0;se<=Ie;){var me=e[se],Ee=J[me]<0?-1:1;switch(K){case 0:var ie=ue(D.huffmanTableAC),xe=ie&15,oe=ie>>4;if(xe===0)oe<15?(ve=N(oe)+(1<<oe),K=4):(oe=16,K=1);else{if(xe!==1)throw new Error("invalid ACn encoding");$=X(xe),K=oe?2:3}continue;case 1:case 2:J[me]?J[me]+=(ne()<<L)*Ee:(oe--,oe===0&&(K=K==2?3:0));break;case 3:J[me]?J[me]+=(ne()<<L)*Ee:(J[me]=$<<L,K=0);break;case 4:J[me]&&(J[me]+=(ne()<<L)*Ee);break}se++}K===4&&(ve--,ve===0&&(K=0))}function re(D,J,se,Ie,oe){var me=se/j|0,Ee=se%j,ie=me*D.v+Ie,xe=Ee*D.h+oe;D.blocks[ie]===void 0&&P.tolerantDecoding||J(D,D.blocks[ie][xe])}function he(D,J,se){var Ie=se/D.blocksPerLine|0,oe=se%D.blocksPerLine;D.blocks[Ie]===void 0&&P.tolerantDecoding||J(D,D.blocks[Ie][oe])}var ft=E.length,vt,I,Z,te,ee,le;V?T===0?le=C===0?be:ge:le=C===0?fe:Ce:le=ke;var ce=0,_e,we;ft==1?we=E[0].blocksPerLine*E[0].blocksPerColumn:we=j*k.mcusPerColumn,R||(R=we);for(var Be,Te;ce<we;){for(I=0;I<ft;I++)E[I].pred=0;if(ve=0,ft==1)for(vt=E[0],ee=0;ee<R;ee++)he(vt,le,ce),ce++;else for(ee=0;ee<R;ee++){for(I=0;I<ft;I++)for(vt=E[I],Be=vt.h,Te=vt.v,Z=0;Z<Te;Z++)for(te=0;te<Be;te++)re(vt,le,ce,Z,te);if(ce++,ce===we)break}if(ce===we)do{if(S[O]===255&&S[O+1]!==0)break;O+=1}while(O<S.length-2);if(ae=0,_e=S[O]<<8|S[O+1],_e<65280)throw new Error("marker was not found");if(_e>=65488&&_e<=65495)O+=2;else break}return O-Y}function g(S,O){var k=[],E=O.blocksPerLine,R=O.blocksPerColumn,T=E<<3,A=new Int32Array(64),C=new Uint8Array(64);function L(Y,de,ae){var ne=O.quantizationTable,ue,N,X,ke,be,ge,ve,fe,K,$=ae,Ce;for(Ce=0;Ce<64;Ce++)$[Ce]=Y[Ce]*ne[Ce];for(Ce=0;Ce<8;++Ce){var re=8*Ce;if($[1+re]==0&&$[2+re]==0&&$[3+re]==0&&$[4+re]==0&&$[5+re]==0&&$[6+re]==0&&$[7+re]==0){K=l*$[0+re]+512>>10,$[0+re]=K,$[1+re]=K,$[2+re]=K,$[3+re]=K,$[4+re]=K,$[5+re]=K,$[6+re]=K,$[7+re]=K;continue}ue=l*$[0+re]+128>>8,N=l*$[4+re]+128>>8,X=$[2+re],ke=$[6+re],be=c*($[1+re]-$[7+re])+128>>8,fe=c*($[1+re]+$[7+re])+128>>8,ge=$[3+re]<<4,ve=$[5+re]<<4,K=ue-N+1>>1,ue=ue+N+1>>1,N=K,K=X*a+ke*o+128>>8,X=X*o-ke*a+128>>8,ke=K,K=be-ve+1>>1,be=be+ve+1>>1,ve=K,K=fe+ge+1>>1,ge=fe-ge+1>>1,fe=K,K=ue-ke+1>>1,ue=ue+ke+1>>1,ke=K,K=N-X+1>>1,N=N+X+1>>1,X=K,K=be*s+fe*n+2048>>12,be=be*n-fe*s+2048>>12,fe=K,K=ge*r+ve*i+2048>>12,ge=ge*i-ve*r+2048>>12,ve=K,$[0+re]=ue+fe,$[7+re]=ue-fe,$[1+re]=N+ve,$[6+re]=N-ve,$[2+re]=X+ge,$[5+re]=X-ge,$[3+re]=ke+be,$[4+re]=ke-be}for(Ce=0;Ce<8;++Ce){var he=Ce;if($[8+he]==0&&$[16+he]==0&&$[24+he]==0&&$[32+he]==0&&$[40+he]==0&&$[48+he]==0&&$[56+he]==0){K=l*ae[Ce+0]+8192>>14,$[0+he]=K,$[8+he]=K,$[16+he]=K,$[24+he]=K,$[32+he]=K,$[40+he]=K,$[48+he]=K,$[56+he]=K;continue}ue=l*$[0+he]+2048>>12,N=l*$[32+he]+2048>>12,X=$[16+he],ke=$[48+he],be=c*($[8+he]-$[56+he])+2048>>12,fe=c*($[8+he]+$[56+he])+2048>>12,ge=$[24+he],ve=$[40+he],K=ue-N+1>>1,ue=ue+N+1>>1,N=K,K=X*a+ke*o+2048>>12,X=X*o-ke*a+2048>>12,ke=K,K=be-ve+1>>1,be=be+ve+1>>1,ve=K,K=fe+ge+1>>1,ge=fe-ge+1>>1,fe=K,K=ue-ke+1>>1,ue=ue+ke+1>>1,ke=K,K=N-X+1>>1,N=N+X+1>>1,X=K,K=be*s+fe*n+2048>>12,be=be*n-fe*s+2048>>12,fe=K,K=ge*r+ve*i+2048>>12,ge=ge*i-ve*r+2048>>12,ve=K,$[0+he]=ue+fe,$[56+he]=ue-fe,$[8+he]=N+ve,$[48+he]=N-ve,$[16+he]=X+ge,$[40+he]=X-ge,$[24+he]=ke+be,$[32+he]=ke-be}for(Ce=0;Ce<64;++Ce){var ft=128+($[Ce]+8>>4);de[Ce]=ft<0?0:ft>255?255:ft}}_(T*R*8);for(var P,U,q=0;q<R;q++){var H=q<<3;for(P=0;P<8;P++)k.push(new Uint8Array(T));for(var j=0;j<E;j++){L(O.blocks[q][j],C,A);var V=0,W=j<<3;for(U=0;U<8;U++){var Q=k[H+U];for(P=0;P<8;P++)Q[W+P]=C[V++]}}}return k}function m(S){return S<0?0:S>255?255:S}u.prototype={load:function(O){var k=new XMLHttpRequest;k.open("GET",O,!0),k.responseType="arraybuffer",k.onload=function(){var E=new Uint8Array(k.response||k.mozResponseArrayBuffer);this.parse(E),this.onload&&this.onload()}.bind(this),k.send(null)},parse:function(O){var k=this.opts.maxResolutionInMP*1e3*1e3,E=0,R=O.length;function T(){var ie=O[E]<<8|O[E+1];return E+=2,ie}function A(){var ie=T(),xe=O.subarray(E,E+ie-2);return E+=xe.length,xe}function C(ie){var xe=1,Ue=1,Ae,ht;for(ht in ie.components)ie.components.hasOwnProperty(ht)&&(Ae=ie.components[ht],xe<Ae.h&&(xe=Ae.h),Ue<Ae.v&&(Ue=Ae.v));var Et=Math.ceil(ie.samplesPerLine/8/xe),Qt=Math.ceil(ie.scanLines/8/Ue);for(ht in ie.components)if(ie.components.hasOwnProperty(ht)){Ae=ie.components[ht];var ui=Math.ceil(Math.ceil(ie.samplesPerLine/8)*Ae.h/xe),fi=Math.ceil(Math.ceil(ie.scanLines/8)*Ae.v/Ue),Ni=Et*Ae.h,Li=Qt*Ae.v,fr=Li*Ni,Bi=[];_(fr*256);for(var Ri=0;Ri<Li;Ri++){for(var Pi=[],Mi=0;Mi<Ni;Mi++)Pi.push(new Int32Array(64));Bi.push(Pi)}Ae.blocksPerLine=ui,Ae.blocksPerColumn=fi,Ae.blocks=Bi}ie.maxH=xe,ie.maxV=Ue,ie.mcusPerLine=Et,ie.mcusPerColumn=Qt}var L=null,P=null,U=null,q,H,j=[],V=[],W=[],Q=[],Y=T(),de=-1;if(this.comments=[],Y!=65496)throw new Error("SOI not found");for(Y=T();Y!=65497;){var ae,ne,ue;switch(Y){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var N=A();if(Y===65534){var X=String.fromCharCode.apply(null,N);this.comments.push(X)}Y===65504&&N[0]===74&&N[1]===70&&N[2]===73&&N[3]===70&&N[4]===0&&(L={version:{major:N[5],minor:N[6]},densityUnits:N[7],xDensity:N[8]<<8|N[9],yDensity:N[10]<<8|N[11],thumbWidth:N[12],thumbHeight:N[13],thumbData:N.subarray(14,14+3*N[12]*N[13])}),Y===65505&&N[0]===69&&N[1]===120&&N[2]===105&&N[3]===102&&N[4]===0&&(this.exifBuffer=N.subarray(5,N.length)),Y===65518&&N[0]===65&&N[1]===100&&N[2]===111&&N[3]===98&&N[4]===101&&N[5]===0&&(P={version:N[6],flags0:N[7]<<8|N[8],flags1:N[9]<<8|N[10],transformCode:N[11]});break;case 65499:for(var ke=T(),be=ke+E-2;E<be;){var ge=O[E++];_(256);var ve=new Int32Array(64);if(ge>>4)if(ge>>4===1)for(ne=0;ne<64;ne++){var fe=e[ne];ve[fe]=T()}else throw new Error("DQT: invalid table spec");else for(ne=0;ne<64;ne++){var fe=e[ne];ve[fe]=O[E++]}j[ge&15]=ve}break;case 65472:case 65473:case 65474:T(),q={},q.extended=Y===65473,q.progressive=Y===65474,q.precision=O[E++],q.scanLines=T(),q.samplesPerLine=T(),q.components={},q.componentsOrder=[];var K=q.scanLines*q.samplesPerLine;if(K>k){var $=Math.ceil((K-k)/1e6);throw new Error(`maxResolutionInMP limit exceeded by ${$}MP`)}var Ce=O[E++],re,he=0,ft=0;for(ae=0;ae<Ce;ae++){re=O[E];var vt=O[E+1]>>4,I=O[E+1]&15,Z=O[E+2];if(vt<=0||I<=0)throw new Error("Invalid sampling factor, expected values above 0");q.componentsOrder.push(re),q.components[re]={h:vt,v:I,quantizationIdx:Z},E+=3}C(q),V.push(q);break;case 65476:var te=T();for(ae=2;ae<te;){var ee=O[E++],le=new Uint8Array(16),ce=0;for(ne=0;ne<16;ne++,E++)ce+=le[ne]=O[E];_(16+ce);var _e=new Uint8Array(ce);for(ne=0;ne<ce;ne++,E++)_e[ne]=O[E];ae+=17+ce,(ee>>4?W:Q)[ee&15]=f(le,_e)}break;case 65501:T(),H=T();break;case 65500:T(),T();break;case 65498:var we=T(),Be=O[E++],Te=[],D;for(ae=0;ae<Be;ae++){D=q.components[O[E++]];var J=O[E++];D.huffmanTableDC=Q[J>>4],D.huffmanTableAC=W[J&15],Te.push(D)}var se=O[E++],Ie=O[E++],oe=O[E++],me=d(O,E,q,Te,H,se,Ie,oe>>4,oe&15,this.opts);E+=me;break;case 65535:O[E]!==255&&E--;break;default:if(O[E-3]==255&&O[E-2]>=192&&O[E-2]<=254){E-=3;break}else if(Y===224||Y==225){if(de!==-1)throw new Error(`first unknown JPEG marker at offset ${de.toString(16)}, second unknown JPEG marker ${Y.toString(16)} at offset ${(E-1).toString(16)}`);de=E-1;let ie=T();if(O[E+ie-2]===255){E+=ie-2;break}}throw new Error("unknown JPEG marker "+Y.toString(16))}Y=T()}if(V.length!=1)throw new Error("only single frame JPEGs supported");for(var ae=0;ae<V.length;ae++){var Ee=V[ae].components;for(var ne in Ee)Ee[ne].quantizationTable=j[Ee[ne].quantizationIdx],delete Ee[ne].quantizationIdx}this.width=q.samplesPerLine,this.height=q.scanLines,this.jfif=L,this.adobe=P,this.components=[];for(var ae=0;ae<q.componentsOrder.length;ae++){var D=q.components[q.componentsOrder[ae]];this.components.push({lines:g(q,D),scaleX:D.h/q.maxH,scaleY:D.v/q.maxV})}},getData:function(O,k){var E=this.width/O,R=this.height/k,T,A,C,L,P,U,q,H,j,V,W=0,Q,Y,de,ae,ne,ue,N,X,ke,be,ge,ve=O*k*this.components.length;_(ve);var fe=new Uint8Array(ve);switch(this.components.length){case 1:for(T=this.components[0],V=0;V<k;V++)for(P=T.lines[0|V*T.scaleY*R],j=0;j<O;j++)Q=P[0|j*T.scaleX*E],fe[W++]=Q;break;case 2:for(T=this.components[0],A=this.components[1],V=0;V<k;V++)for(P=T.lines[0|V*T.scaleY*R],U=A.lines[0|V*A.scaleY*R],j=0;j<O;j++)Q=P[0|j*T.scaleX*E],fe[W++]=Q,Q=U[0|j*A.scaleX*E],fe[W++]=Q;break;case 3:for(ge=!0,this.adobe&&this.adobe.transformCode?ge=!0:typeof this.opts.colorTransform!="undefined"&&(ge=!!this.opts.colorTransform),T=this.components[0],A=this.components[1],C=this.components[2],V=0;V<k;V++)for(P=T.lines[0|V*T.scaleY*R],U=A.lines[0|V*A.scaleY*R],q=C.lines[0|V*C.scaleY*R],j=0;j<O;j++)ge?(Q=P[0|j*T.scaleX*E],Y=U[0|j*A.scaleX*E],de=q[0|j*C.scaleX*E],X=m(Q+1.402*(de-128)),ke=m(Q-.3441363*(Y-128)-.71413636*(de-128)),be=m(Q+1.772*(Y-128))):(X=P[0|j*T.scaleX*E],ke=U[0|j*A.scaleX*E],be=q[0|j*C.scaleX*E]),fe[W++]=X,fe[W++]=ke,fe[W++]=be;break;case 4:if(!this.adobe)throw new Error("Unsupported color mode (4 components)");for(ge=!1,this.adobe&&this.adobe.transformCode?ge=!0:typeof this.opts.colorTransform!="undefined"&&(ge=!!this.opts.colorTransform),T=this.components[0],A=this.components[1],C=this.components[2],L=this.components[3],V=0;V<k;V++)for(P=T.lines[0|V*T.scaleY*R],U=A.lines[0|V*A.scaleY*R],q=C.lines[0|V*C.scaleY*R],H=L.lines[0|V*L.scaleY*R],j=0;j<O;j++)ge?(Q=P[0|j*T.scaleX*E],Y=U[0|j*A.scaleX*E],de=q[0|j*C.scaleX*E],ae=H[0|j*L.scaleX*E],ne=255-m(Q+1.402*(de-128)),ue=255-m(Q-.3441363*(Y-128)-.71413636*(de-128)),N=255-m(Q+1.772*(Y-128))):(ne=P[0|j*T.scaleX*E],ue=U[0|j*A.scaleX*E],N=q[0|j*C.scaleX*E],ae=H[0|j*L.scaleX*E]),fe[W++]=255-ne,fe[W++]=255-ue,fe[W++]=255-N,fe[W++]=255-ae;break;default:throw new Error("Unsupported color mode")}return fe},copyToImageData:function(O,k){var E=O.width,R=O.height,T=O.data,A=this.getData(E,R),C=0,L=0,P,U,q,H,j,V,W,Q,Y;switch(this.components.length){case 1:for(U=0;U<R;U++)for(P=0;P<E;P++)q=A[C++],T[L++]=q,T[L++]=q,T[L++]=q,k&&(T[L++]=255);break;case 3:for(U=0;U<R;U++)for(P=0;P<E;P++)W=A[C++],Q=A[C++],Y=A[C++],T[L++]=W,T[L++]=Q,T[L++]=Y,k&&(T[L++]=255);break;case 4:for(U=0;U<R;U++)for(P=0;P<E;P++)j=A[C++],V=A[C++],q=A[C++],H=A[C++],W=255-m(j*(1-H/255)+H),Q=255-m(V*(1-H/255)+H),Y=255-m(q*(1-H/255)+H),T[L++]=W,T[L++]=Q,T[L++]=Y,k&&(T[L++]=255);break;default:throw new Error("Unsupported color mode")}}};var v=0,b=0;function _(S=0){var O=v+S;if(O>b){var k=Math.ceil((O-b)/1024/1024);throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${k}MB`)}v=O}return u.resetMaxMemoryUsage=function(S){v=0,b=S},u.getBytesAllocated=function(){return v},u.requestMemoryAllocation=_,u}();typeof Ya!="undefined"?Ya.exports=Xh:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].decode=Xh);function Xh(t,e={}){var i={colorTransform:void 0,useTArray:!1,formatAsRGBA:!0,tolerantDecoding:!0,maxResolutionInMP:100,maxMemoryUsageInMB:512},r={...i,...e},n=new Uint8Array(t),s=new Ga;s.opts=r,Ga.resetMaxMemoryUsage(r.maxMemoryUsageInMB*1024*1024),s.parse(n);var o=r.formatAsRGBA?4:3,a=s.width*s.height*o;try{Ga.requestMemoryAllocation(a);var l={width:s.width,height:s.height,exifBuffer:s.exifBuffer,data:r.useTArray?new Uint8Array(a):Buffer.alloc(a)};s.comments.length>0&&(l.comments=s.comments)}catch(c){throw c instanceof RangeError?new Error("Could not allocate enough memory for the image. Required: "+a):c instanceof ReferenceError&&c.message==="Buffer is not defined"?new Error("Buffer is not globally defined in this environment. Consider setting useTArray to true"):c}return s.copyToImageData(l,r.formatAsRGBA),l}});var ip=w((e2,tp)=>{var Qw=Qh(),Xw=ep();tp.exports={encode:Qw,decode:Xw}});var np=w((t2,rp)=>{"use strict";function Ns(){this._types=Object.create(null),this._extensions=Object.create(null);for(let t=0;t<arguments.length;t++)this.define(arguments[t]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}Ns.prototype.define=function(t,e){for(let i in t){let r=t[i].map(function(n){return n.toLowerCase()});i=i.toLowerCase();for(let n=0;n<r.length;n++){let s=r[n];if(s[0]!=="*"){if(!e&&s in this._types)throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+i+'".');this._types[s]=i}}if(e||!this._extensions[i]){let n=r[0];this._extensions[i]=n[0]!=="*"?n:n.substr(1)}}};Ns.prototype.getType=function(t){t=String(t);let e=t.replace(/^.*[/\\]/,"").toLowerCase(),i=e.replace(/^.*\./,"").toLowerCase(),r=e.length<t.length;return(i.length<e.length-1||!r)&&this._types[i]||null};Ns.prototype.getExtension=function(t){return t=/^\s*([^;\s]*)/.test(t)&&RegExp.$1,t&&this._extensions[t.toLowerCase()]||null};rp.exports=Ns});var op=w((i2,sp)=>{sp.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}});var lp=w((r2,ap)=>{ap.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}});var up=w((n2,cp)=>{"use strict";var e1=np();cp.exports=new e1(op(),lp())});var hp=w((s2,fp)=>{fp.exports=function(t,e){for(var i=[],r=0;r<t.length;r++){var n=e(t[r],r);t1(n)?i.push.apply(i,n):i.push(n)}return i};var t1=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"}});var vp=w((o2,gp)=>{"use strict";gp.exports=dp;function dp(t,e,i){t instanceof RegExp&&(t=pp(t,i)),e instanceof RegExp&&(e=pp(e,i));var r=mp(t,e,i);return r&&{start:r[0],end:r[1],pre:i.slice(0,r[0]),body:i.slice(r[0]+t.length,r[1]),post:i.slice(r[1]+e.length)}}function pp(t,e){var i=e.match(t);return i?i[0]:null}dp.range=mp;function mp(t,e,i){var r,n,s,o,a,l=i.indexOf(t),c=i.indexOf(e,l+1),u=l;if(l>=0&&c>0){if(t===e)return[l,c];for(r=[],s=i.length;u>=0&&!a;)u==l?(r.push(u),l=i.indexOf(t,u+1)):r.length==1?a=[r.pop(),c]:(n=r.pop(),n<s&&(s=n,o=c),c=i.indexOf(e,u+1)),u=l<c&&l>=0?l:c;r.length&&(a=[s,o])}return a}});var kp=w((a2,Ep)=>{var i1=hp(),yp=vp();Ep.exports=s1;var bp="\0SLASH"+Math.random()+"\0",_p="\0OPEN"+Math.random()+"\0",za="\0CLOSE"+Math.random()+"\0",wp="\0COMMA"+Math.random()+"\0",xp="\0PERIOD"+Math.random()+"\0";function Wa(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function r1(t){return t.split("\\\\").join(bp).split("\\{").join(_p).split("\\}").join(za).split("\\,").join(wp).split("\\.").join(xp)}function n1(t){return t.split(bp).join("\\").split(_p).join("{").split(za).join("}").split(wp).join(",").split(xp).join(".")}function Sp(t){if(!t)return[""];var e=[],i=yp("{","}",t);if(!i)return t.split(",");var r=i.pre,n=i.body,s=i.post,o=r.split(",");o[o.length-1]+="{"+n+"}";var a=Sp(s);return s.length&&(o[o.length-1]+=a.shift(),o.push.apply(o,a)),e.push.apply(e,o),e}function s1(t){return t?(t.substr(0,2)==="{}"&&(t="\\{\\}"+t.substr(2)),Sr(r1(t),!0).map(n1)):[]}function o1(t){return"{"+t+"}"}function a1(t){return/^-?0\d/.test(t)}function l1(t,e){return t<=e}function c1(t,e){return t>=e}function Sr(t,e){var i=[],r=yp("{","}",t);if(!r||/\$$/.test(r.pre))return[t];var n=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),s=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),o=n||s,a=r.body.indexOf(",")>=0;if(!o&&!a)return r.post.match(/,.*\}/)?(t=r.pre+"{"+r.body+za+r.post,Sr(t)):[t];var l;if(o)l=r.body.split(/\.\./);else if(l=Sp(r.body),l.length===1&&(l=Sr(l[0],!1).map(o1),l.length===1)){var u=r.post.length?Sr(r.post,!1):[""];return u.map(function(P){return r.pre+l[0]+P})}var c=r.pre,u=r.post.length?Sr(r.post,!1):[""],f;if(o){var d=Wa(l[0]),g=Wa(l[1]),m=Math.max(l[0].length,l[1].length),v=l.length==3?Math.abs(Wa(l[2])):1,b=l1,_=g<d;_&&(v*=-1,b=c1);var S=l.some(a1);f=[];for(var O=d;b(O,g);O+=v){var k;if(s)k=String.fromCharCode(O),k==="\\"&&(k="");else if(k=String(O),S){var E=m-k.length;if(E>0){var R=new Array(E+1).join("0");O<0?k="-"+R+k.slice(1):k=R+k}}f.push(k)}}else f=i1(l,function(L){return Sr(L,!1)});for(var T=0;T<f.length;T++)for(var A=0;A<u.length;A++){var C=c+f[T]+u[A];(!e||o||C)&&i.push(C)}return i}});var Np=w((l2,Ip)=>{Ip.exports=wt;wt.Minimatch=Qe;var ln=function(){try{return require("path")}catch{}}()||{sep:"/"};wt.sep=ln.sep;var Za=wt.GLOBSTAR=Qe.GLOBSTAR={},u1=kp(),Op={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},Ka="[^/]",Ja=Ka+"*?",f1="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",h1="(?:(?!(?:\\/|^)\\.).)*?",Cp=p1("().*{}+?[]^$\\!");function p1(t){return t.split("").reduce(function(e,i){return e[i]=!0,e},{})}var Tp=/\/+/;wt.filter=d1;function d1(t,e){return e=e||{},function(i,r,n){return wt(i,t,e)}}function pi(t,e){e=e||{};var i={};return Object.keys(t).forEach(function(r){i[r]=t[r]}),Object.keys(e).forEach(function(r){i[r]=e[r]}),i}wt.defaults=function(t){if(!t||typeof t!="object"||!Object.keys(t).length)return wt;var e=wt,i=function(n,s,o){return e(n,s,pi(t,o))};return i.Minimatch=function(n,s){return new e.Minimatch(n,pi(t,s))},i.Minimatch.defaults=function(n){return e.defaults(pi(t,n)).Minimatch},i.filter=function(n,s){return e.filter(n,pi(t,s))},i.defaults=function(n){return e.defaults(pi(t,n))},i.makeRe=function(n,s){return e.makeRe(n,pi(t,s))},i.braceExpand=function(n,s){return e.braceExpand(n,pi(t,s))},i.match=function(r,n,s){return e.match(r,n,pi(t,s))},i};Qe.defaults=function(t){return wt.defaults(t).Minimatch};function wt(t,e,i){return Bs(e),i||(i={}),!i.nocomment&&e.charAt(0)==="#"?!1:new Qe(e,i).match(t)}function Qe(t,e){if(!(this instanceof Qe))return new Qe(t,e);Bs(t),e||(e={}),t=t.trim(),!e.allowWindowsEscape&&ln.sep!=="/"&&(t=t.split(ln.sep).join("/")),this.options=e,this.set=[],this.pattern=t,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.make()}Qe.prototype.debug=function(){};Qe.prototype.make=m1;function m1(){var t=this.pattern,e=this.options;if(!e.nocomment&&t.charAt(0)==="#"){this.comment=!0;return}if(!t){this.empty=!0;return}this.parseNegate();var i=this.globSet=this.braceExpand();e.debug&&(this.debug=function(){console.error.apply(console,arguments)}),this.debug(this.pattern,i),i=this.globParts=i.map(function(r){return r.split(Tp)}),this.debug(this.pattern,i),i=i.map(function(r,n,s){return r.map(this.parse,this)},this),this.debug(this.pattern,i),i=i.filter(function(r){return r.indexOf(!1)===-1}),this.debug(this.pattern,i),this.set=i}Qe.prototype.parseNegate=g1;function g1(){var t=this.pattern,e=!1,i=this.options,r=0;if(!i.nonegate){for(var n=0,s=t.length;n<s&&t.charAt(n)==="!";n++)e=!e,r++;r&&(this.pattern=t.substr(r)),this.negate=e}}wt.braceExpand=function(t,e){return Ap(t,e)};Qe.prototype.braceExpand=Ap;function Ap(t,e){return e||(this instanceof Qe?e=this.options:e={}),t=typeof t=="undefined"?this.pattern:t,Bs(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:u1(t)}var v1=1024*64,Bs=function(t){if(typeof t!="string")throw new TypeError("invalid pattern");if(t.length>v1)throw new TypeError("pattern is too long")};Qe.prototype.parse=y1;var Ls={};function y1(t,e){Bs(t);var i=this.options;if(t==="**")if(i.noglobstar)t="*";else return Za;if(t==="")return"";var r="",n=!!i.nocase,s=!1,o=[],a=[],l,c=!1,u=-1,f=-1,d=t.charAt(0)==="."?"":i.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",g=this;function m(){if(l){switch(l){case"*":r+=Ja,n=!0;break;case"?":r+=Ka,n=!0;break;default:r+="\\"+l;break}g.debug("clearStateChar %j %j",l,r),l=!1}}for(var v=0,b=t.length,_;v<b&&(_=t.charAt(v));v++){if(this.debug("%s	%s %s %j",t,v,r,_),s&&Cp[_]){r+="\\"+_,s=!1;continue}switch(_){case"/":return!1;case"\\":m(),s=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s	%s %s %j <-- stateChar",t,v,r,_),c){this.debug("  in class"),_==="!"&&v===f+1&&(_="^"),r+=_;continue}g.debug("call clearStateChar %j",l),m(),l=_,i.noext&&m();continue;case"(":if(c){r+="(";continue}if(!l){r+="\\(";continue}o.push({type:l,start:v-1,reStart:r.length,open:Op[l].open,close:Op[l].close}),r+=l==="!"?"(?:(?!(?:":"(?:",this.debug("plType %j %j",l,r),l=!1;continue;case")":if(c||!o.length){r+="\\)";continue}m(),n=!0;var S=o.pop();r+=S.close,S.type==="!"&&a.push(S),S.reEnd=r.length;continue;case"|":if(c||!o.length||s){r+="\\|",s=!1;continue}m(),r+="|";continue;case"[":if(m(),c){r+="\\"+_;continue}c=!0,f=v,u=r.length,r+=_;continue;case"]":if(v===f+1||!c){r+="\\"+_,s=!1;continue}var O=t.substring(f+1,v);try{RegExp("["+O+"]")}catch{var k=this.parse(O,Ls);r=r.substr(0,u)+"\\["+k[0]+"\\]",n=n||k[1],c=!1;continue}n=!0,c=!1,r+=_;continue;default:m(),s?s=!1:Cp[_]&&!(_==="^"&&c)&&(r+="\\"),r+=_}}for(c&&(O=t.substr(f+1),k=this.parse(O,Ls),r=r.substr(0,u)+"\\["+k[0],n=n||k[1]),S=o.pop();S;S=o.pop()){var E=r.slice(S.reStart+S.open.length);this.debug("setting tail",r,S),E=E.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(de,ae,ne){return ne||(ne="\\"),ae+ae+ne+"|"}),this.debug(`tail=%j
   %s`,E,E,S,r);var R=S.type==="*"?Ja:S.type==="?"?Ka:"\\"+S.type;n=!0,r=r.slice(0,S.reStart)+R+"\\("+E}m(),s&&(r+="\\\\");var T=!1;switch(r.charAt(0)){case"[":case".":case"(":T=!0}for(var A=a.length-1;A>-1;A--){var C=a[A],L=r.slice(0,C.reStart),P=r.slice(C.reStart,C.reEnd-8),U=r.slice(C.reEnd-8,C.reEnd),q=r.slice(C.reEnd);U+=q;var H=L.split("(").length-1,j=q;for(v=0;v<H;v++)j=j.replace(/\)[+*?]?/,"");q=j;var V="";q===""&&e!==Ls&&(V="$");var W=L+P+q+V+U;r=W}if(r!==""&&n&&(r="(?=.)"+r),T&&(r=d+r),e===Ls)return[r,n];if(!n)return _1(t);var Q=i.nocase?"i":"";try{var Y=new RegExp("^"+r+"$",Q)}catch{return new RegExp("$.")}return Y._glob=t,Y._src=r,Y}wt.makeRe=function(t,e){return new Qe(t,e||{}).makeRe()};Qe.prototype.makeRe=b1;function b1(){if(this.regexp||this.regexp===!1)return this.regexp;var t=this.set;if(!t.length)return this.regexp=!1,this.regexp;var e=this.options,i=e.noglobstar?Ja:e.dot?f1:h1,r=e.nocase?"i":"",n=t.map(function(s){return s.map(function(o){return o===Za?i:typeof o=="string"?w1(o):o._src}).join("\\/")}).join("|");n="^(?:"+n+")$",this.negate&&(n="^(?!"+n+").*$");try{this.regexp=new RegExp(n,r)}catch{this.regexp=!1}return this.regexp}wt.match=function(t,e,i){i=i||{};var r=new Qe(e,i);return t=t.filter(function(n){return r.match(n)}),r.options.nonull&&!t.length&&t.push(e),t};Qe.prototype.match=function(e,i){if(typeof i=="undefined"&&(i=this.partial),this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&i)return!0;var r=this.options;ln.sep!=="/"&&(e=e.split(ln.sep).join("/")),e=e.split(Tp),this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var s,o;for(o=e.length-1;o>=0&&(s=e[o],!s);o--);for(o=0;o<n.length;o++){var a=n[o],l=e;r.matchBase&&a.length===1&&(l=[s]);var c=this.matchOne(l,a,i);if(c)return r.flipNegate?!0:!this.negate}return r.flipNegate?!1:this.negate};Qe.prototype.matchOne=function(t,e,i){var r=this.options;this.debug("matchOne",{this:this,file:t,pattern:e}),this.debug("matchOne",t.length,e.length);for(var n=0,s=0,o=t.length,a=e.length;n<o&&s<a;n++,s++){this.debug("matchOne loop");var l=e[s],c=t[n];if(this.debug(e,l,c),l===!1)return!1;if(l===Za){this.debug("GLOBSTAR",[e,l,c]);var u=n,f=s+1;if(f===a){for(this.debug("** at the end");n<o;n++)if(t[n]==="."||t[n]===".."||!r.dot&&t[n].charAt(0)===".")return!1;return!0}for(;u<o;){var d=t[u];if(this.debug(`
globstar while`,t,u,e,f,d),this.matchOne(t.slice(u),e.slice(f),i))return this.debug("globstar found match!",u,o,d),!0;if(d==="."||d===".."||!r.dot&&d.charAt(0)==="."){this.debug("dot detected!",t,u,e,f);break}this.debug("globstar swallow a segment, and continue"),u++}return!!(i&&(this.debug(`
>>> no match, partial?`,t,u,e,f),u===o))}var g;if(typeof l=="string"?(g=c===l,this.debug("string match",l,c,g)):(g=c.match(l),this.debug("pattern match",l,c,g)),!g)return!1}if(n===o&&s===a)return!0;if(n===o)return i;if(s===a)return n===o-1&&t[n]==="";throw new Error("wtf?")};function _1(t){return t.replace(/\\(.)/g,"$1")}function w1(t){return t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}});var Xa=w((c2,Bp)=>{"use strict";var Lp=require("fs"),Qa;function x1(){try{return Lp.statSync("/.dockerenv"),!0}catch{return!1}}function S1(){try{return Lp.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Bp.exports=()=>(Qa===void 0&&(Qa=x1()||S1()),Qa)});var Mp=w((u2,el)=>{"use strict";var E1=require("os"),k1=require("fs"),Rp=Xa(),Pp=()=>{if(process.platform!=="linux")return!1;if(E1.release().toLowerCase().includes("microsoft"))return!Rp();try{return k1.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!Rp():!1}catch{return!1}};process.env.__IS_WSL_TEST__?el.exports=Pp:el.exports=Pp()});var qp=w((f2,Fp)=>{"use strict";Fp.exports=(t,e,i)=>{let r=n=>Object.defineProperty(t,e,{value:n,enumerable:!0,writable:!0});return Object.defineProperty(t,e,{configurable:!0,enumerable:!0,get(){let n=i();return r(n),n},set(n){r(n)}}),t}});var Gp=w((h2,Hp)=>{var O1=require("path"),C1=require("child_process"),{promises:tl,constants:Vp}=require("fs"),Rs=Mp(),T1=Xa(),il=qp(),Dp=O1.join(__dirname,"xdg-open"),{platform:Er,arch:jp}=process,A1=(()=>{let t="/mnt/",e;return async function(){if(e)return e;let i="/etc/wsl.conf",r=!1;try{await tl.access(i,Vp.F_OK),r=!0}catch{}if(!r)return t;let n=await tl.readFile(i,{encoding:"utf8"}),s=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(n);return s?(e=s.groups.mountPoint.trim(),e=e.endsWith("/")?e:`${e}/`,e):t}})(),Up=async(t,e)=>{let i;for(let r of t)try{return await e(r)}catch(n){i=n}throw i},Ps=async t=>{if(t={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...t},Array.isArray(t.app))return Up(t.app,a=>Ps({...t,app:a}));let{name:e,arguments:i=[]}=t.app||{};if(i=[...i],Array.isArray(e))return Up(e,a=>Ps({...t,app:{name:a,arguments:i}}));let r,n=[],s={};if(Er==="darwin")r="open",t.wait&&n.push("--wait-apps"),t.background&&n.push("--background"),t.newInstance&&n.push("--new"),e&&n.push("-a",e);else if(Er==="win32"||Rs&&!T1()){let a=await A1();r=Rs?`${a}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,n.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Rs||(s.windowsVerbatimArguments=!0);let l=["Start"];t.wait&&l.push("-Wait"),e?(l.push(`"\`"${e}\`""`,"-ArgumentList"),t.target&&i.unshift(t.target)):t.target&&l.push(`"${t.target}"`),i.length>0&&(i=i.map(c=>`"\`"${c}\`""`),l.push(i.join(","))),t.target=Buffer.from(l.join(" "),"utf16le").toString("base64")}else{if(e)r=e;else{let a=!__dirname||__dirname==="/",l=!1;try{await tl.access(Dp,Vp.X_OK),l=!0}catch{}r=process.versions.electron||Er==="android"||a||!l?"xdg-open":Dp}i.length>0&&n.push(...i),t.wait||(s.stdio="ignore",s.detached=!0)}t.target&&n.push(t.target),Er==="darwin"&&i.length>0&&n.push("--args",...i);let o=C1.spawn(r,n,s);return t.wait?new Promise((a,l)=>{o.once("error",l),o.once("close",c=>{if(t.allowNonzeroExitCode&&c>0){l(new Error(`Exited with code ${c}`));return}a(o)})}):(o.unref(),o)},rl=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `target`");return Ps({...e,target:t})},I1=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `name`");let{arguments:i=[]}=e||{};if(i!=null&&!Array.isArray(i))throw new TypeError("Expected `appArguments` as Array type");return Ps({...e,app:{name:t,arguments:i}})};function $p(t){if(typeof t=="string"||Array.isArray(t))return t;let{[jp]:e}=t;if(!e)throw new Error(`${jp} is not supported`);return e}function nl({[Er]:t},{wsl:e}){if(e&&Rs)return $p(e);if(!t)throw new Error(`${Er} is not supported`);return $p(t)}var Ms={};il(Ms,"chrome",()=>nl({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));il(Ms,"firefox",()=>nl({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));il(Ms,"edge",()=>nl({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));rl.apps=Ms;rl.openApp=I1;Hp.exports=rl});var sl=w((p2,Wp)=>{"use strict";var N1=require("util"),Yp=require("stream"),Ft=Wp.exports=function(){Yp.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};N1.inherits(Ft,Yp);Ft.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))};Ft.prototype.write=function(t,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let i;return Buffer.isBuffer(t)?i=t:i=Buffer.from(t,e||this._encoding),this._buffers.push(i),this._buffered+=i.length,this._process(),this._reads&&this._reads.length===0&&(this._paused=!0),this.writable&&!this._paused};Ft.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(this._buffers.length===0?this._end():(this._buffers.push(null),this._process()))};Ft.prototype.destroySoon=Ft.prototype.end;Ft.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()};Ft.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))};Ft.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))};Ft.prototype._processRead=function(t){this._reads.shift();let e=0,i=0,r=Buffer.alloc(t.length);for(;e<t.length;){let n=this._buffers[i++],s=Math.min(n.length,t.length-e);n.copy(r,e,0,s),e+=s,s!==n.length&&(this._buffers[--i]=n.slice(s))}i>0&&this._buffers.splice(0,i),this._buffered-=t.length,t.func.call(this,r)};Ft.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}});var al=w(ol=>{"use strict";var di=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];ol.getImagePasses=function(t,e){let i=[],r=t%8,n=e%8,s=(t-r)/8,o=(e-n)/8;for(let a=0;a<di.length;a++){let l=di[a],c=s*l.x.length,u=o*l.y.length;for(let f=0;f<l.x.length&&l.x[f]<r;f++)c++;for(let f=0;f<l.y.length&&l.y[f]<n;f++)u++;c>0&&u>0&&i.push({width:c,height:u,index:a})}return i};ol.getInterlaceIterator=function(t){return function(e,i,r){let n=e%di[r].x.length,s=(e-n)/di[r].x.length*8+di[r].x[n],o=i%di[r].y.length,a=(i-o)/di[r].y.length*8+di[r].y[o];return s*4+a*t*4}}});var ll=w((m2,zp)=>{"use strict";zp.exports=function(e,i,r){let n=e+i-r,s=Math.abs(n-e),o=Math.abs(n-i),a=Math.abs(n-r);return s<=o&&s<=a?e:o<=a?i:r}});var cl=w((g2,Jp)=>{"use strict";var L1=al(),B1=ll();function Kp(t,e,i){let r=t*e;return i!==8&&(r=Math.ceil(r/(8/i))),r}var kr=Jp.exports=function(t,e){let i=t.width,r=t.height,n=t.interlace,s=t.bpp,o=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],n){let a=L1.getImagePasses(i,r);for(let l=0;l<a.length;l++)this._images.push({byteWidth:Kp(a[l].width,s,o),height:a[l].height,lineIndex:0})}else this._images.push({byteWidth:Kp(i,s,o),height:r,lineIndex:0});o===8?this._xComparison=s:o===16?this._xComparison=s*2:this._xComparison=1};kr.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))};kr.prototype._unFilterType1=function(t,e,i){let r=this._xComparison,n=r-1;for(let s=0;s<i;s++){let o=t[1+s],a=s>n?e[s-r]:0;e[s]=o+a}};kr.prototype._unFilterType2=function(t,e,i){let r=this._lastLine;for(let n=0;n<i;n++){let s=t[1+n],o=r?r[n]:0;e[n]=s+o}};kr.prototype._unFilterType3=function(t,e,i){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<i;o++){let a=t[1+o],l=s?s[o]:0,c=o>n?e[o-r]:0,u=Math.floor((c+l)/2);e[o]=a+u}};kr.prototype._unFilterType4=function(t,e,i){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<i;o++){let a=t[1+o],l=s?s[o]:0,c=o>n?e[o-r]:0,u=o>n&&s?s[o-r]:0,f=B1(c,l,u);e[o]=a+f}};kr.prototype._reverseFilterLine=function(t){let e=t[0],i,r=this._images[this._imageIndex],n=r.byteWidth;if(e===0)i=t.slice(1,n+1);else switch(i=Buffer.alloc(n),e){case 1:this._unFilterType1(t,i,n);break;case 2:this._unFilterType2(t,i,n);break;case 3:this._unFilterType3(t,i,n);break;case 4:this._unFilterType4(t,i,n);break;default:throw new Error("Unrecognised filter type - "+e)}this.write(i),r.lineIndex++,r.lineIndex>=r.height?(this._lastLine=null,this._imageIndex++,r=this._images[this._imageIndex]):this._lastLine=i,r?this.read(r.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}});var Xp=w((v2,Qp)=>{"use strict";var R1=require("util"),Zp=sl(),P1=cl(),M1=Qp.exports=function(t){Zp.call(this);let e=[],i=this;this._filter=new P1(t,{read:this.read.bind(this),write:function(r){e.push(r)},complete:function(){i.emit("complete",Buffer.concat(e))}}),this._filter.start()};R1.inherits(M1,Zp)});var Or=w((y2,ed)=>{"use strict";ed.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}});var hl=w((b2,td)=>{"use strict";var ul=[];(function(){for(let t=0;t<256;t++){let e=t;for(let i=0;i<8;i++)e&1?e=3988292384^e>>>1:e=e>>>1;ul[t]=e}})();var fl=td.exports=function(){this._crc=-1};fl.prototype.write=function(t){for(let e=0;e<t.length;e++)this._crc=ul[(this._crc^t[e])&255]^this._crc>>>8;return!0};fl.prototype.crc32=function(){return this._crc^-1};fl.crc32=function(t){let e=-1;for(let i=0;i<t.length;i++)e=ul[(e^t[i])&255]^e>>>8;return e^-1}});var pl=w((_2,id)=>{"use strict";var Ve=Or(),F1=hl(),We=id.exports=function(t,e){this._options=t,t.checkCRC=t.checkCRC!==!1,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[Ve.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[Ve.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[Ve.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[Ve.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[Ve.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[Ve.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};We.prototype.start=function(){this.read(Ve.PNG_SIGNATURE.length,this._parseSignature.bind(this))};We.prototype._parseSignature=function(t){let e=Ve.PNG_SIGNATURE;for(let i=0;i<e.length;i++)if(t[i]!==e[i]){this.error(new Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))};We.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),i=t.readUInt32BE(4),r="";for(let s=4;s<8;s++)r+=String.fromCharCode(t[s]);let n=!!(t[4]&32);if(!this._hasIHDR&&i!==Ve.TYPE_IHDR){this.error(new Error("Expected IHDR on beggining"));return}if(this._crc=new F1,this._crc.write(Buffer.from(r)),this._chunks[i])return this._chunks[i](e);if(!n){this.error(new Error("Unsupported critical chunk type "+r));return}this.read(e+4,this._skipChunk.bind(this))};We.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))};We.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))};We.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),i=this._crc.crc32();if(this._options.checkCRC&&i!==e){this.error(new Error("Crc error - "+e+" - "+i));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))};We.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))};We.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),i=t.readUInt32BE(4),r=t[8],n=t[9],s=t[10],o=t[11],a=t[12];if(r!==8&&r!==4&&r!==2&&r!==1&&r!==16){this.error(new Error("Unsupported bit depth "+r));return}if(!(n in Ve.COLORTYPE_TO_BPP_MAP)){this.error(new Error("Unsupported color type"));return}if(s!==0){this.error(new Error("Unsupported compression method"));return}if(o!==0){this.error(new Error("Unsupported filter method"));return}if(a!==0&&a!==1){this.error(new Error("Unsupported interlace method"));return}this._colorType=n;let l=Ve.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:i,depth:r,interlace:!!a,palette:!!(n&Ve.COLORTYPE_PALETTE),color:!!(n&Ve.COLORTYPE_COLOR),alpha:!!(n&Ve.COLORTYPE_ALPHA),bpp:l,colorType:n}),this._handleChunkEnd()};We.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))};We.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let i=0;i<e;i++)this._palette.push([t[i*3],t[i*3+1],t[i*3+2],255]);this.palette(this._palette),this._handleChunkEnd()};We.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))};We.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===Ve.COLORTYPE_PALETTE_COLOR){if(this._palette.length===0){this.error(new Error("Transparency chunk must be after palette"));return}if(t.length>this._palette.length){this.error(new Error("More transparent colors than palette size"));return}for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===Ve.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===Ve.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()};We.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))};We.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/Ve.GAMMA_DIVISION),this._handleChunkEnd()};We.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))};We.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===Ve.COLORTYPE_PALETTE_COLOR&&this._palette.length===0)throw new Error("Expected palette not found");this.inflateData(e);let i=t-e.length;i>0?this._handleIDAT(i):this._handleChunkEnd()};We.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))};We.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}});var dl=w(nd=>{"use strict";var rd=al(),q1=[function(){},function(t,e,i,r){if(r===e.length)throw new Error("Ran out of data");let n=e[r];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=255},function(t,e,i,r){if(r+1>=e.length)throw new Error("Ran out of data");let n=e[r];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=e[r+1]},function(t,e,i,r){if(r+2>=e.length)throw new Error("Ran out of data");t[i]=e[r],t[i+1]=e[r+1],t[i+2]=e[r+2],t[i+3]=255},function(t,e,i,r){if(r+3>=e.length)throw new Error("Ran out of data");t[i]=e[r],t[i+1]=e[r+1],t[i+2]=e[r+2],t[i+3]=e[r+3]}],D1=[function(){},function(t,e,i,r){let n=e[0];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=r},function(t,e,i){let r=e[0];t[i]=r,t[i+1]=r,t[i+2]=r,t[i+3]=e[1]},function(t,e,i,r){t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=r},function(t,e,i){t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=e[3]}];function j1(t,e){let i=[],r=0;function n(){if(r===t.length)throw new Error("Ran out of data");let s=t[r];r++;let o,a,l,c,u,f,d,g;switch(e){default:throw new Error("unrecognised depth");case 16:d=t[r],r++,i.push((s<<8)+d);break;case 4:d=s&15,g=s>>4,i.push(g,d);break;case 2:u=s&3,f=s>>2&3,d=s>>4&3,g=s>>6&3,i.push(g,d,f,u);break;case 1:o=s&1,a=s>>1&1,l=s>>2&1,c=s>>3&1,u=s>>4&1,f=s>>5&1,d=s>>6&1,g=s>>7&1,i.push(g,d,f,u,c,l,a,o);break}}return{get:function(s){for(;i.length<s;)n();let o=i.slice(0,s);return i=i.slice(s),o},resetAfterLine:function(){i.length=0},end:function(){if(r!==t.length)throw new Error("extra data found")}}}function U1(t,e,i,r,n,s){let o=t.width,a=t.height,l=t.index;for(let c=0;c<a;c++)for(let u=0;u<o;u++){let f=i(u,c,l);q1[r](e,n,f,s),s+=r}return s}function $1(t,e,i,r,n,s){let o=t.width,a=t.height,l=t.index;for(let c=0;c<a;c++){for(let u=0;u<o;u++){let f=n.get(r),d=i(u,c,l);D1[r](e,f,d,s)}n.resetAfterLine()}}nd.dataToBitMap=function(t,e){let i=e.width,r=e.height,n=e.depth,s=e.bpp,o=e.interlace,a;n!==8&&(a=j1(t,n));let l;n<=8?l=Buffer.alloc(i*r*4):l=new Uint16Array(i*r*4);let c=Math.pow(2,n)-1,u=0,f,d;if(o)f=rd.getImagePasses(i,r),d=rd.getInterlaceIterator(i,r);else{let g=0;d=function(){let m=g;return g+=4,m},f=[{width:i,height:r}]}for(let g=0;g<f.length;g++)n===8?u=U1(f[g],l,d,s,t,u):$1(f[g],l,d,s,a,c);if(n===8){if(u!==t.length)throw new Error("extra data found")}else a.end();return l}});var ml=w((x2,sd)=>{"use strict";function V1(t,e,i,r,n){let s=0;for(let o=0;o<r;o++)for(let a=0;a<i;a++){let l=n[t[s]];if(!l)throw new Error("index "+t[s]+" not in palette");for(let c=0;c<4;c++)e[s+c]=l[c];s+=4}}function H1(t,e,i,r,n){let s=0;for(let o=0;o<r;o++)for(let a=0;a<i;a++){let l=!1;if(n.length===1?n[0]===t[s]&&(l=!0):n[0]===t[s]&&n[1]===t[s+1]&&n[2]===t[s+2]&&(l=!0),l)for(let c=0;c<4;c++)e[s+c]=0;s+=4}}function G1(t,e,i,r,n){let s=255,o=Math.pow(2,n)-1,a=0;for(let l=0;l<r;l++)for(let c=0;c<i;c++){for(let u=0;u<4;u++)e[a+u]=Math.floor(t[a+u]*s/o+.5);a+=4}}sd.exports=function(t,e,i=!1){let r=e.depth,n=e.width,s=e.height,o=e.colorType,a=e.transColor,l=e.palette,c=t;return o===3?V1(t,c,n,s,l):(a&&H1(t,c,n,s,a),r!==8&&!i&&(r===16&&(c=Buffer.alloc(n*s*4)),G1(t,c,n,s,r))),c}});var ld=w((S2,ad)=>{"use strict";var Y1=require("util"),gl=require("zlib"),od=sl(),W1=Xp(),z1=pl(),K1=dl(),J1=ml(),Vt=ad.exports=function(t){od.call(this),this._parser=new z1(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};Y1.inherits(Vt,od);Vt.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0};Vt.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=gl.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let i=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,r=Math.max(i,gl.Z_MIN_CHUNK);this._inflate=gl.createInflate({chunkSize:r});let n=i,s=this.emit.bind(this,"error");this._inflate.on("error",function(a){n&&s(a)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(a){n&&(a.length>n&&(a=a.slice(0,n)),n-=a.length,o(a))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)};Vt.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new W1(this._bitmapInfo)};Vt.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t};Vt.prototype._handlePalette=function(t){this._bitmapInfo.palette=t};Vt.prototype._simpleTransparency=function(){this._metaData.alpha=!0};Vt.prototype._headersFinished=function(){this.emit("metadata",this._metaData)};Vt.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))};Vt.prototype._complete=function(t){if(this.errord)return;let e;try{let i=K1.dataToBitMap(t,this._bitmapInfo);e=J1(i,this._bitmapInfo,this._options.skipRescale),i=null}catch(i){this._handleError(i);return}this.emit("parsed",e)}});var ud=w((E2,cd)=>{"use strict";var Ct=Or();cd.exports=function(t,e,i,r){let n=[Ct.COLORTYPE_COLOR_ALPHA,Ct.COLORTYPE_ALPHA].indexOf(r.colorType)!==-1;if(r.colorType===r.inputColorType){let m=function(){let v=new ArrayBuffer(2);return new DataView(v).setInt16(0,256,!0),new Int16Array(v)[0]!==256}();if(r.bitDepth===8||r.bitDepth===16&&m)return t}let s=r.bitDepth!==16?t:new Uint16Array(t.buffer),o=255,a=Ct.COLORTYPE_TO_BPP_MAP[r.inputColorType];a===4&&!r.inputHasAlpha&&(a=3);let l=Ct.COLORTYPE_TO_BPP_MAP[r.colorType];r.bitDepth===16&&(o=65535,l*=2);let c=Buffer.alloc(e*i*l),u=0,f=0,d=r.bgColor||{};d.red===void 0&&(d.red=o),d.green===void 0&&(d.green=o),d.blue===void 0&&(d.blue=o);function g(){let m,v,b,_=o;switch(r.inputColorType){case Ct.COLORTYPE_COLOR_ALPHA:_=s[u+3],m=s[u],v=s[u+1],b=s[u+2];break;case Ct.COLORTYPE_COLOR:m=s[u],v=s[u+1],b=s[u+2];break;case Ct.COLORTYPE_ALPHA:_=s[u+1],m=s[u],v=m,b=m;break;case Ct.COLORTYPE_GRAYSCALE:m=s[u],v=m,b=m;break;default:throw new Error("input color type:"+r.inputColorType+" is not supported at present")}return r.inputHasAlpha&&(n||(_/=o,m=Math.min(Math.max(Math.round((1-_)*d.red+_*m),0),o),v=Math.min(Math.max(Math.round((1-_)*d.green+_*v),0),o),b=Math.min(Math.max(Math.round((1-_)*d.blue+_*b),0),o))),{red:m,green:v,blue:b,alpha:_}}for(let m=0;m<i;m++)for(let v=0;v<e;v++){let b=g(s,u);switch(r.colorType){case Ct.COLORTYPE_COLOR_ALPHA:case Ct.COLORTYPE_COLOR:r.bitDepth===8?(c[f]=b.red,c[f+1]=b.green,c[f+2]=b.blue,n&&(c[f+3]=b.alpha)):(c.writeUInt16BE(b.red,f),c.writeUInt16BE(b.green,f+2),c.writeUInt16BE(b.blue,f+4),n&&c.writeUInt16BE(b.alpha,f+6));break;case Ct.COLORTYPE_ALPHA:case Ct.COLORTYPE_GRAYSCALE:{let _=(b.red+b.green+b.blue)/3;r.bitDepth===8?(c[f]=_,n&&(c[f+1]=b.alpha)):(c.writeUInt16BE(_,f),n&&c.writeUInt16BE(b.alpha,f+2));break}default:throw new Error("unrecognised color Type "+r.colorType)}u+=a,f+=l}return c}});var pd=w((k2,hd)=>{"use strict";var fd=ll();function Z1(t,e,i,r,n){for(let s=0;s<i;s++)r[n+s]=t[e+s]}function Q1(t,e,i){let r=0,n=e+i;for(let s=e;s<n;s++)r+=Math.abs(t[s]);return r}function X1(t,e,i,r,n,s){for(let o=0;o<i;o++){let a=o>=s?t[e+o-s]:0,l=t[e+o]-a;r[n+o]=l}}function ex(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,a=t[e+s]-o;n+=Math.abs(a)}return n}function tx(t,e,i,r,n){for(let s=0;s<i;s++){let o=e>0?t[e+s-i]:0,a=t[e+s]-o;r[n+s]=a}}function ix(t,e,i){let r=0,n=e+i;for(let s=e;s<n;s++){let o=e>0?t[s-i]:0,a=t[s]-o;r+=Math.abs(a)}return r}function rx(t,e,i,r,n,s){for(let o=0;o<i;o++){let a=o>=s?t[e+o-s]:0,l=e>0?t[e+o-i]:0,c=t[e+o]-(a+l>>1);r[n+o]=c}}function nx(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,a=e>0?t[e+s-i]:0,l=t[e+s]-(o+a>>1);n+=Math.abs(l)}return n}function sx(t,e,i,r,n,s){for(let o=0;o<i;o++){let a=o>=s?t[e+o-s]:0,l=e>0?t[e+o-i]:0,c=e>0&&o>=s?t[e+o-(i+s)]:0,u=t[e+o]-fd(a,l,c);r[n+o]=u}}function ox(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,a=e>0?t[e+s-i]:0,l=e>0&&s>=r?t[e+s-(i+r)]:0,c=t[e+s]-fd(o,a,l);n+=Math.abs(c)}return n}var ax={0:Z1,1:X1,2:tx,3:rx,4:sx},lx={0:Q1,1:ex,2:ix,3:nx,4:ox};hd.exports=function(t,e,i,r,n){let s;if(!("filterType"in r)||r.filterType===-1)s=[0,1,2,3,4];else if(typeof r.filterType=="number")s=[r.filterType];else throw new Error("unrecognised filter types");r.bitDepth===16&&(n*=2);let o=e*n,a=0,l=0,c=Buffer.alloc((o+1)*i),u=s[0];for(let f=0;f<i;f++){if(s.length>1){let d=1/0;for(let g=0;g<s.length;g++){let m=lx[s[g]](t,l,o,n);m<d&&(u=s[g],d=m)}}c[a]=u,a++,ax[u](t,l,o,c,a,n),a+=o,l+=o}return c}});var vl=w((O2,dd)=>{"use strict";var it=Or(),cx=hl(),ux=ud(),fx=pd(),hx=require("zlib"),mi=dd.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32*1024,t.deflateLevel=t.deflateLevel!=null?t.deflateLevel:9,t.deflateStrategy=t.deflateStrategy!=null?t.deflateStrategy:3,t.inputHasAlpha=t.inputHasAlpha!=null?t.inputHasAlpha:!0,t.deflateFactory=t.deflateFactory||hx.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType=typeof t.colorType=="number"?t.colorType:it.COLORTYPE_COLOR_ALPHA,t.inputColorType=typeof t.inputColorType=="number"?t.inputColorType:it.COLORTYPE_COLOR_ALPHA,[it.COLORTYPE_GRAYSCALE,it.COLORTYPE_COLOR,it.COLORTYPE_COLOR_ALPHA,it.COLORTYPE_ALPHA].indexOf(t.colorType)===-1)throw new Error("option color type:"+t.colorType+" is not supported at present");if([it.COLORTYPE_GRAYSCALE,it.COLORTYPE_COLOR,it.COLORTYPE_COLOR_ALPHA,it.COLORTYPE_ALPHA].indexOf(t.inputColorType)===-1)throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(t.bitDepth!==8&&t.bitDepth!==16)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};mi.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}};mi.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())};mi.prototype.filterData=function(t,e,i){let r=ux(t,e,i,this._options),n=it.COLORTYPE_TO_BPP_MAP[this._options.colorType];return fx(r,e,i,this._options,n)};mi.prototype._packChunk=function(t,e){let i=e?e.length:0,r=Buffer.alloc(i+12);return r.writeUInt32BE(i,0),r.writeUInt32BE(t,4),e&&e.copy(r,8),r.writeInt32BE(cx.crc32(r.slice(4,r.length-4)),r.length-4),r};mi.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*it.GAMMA_DIVISION),0),this._packChunk(it.TYPE_gAMA,e)};mi.prototype.packIHDR=function(t,e){let i=Buffer.alloc(13);return i.writeUInt32BE(t,0),i.writeUInt32BE(e,4),i[8]=this._options.bitDepth,i[9]=this._options.colorType,i[10]=0,i[11]=0,i[12]=0,this._packChunk(it.TYPE_IHDR,i)};mi.prototype.packIDAT=function(t){return this._packChunk(it.TYPE_IDAT,t)};mi.prototype.packIEND=function(){return this._packChunk(it.TYPE_IEND,null)}});var yd=w((C2,vd)=>{"use strict";var px=require("util"),md=require("stream"),dx=Or(),mx=vl(),gd=vd.exports=function(t){md.call(this);let e=t||{};this._packer=new mx(e),this._deflate=this._packer.createDeflate(),this.readable=!0};px.inherits(gd,md);gd.prototype.pack=function(t,e,i,r){this.emit("data",Buffer.from(dx.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,i)),r&&this.emit("data",this._packer.packGAMA(r));let n=this._packer.filterData(t,e,i);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(s){this.emit("data",this._packer.packIDAT(s))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(n)}});var Ed=w((cn,Sd)=>{"use strict";var bd=require("assert").ok,Cr=require("zlib"),gx=require("util"),_d=require("buffer").kMaxLength;function Vi(t){if(!(this instanceof Vi))return new Vi(t);t&&t.chunkSize<Cr.Z_MIN_CHUNK&&(t.chunkSize=Cr.Z_MIN_CHUNK),Cr.Inflate.call(this,t),this._offset=this._offset===void 0?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&t.maxLength!=null&&(this._maxLength=t.maxLength)}function vx(t){return new Vi(t)}function wd(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}Vi.prototype._processChunk=function(t,e,i){if(typeof i=="function")return Cr.Inflate._processChunk.call(this,t,e,i);let r=this,n=t&&t.length,s=this._chunkSize-this._offset,o=this._maxLength,a=0,l=[],c=0,u;this.on("error",function(m){u=m});function f(m,v){if(r._hadError)return;let b=s-v;if(bd(b>=0,"have should not go down"),b>0){let _=r._buffer.slice(r._offset,r._offset+b);if(r._offset+=b,_.length>o&&(_=_.slice(0,o)),l.push(_),c+=_.length,o-=_.length,o===0)return!1}return(v===0||r._offset>=r._chunkSize)&&(s=r._chunkSize,r._offset=0,r._buffer=Buffer.allocUnsafe(r._chunkSize)),v===0?(a+=n-m,n=m,!0):!1}bd(this._handle,"zlib binding closed");let d;do d=this._handle.writeSync(e,t,a,n,this._buffer,this._offset,s),d=d||this._writeState;while(!this._hadError&&f(d[0],d[1]));if(this._hadError)throw u;if(c>=_d)throw wd(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+_d.toString(16)+" bytes");let g=Buffer.concat(l,c);return wd(this),g};gx.inherits(Vi,Cr.Inflate);function yx(t,e){if(typeof e=="string"&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");let i=t._finishFlushFlag;return i==null&&(i=Cr.Z_FINISH),t._processChunk(e,i)}function xd(t,e){return yx(new Vi(e),t)}Sd.exports=cn=xd;cn.Inflate=Vi;cn.createInflate=vx;cn.inflateSync=xd});var yl=w((T2,Od)=>{"use strict";var kd=Od.exports=function(t){this._buffer=t,this._reads=[]};kd.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})};kd.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}if(this._reads.length>0)throw new Error("There are some read requests waitng on finished stream");if(this._buffer.length>0)throw new Error("unrecognised content at end of stream")}});var Td=w(Cd=>{"use strict";var bx=yl(),_x=cl();Cd.process=function(t,e){let i=[],r=new bx(t);return new _x(e,{read:r.read.bind(r),write:function(s){i.push(s)},complete:function(){}}).start(),r.process(),Buffer.concat(i)}});var Ld=w((I2,Nd)=>{"use strict";var Ad=!0,Id=require("zlib"),wx=Ed();Id.deflateSync||(Ad=!1);var xx=yl(),Sx=Td(),Ex=pl(),kx=dl(),Ox=ml();Nd.exports=function(t,e){if(!Ad)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let i;function r(k){i=k}let n;function s(k){n=k}function o(k){n.transColor=k}function a(k){n.palette=k}function l(){n.alpha=!0}let c;function u(k){c=k}let f=[];function d(k){f.push(k)}let g=new xx(t);if(new Ex(e,{read:g.read.bind(g),error:r,metadata:s,gamma:u,palette:a,transColor:o,inflateData:d,simpleTransparency:l}).start(),g.process(),i)throw i;let v=Buffer.concat(f);f.length=0;let b;if(n.interlace)b=Id.inflateSync(v);else{let E=((n.width*n.bpp*n.depth+7>>3)+1)*n.height;b=wx(v,{chunkSize:E,maxLength:E})}if(v=null,!b||!b.length)throw new Error("bad png - invalid inflate data response");let _=Sx.process(b,n);v=null;let S=kx.dataToBitMap(_,n);_=null;let O=Ox(S,n,e.skipRescale);return n.data=O,n.gamma=c||0,n}});var Md=w((N2,Pd)=>{"use strict";var Bd=!0,Rd=require("zlib");Rd.deflateSync||(Bd=!1);var Cx=Or(),Tx=vl();Pd.exports=function(t,e){if(!Bd)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let i=e||{},r=new Tx(i),n=[];n.push(Buffer.from(Cx.PNG_SIGNATURE)),n.push(r.packIHDR(t.width,t.height)),t.gamma&&n.push(r.packGAMA(t.gamma));let s=r.filterData(t.data,t.width,t.height),o=Rd.deflateSync(s,r.getDeflateOptions());if(s=null,!o||!o.length)throw new Error("bad png - invalid compressed data response");return n.push(r.packIDAT(o)),n.push(r.packIEND()),Buffer.concat(n)}});var Fd=w(bl=>{"use strict";var Ax=Ld(),Ix=Md();bl.read=function(t,e){return Ax(t,e||{})};bl.write=function(t,e){return Ix(t,e)}});var jd=w(Dd=>{"use strict";var Nx=require("util"),qd=require("stream"),Lx=ld(),Bx=yd(),Rx=Fd(),at=Dd.PNG=function(t){qd.call(this),t=t||{},this.width=t.width|0,this.height=t.height|0,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new Lx(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(e){this.data=e,this.emit("parsed",e)}.bind(this)),this._packer=new Bx(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};Nx.inherits(at,qd);at.sync=Rx;at.prototype.pack=function(){return!this.data||!this.data.length?(this.emit("error","No data provided"),this):(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this)};at.prototype.parse=function(t,e){if(e){let i,r;i=function(n){this.removeListener("error",r),this.data=n,e(null,this)}.bind(this),r=function(n){this.removeListener("parsed",i),e(n,null)}.bind(this),this.once("parsed",i),this.once("error",r)}return this.end(t),this};at.prototype.write=function(t){return this._parser.write(t),!0};at.prototype.end=function(t){this._parser.end(t)};at.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)};at.prototype._gamma=function(t){this.gamma=t};at.prototype._handleClose=function(){!this._parser.writable&&!this._packer.readable&&this.emit("close")};at.bitblt=function(t,e,i,r,n,s,o,a){if(i|=0,r|=0,n|=0,s|=0,o|=0,a|=0,i>t.width||r>t.height||i+n>t.width||r+s>t.height)throw new Error("bitblt reading outside image");if(o>e.width||a>e.height||o+n>e.width||a+s>e.height)throw new Error("bitblt writing outside image");for(let l=0;l<s;l++)t.data.copy(e.data,(a+l)*e.width+o<<2,(r+l)*t.width+i<<2,(r+l)*t.width+i+n<<2)};at.prototype.bitblt=function(t,e,i,r,n,s,o){return at.bitblt(this,t,e,i,r,n,s,o),this};at.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let i=0;i<t.width;i++){let r=t.width*e+i<<2;for(let n=0;n<3;n++){let s=t.data[r+n]/255;s=Math.pow(s,1/2.2/t.gamma),t.data[r+n]=Math.round(s*255)}}t.gamma=0}};at.prototype.adjustGamma=function(){at.adjustGamma(this)}});var un=w(wl=>{var Fs=class extends Error{constructor(e,i,r){super(r),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=i,this.exitCode=e,this.nestedError=void 0}},_l=class extends Fs{constructor(e){super(1,"commander.invalidArgument",e),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}};wl.CommanderError=Fs;wl.InvalidArgumentError=_l});var qs=w(Sl=>{var{InvalidArgumentError:Px}=un(),xl=class{constructor(e,i){switch(this.description=i||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,e[0]){case"<":this.required=!0,this._name=e.slice(1,-1);break;case"[":this.required=!1,this._name=e.slice(1,-1);break;default:this.required=!0,this._name=e;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(e,i){return i===this.defaultValue||!Array.isArray(i)?[e]:i.concat(e)}default(e,i){return this.defaultValue=e,this.defaultValueDescription=i,this}argParser(e){return this.parseArg=e,this}choices(e){return this.argChoices=e,this.parseArg=(i,r)=>{if(!e.includes(i))throw new Px(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(i,r):i},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}};function Mx(t){let e=t.name()+(t.variadic===!0?"...":"");return t.required?"<"+e+">":"["+e+"]"}Sl.Argument=xl;Sl.humanReadableArgName=Mx});var kl=w(Ud=>{var{humanReadableArgName:Fx}=qs(),El=class{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1}visibleCommands(e){let i=e.commands.filter(r=>!r._hidden);if(e._hasImplicitHelpCommand()){let[,r,n]=e._helpCommandnameAndArgs.match(/([^ ]+) *(.*)/),s=e.createCommand(r).helpOption(!1);s.description(e._helpCommandDescription),n&&s.arguments(n),i.push(s)}return this.sortSubcommands&&i.sort((r,n)=>r.name().localeCompare(n.name())),i}visibleOptions(e){let i=e.options.filter(s=>!s.hidden),r=e._hasHelpOption&&e._helpShortFlag&&!e._findOption(e._helpShortFlag),n=e._hasHelpOption&&!e._findOption(e._helpLongFlag);if(r||n){let s;r?n?s=e.createOption(e._helpFlags,e._helpDescription):s=e.createOption(e._helpShortFlag,e._helpDescription):s=e.createOption(e._helpLongFlag,e._helpDescription),i.push(s)}if(this.sortOptions){let s=o=>o.short?o.short.replace(/^-/,""):o.long.replace(/^--/,"");i.sort((o,a)=>s(o).localeCompare(s(a)))}return i}visibleArguments(e){return e._argsDescription&&e._args.forEach(i=>{i.description=i.description||e._argsDescription[i.name()]||""}),e._args.find(i=>i.description)?e._args:[]}subcommandTerm(e){let i=e._args.map(r=>Fx(r)).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(i?" "+i:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,i){return i.visibleCommands(e).reduce((r,n)=>Math.max(r,i.subcommandTerm(n).length),0)}longestOptionTermLength(e,i){return i.visibleOptions(e).reduce((r,n)=>Math.max(r,i.optionTerm(n).length),0)}longestArgumentTermLength(e,i){return i.visibleArguments(e).reduce((r,n)=>Math.max(r,i.argumentTerm(n).length),0)}commandUsage(e){let i=e._name;e._aliases[0]&&(i=i+"|"+e._aliases[0]);let r="";for(let n=e.parent;n;n=n.parent)r=n.name()+" "+r;return r+i+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.description()}optionDescription(e){let i=[];return e.argChoices&&!e.negate&&i.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&!e.negate&&i.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),e.envVar!==void 0&&i.push(`env: ${e.envVar}`),i.length>0?`${e.description} (${i.join(", ")})`:e.description}argumentDescription(e){let i=[];if(e.argChoices&&i.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&i.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),i.length>0){let r=`(${i.join(", ")})`;return e.description?`${e.description} ${r}`:r}return e.description}formatHelp(e,i){let r=i.padWidth(e,i),n=i.helpWidth||80,s=2,o=2;function a(m,v){if(v){let b=`${m.padEnd(r+o)}${v}`;return i.wrap(b,n-s,r+o)}return m}function l(m){return m.join(`
`).replace(/^/gm," ".repeat(s))}let c=[`Usage: ${i.commandUsage(e)}`,""],u=i.commandDescription(e);u.length>0&&(c=c.concat([u,""]));let f=i.visibleArguments(e).map(m=>a(i.argumentTerm(m),i.argumentDescription(m)));f.length>0&&(c=c.concat(["Arguments:",l(f),""]));let d=i.visibleOptions(e).map(m=>a(i.optionTerm(m),i.optionDescription(m)));d.length>0&&(c=c.concat(["Options:",l(d),""]));let g=i.visibleCommands(e).map(m=>a(i.subcommandTerm(m),i.subcommandDescription(m)));return g.length>0&&(c=c.concat(["Commands:",l(g),""])),c.join(`
`)}padWidth(e,i){return Math.max(i.longestOptionTermLength(e,i),i.longestSubcommandTermLength(e,i),i.longestArgumentTermLength(e,i))}wrap(e,i,r,n=40){if(e.match(/[\n]\s+/))return e;let s=i-r;if(s<n)return e;let o=e.substr(0,r),a=e.substr(r),l=" ".repeat(r),c=new RegExp(".{1,"+(s-1)+"}([\\s\u200B]|$)|[^\\s\u200B]+?([\\s\u200B]|$)","g"),u=a.match(c)||[];return o+u.map((f,d)=>(f.slice(-1)===`
`&&(f=f.slice(0,f.length-1)),(d>0?l:"")+f.trimRight())).join(`
`)}};Ud.Help=El});var Tl=w(Cl=>{var{InvalidArgumentError:qx}=un(),Ol=class{constructor(e,i){this.flags=e,this.description=i||"",this.required=e.includes("<"),this.optional=e.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(e),this.mandatory=!1;let r=$d(e);this.short=r.shortFlag,this.long=r.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0}default(e,i){return this.defaultValue=e,this.defaultValueDescription=i,this}env(e){return this.envVar=e,this}argParser(e){return this.parseArg=e,this}makeOptionMandatory(e=!0){return this.mandatory=!!e,this}hideHelp(e=!0){return this.hidden=!!e,this}_concatValue(e,i){return i===this.defaultValue||!Array.isArray(i)?[e]:i.concat(e)}choices(e){return this.argChoices=e,this.parseArg=(i,r)=>{if(!e.includes(i))throw new qx(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(i,r):i},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return Dx(this.name().replace(/^no-/,""))}is(e){return this.short===e||this.long===e}};function Dx(t){return t.split("-").reduce((e,i)=>e+i[0].toUpperCase()+i.slice(1))}function $d(t){let e,i,r=t.split(/[ |,]+/);return r.length>1&&!/^[[<]/.test(r[1])&&(e=r.shift()),i=r.shift(),!e&&/^-[^-]$/.test(i)&&(e=i,i=void 0),{shortFlag:e,longFlag:i}}Cl.Option=Ol;Cl.splitOptionFlags=$d});var Hd=w(Vd=>{function jx(t,e){if(Math.abs(t.length-e.length)>3)return Math.max(t.length,e.length);let i=[];for(let r=0;r<=t.length;r++)i[r]=[r];for(let r=0;r<=e.length;r++)i[0][r]=r;for(let r=1;r<=e.length;r++)for(let n=1;n<=t.length;n++){let s=1;t[n-1]===e[r-1]?s=0:s=1,i[n][r]=Math.min(i[n-1][r]+1,i[n][r-1]+1,i[n-1][r-1]+s),n>1&&r>1&&t[n-1]===e[r-2]&&t[n-2]===e[r-1]&&(i[n][r]=Math.min(i[n][r],i[n-2][r-2]+1))}return i[t.length][e.length]}function Ux(t,e){if(!e||e.length===0)return"";e=Array.from(new Set(e));let i=t.startsWith("--");i&&(t=t.slice(2),e=e.map(o=>o.slice(2)));let r=[],n=3,s=.4;return e.forEach(o=>{if(o.length<=1)return;let a=jx(t,o),l=Math.max(t.length,o.length);(l-a)/l>s&&(a<n?(n=a,r=[o]):a===n&&r.push(o))}),r.sort((o,a)=>o.localeCompare(a)),i&&(r=r.map(o=>`--${o}`)),r.length>1?`
(Did you mean one of ${r.join(", ")}?)`:r.length===1?`
(Did you mean ${r[0]}?)`:""}Vd.suggestSimilar=Ux});var Kd=w(zd=>{var $x=require("events").EventEmitter,Al=require("child_process"),Hi=require("path"),Il=require("fs"),{Argument:Vx,humanReadableArgName:Hx}=qs(),{CommanderError:Nl}=un(),{Help:Gx}=kl(),{Option:Yx,splitOptionFlags:Wx}=Tl(),{suggestSimilar:Gd}=Hd(),Bl=class t extends $x{constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this._args=[],this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!1,this._outputConfiguration={writeOut:i=>process.stdout.write(i),writeErr:i=>process.stderr.write(i),getOutHelpWidth:()=>process.stdout.isTTY?process.stdout.columns:void 0,getErrHelpWidth:()=>process.stderr.isTTY?process.stderr.columns:void 0,outputError:(i,r)=>r(i)},this._hidden=!1,this._hasHelpOption=!0,this._helpFlags="-h, --help",this._helpDescription="display help for command",this._helpShortFlag="-h",this._helpLongFlag="--help",this._addImplicitHelpCommand=void 0,this._helpCommandName="help",this._helpCommandnameAndArgs="help [command]",this._helpCommandDescription="display help for command",this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._hasHelpOption=e._hasHelpOption,this._helpFlags=e._helpFlags,this._helpDescription=e._helpDescription,this._helpShortFlag=e._helpShortFlag,this._helpLongFlag=e._helpLongFlag,this._helpCommandName=e._helpCommandName,this._helpCommandnameAndArgs=e._helpCommandnameAndArgs,this._helpCommandDescription=e._helpCommandDescription,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}command(e,i,r){let n=i,s=r;typeof n=="object"&&n!==null&&(s=n,n=null),s=s||{};let[,o,a]=e.match(/([^ ]+) *(.*)/),l=this.createCommand(o);return n&&(l.description(n),l._executableHandler=!0),s.isDefault&&(this._defaultCommandName=l._name),l._hidden=!!(s.noHelp||s.hidden),l._executableFile=s.executableFile||null,a&&l.arguments(a),this.commands.push(l),l.parent=this,l.copyInheritedSettings(this),n?this:l}createCommand(e){return new t(e)}createHelp(){return Object.assign(new Gx,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,i){if(!e._name)throw new Error("Command passed to .addCommand() must have a name");function r(n){n.forEach(s=>{if(s._executableHandler&&!s._executableFile)throw new Error(`Must specify executableFile for deeply nested executable: ${s.name()}`);r(s.commands)})}return r(e.commands),i=i||{},i.isDefault&&(this._defaultCommandName=e._name),(i.noHelp||i.hidden)&&(e._hidden=!0),this.commands.push(e),e.parent=this,this}createArgument(e,i){return new Vx(e,i)}argument(e,i,r,n){let s=this.createArgument(e,i);return typeof r=="function"?s.default(n).argParser(r):s.default(r),this.addArgument(s),this}arguments(e){return e.split(/ +/).forEach(i=>{this.argument(i)}),this}addArgument(e){let i=this._args.slice(-1)[0];if(i&&i.variadic)throw new Error(`only the last argument can be variadic '${i.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this._args.push(e),this}addHelpCommand(e,i){return e===!1?this._addImplicitHelpCommand=!1:(this._addImplicitHelpCommand=!0,typeof e=="string"&&(this._helpCommandName=e.split(" ")[0],this._helpCommandnameAndArgs=e),this._helpCommandDescription=i||this._helpCommandDescription),this}_hasImplicitHelpCommand(){return this._addImplicitHelpCommand===void 0?this.commands.length&&!this._actionHandler&&!this._findCommand("help"):this._addImplicitHelpCommand}hook(e,i){let r=["preAction","postAction"];if(!r.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${r.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(i):this._lifeCycleHooks[e]=[i],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=i=>{if(i.code!=="commander.executeSubCommandAsync")throw i},this}_exit(e,i,r){this._exitCallback&&this._exitCallback(new Nl(e,i,r)),process.exit(e)}action(e){let i=r=>{let n=this._args.length,s=r.slice(0,n);return this._storeOptionsAsProperties?s[n]=this:s[n]=this.opts(),s.push(this),e.apply(this,s)};return this._actionHandler=i,this}createOption(e,i){return new Yx(e,i)}addOption(e){let i=e.name(),r=e.attributeName(),n=e.defaultValue;if(e.negate||e.optional||e.required||typeof n=="boolean"){if(e.negate){let o=e.long.replace(/^--no-/,"--");n=this._findOption(o)?this.getOptionValue(r):!0}n!==void 0&&this.setOptionValueWithSource(r,n,"default")}this.options.push(e);let s=(o,a,l)=>{let c=this.getOptionValue(r);if(o!==null&&e.parseArg)try{o=e.parseArg(o,c===void 0?n:c)}catch(u){if(u.code==="commander.invalidArgument"){let f=`${a} ${u.message}`;this._displayError(u.exitCode,u.code,f)}throw u}else o!==null&&e.variadic&&(o=e._concatValue(o,c));typeof c=="boolean"||typeof c=="undefined"?o==null?this.setOptionValueWithSource(r,e.negate?!1:n||!0,l):this.setOptionValueWithSource(r,o,l):o!==null&&this.setOptionValueWithSource(r,e.negate?!1:o,l)};return this.on("option:"+i,o=>{let a=`error: option '${e.flags}' argument '${o}' is invalid.`;s(o,a,"cli")}),e.envVar&&this.on("optionEnv:"+i,o=>{let a=`error: option '${e.flags}' value '${o}' from env '${e.envVar}' is invalid.`;s(o,a,"env")}),this}_optionEx(e,i,r,n,s){let o=this.createOption(i,r);if(o.makeOptionMandatory(!!e.mandatory),typeof n=="function")o.default(s).argParser(n);else if(n instanceof RegExp){let a=n;n=(l,c)=>{let u=a.exec(l);return u?u[0]:c},o.default(s).argParser(n)}else o.default(n);return this.addOption(o)}option(e,i,r,n){return this._optionEx({},e,i,r,n)}requiredOption(e,i,r,n){return this._optionEx({mandatory:!0},e,i,r,n)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){if(this._passThroughOptions=!!e,this.parent&&e&&!this.parent._enablePositionalOptions)throw new Error("passThroughOptions can not be used without turning on enablePositionalOptions for parent command(s)");return this}storeOptionsAsProperties(e=!0){if(this._storeOptionsAsProperties=!!e,this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");return this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,i){return this._storeOptionsAsProperties?this[e]=i:this._optionValues[e]=i,this}setOptionValueWithSource(e,i,r){return this.setOptionValue(e,i),this._optionValueSources[e]=r,this}getOptionValueSource(e){return this._optionValueSources[e]}_prepareUserArgs(e,i){if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");i=i||{},e===void 0&&(e=process.argv,process.versions&&process.versions.electron&&(i.from="electron")),this.rawArgs=e.slice();let r;switch(i.from){case void 0:case"node":this._scriptPath=e[1],r=e.slice(2);break;case"electron":process.defaultApp?(this._scriptPath=e[1],r=e.slice(2)):r=e.slice(1);break;case"user":r=e.slice(0);break;default:throw new Error(`unexpected parse option { from: '${i.from}' }`)}return!this._scriptPath&&require.main&&(this._scriptPath=require.main.filename),this._name=this._name||this._scriptPath&&Hi.basename(this._scriptPath,Hi.extname(this._scriptPath)),r}parse(e,i){let r=this._prepareUserArgs(e,i);return this._parseCommand([],r),this}async parseAsync(e,i){let r=this._prepareUserArgs(e,i);return await this._parseCommand([],r),this}_executeSubCommand(e,i){i=i.slice();let r=!1,n=[".js",".ts",".tsx",".mjs",".cjs"];this._checkForMissingMandatoryOptions();let s=this._scriptPath;!s&&require.main&&(s=require.main.filename);let o;try{let d=Il.realpathSync(s);o=Hi.dirname(d)}catch{o="."}let a=Hi.basename(s,Hi.extname(s))+"-"+e._name;e._executableFile&&(a=e._executableFile);let l=Hi.join(o,a);Il.existsSync(l)?a=l:n.forEach(d=>{Il.existsSync(`${l}${d}`)&&(a=`${l}${d}`)}),r=n.includes(Hi.extname(a));let c;process.platform!=="win32"?r?(i.unshift(a),i=Wd(process.execArgv).concat(i),c=Al.spawn(process.argv[0],i,{stdio:"inherit"})):c=Al.spawn(a,i,{stdio:"inherit"}):(i.unshift(a),i=Wd(process.execArgv).concat(i),c=Al.spawn(process.execPath,i,{stdio:"inherit"})),["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(d=>{process.on(d,()=>{c.killed===!1&&c.exitCode===null&&c.kill(d)})});let f=this._exitCallback;f?c.on("close",()=>{f(new Nl(process.exitCode||0,"commander.executeSubCommandAsync","(close)"))}):c.on("close",process.exit.bind(process)),c.on("error",d=>{if(d.code==="ENOENT"){let g=`'${a}' does not exist
 - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name`;throw new Error(g)}else if(d.code==="EACCES")throw new Error(`'${a}' not executable`);if(!f)process.exit(1);else{let g=new Nl(1,"commander.executeSubCommandAsync","(error)");g.nestedError=d,f(g)}}),this.runningCommand=c}_dispatchSubcommand(e,i,r){let n=this._findCommand(e);if(n||this.help({error:!0}),n._executableHandler)this._executeSubCommand(n,i.concat(r));else return n._parseCommand(i,r)}_checkNumberOfArguments(){this._args.forEach((e,i)=>{e.required&&this.args[i]==null&&this.missingArgument(e.name())}),!(this._args.length>0&&this._args[this._args.length-1].variadic)&&this.args.length>this._args.length&&this._excessArguments(this.args)}_processArguments(){let e=(r,n,s)=>{let o=n;if(n!==null&&r.parseArg)try{o=r.parseArg(n,s)}catch(a){if(a.code==="commander.invalidArgument"){let l=`error: command-argument value '${n}' is invalid for argument '${r.name()}'. ${a.message}`;this._displayError(a.exitCode,a.code,l)}throw a}return o};this._checkNumberOfArguments();let i=[];this._args.forEach((r,n)=>{let s=r.defaultValue;r.variadic?n<this.args.length?(s=this.args.slice(n),r.parseArg&&(s=s.reduce((o,a)=>e(r,a,o),r.defaultValue))):s===void 0&&(s=[]):n<this.args.length&&(s=this.args[n],r.parseArg&&(s=e(r,s,r.defaultValue))),i[n]=s}),this.processedArgs=i}_chainOrCall(e,i){return e&&e.then&&typeof e.then=="function"?e.then(()=>i()):i()}_chainOrCallHooks(e,i){let r=e,n=[];return Ll(this).reverse().filter(s=>s._lifeCycleHooks[i]!==void 0).forEach(s=>{s._lifeCycleHooks[i].forEach(o=>{n.push({hookedCommand:s,callback:o})})}),i==="postAction"&&n.reverse(),n.forEach(s=>{r=this._chainOrCall(r,()=>s.callback(s.hookedCommand,this))}),r}_parseCommand(e,i){let r=this.parseOptions(i);if(this._parseOptionsEnv(),e=e.concat(r.operands),i=r.unknown,this.args=e.concat(i),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),i);if(this._hasImplicitHelpCommand()&&e[0]===this._helpCommandName)return e.length===1&&this.help(),this._dispatchSubcommand(e[1],[],[this._helpLongFlag]);if(this._defaultCommandName)return Yd(this,i),this._dispatchSubcommand(this._defaultCommandName,e,i);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),Yd(this,r.unknown),this._checkForMissingMandatoryOptions();let n=()=>{r.unknown.length>0&&this.unknownOption(r.unknown[0])},s=`command:${this.name()}`;if(this._actionHandler){n(),this._processArguments();let o;return o=this._chainOrCallHooks(o,"preAction"),o=this._chainOrCall(o,()=>this._actionHandler(this.processedArgs)),this.parent&&this.parent.emit(s,e,i),o=this._chainOrCallHooks(o,"postAction"),o}if(this.parent&&this.parent.listenerCount(s))n(),this._processArguments(),this.parent.emit(s,e,i);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,i);this.listenerCount("command:*")?this.emit("command:*",e,i):this.commands.length?this.unknownCommand():(n(),this._processArguments())}else this.commands.length?(n(),this.help({error:!0})):(n(),this._processArguments())}_findCommand(e){if(e)return this.commands.find(i=>i._name===e||i._aliases.includes(e))}_findOption(e){return this.options.find(i=>i.is(e))}_checkForMissingMandatoryOptions(){for(let e=this;e;e=e.parent)e.options.forEach(i=>{i.mandatory&&e.getOptionValue(i.attributeName())===void 0&&e.missingMandatoryOptionValue(i)})}parseOptions(e){let i=[],r=[],n=i,s=e.slice();function o(l){return l.length>1&&l[0]==="-"}let a=null;for(;s.length;){let l=s.shift();if(l==="--"){n===r&&n.push(l),n.push(...s);break}if(a&&!o(l)){this.emit(`option:${a.name()}`,l);continue}if(a=null,o(l)){let c=this._findOption(l);if(c){if(c.required){let u=s.shift();u===void 0&&this.optionMissingArgument(c),this.emit(`option:${c.name()}`,u)}else if(c.optional){let u=null;s.length>0&&!o(s[0])&&(u=s.shift()),this.emit(`option:${c.name()}`,u)}else this.emit(`option:${c.name()}`);a=c.variadic?c:null;continue}}if(l.length>2&&l[0]==="-"&&l[1]!=="-"){let c=this._findOption(`-${l[1]}`);if(c){c.required||c.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${c.name()}`,l.slice(2)):(this.emit(`option:${c.name()}`),s.unshift(`-${l.slice(2)}`));continue}}if(/^--[^=]+=/.test(l)){let c=l.indexOf("="),u=this._findOption(l.slice(0,c));if(u&&(u.required||u.optional)){this.emit(`option:${u.name()}`,l.slice(c+1));continue}}if(o(l)&&(n=r),(this._enablePositionalOptions||this._passThroughOptions)&&i.length===0&&r.length===0){if(this._findCommand(l)){i.push(l),s.length>0&&r.push(...s);break}else if(l===this._helpCommandName&&this._hasImplicitHelpCommand()){i.push(l),s.length>0&&i.push(...s);break}else if(this._defaultCommandName){r.push(l),s.length>0&&r.push(...s);break}}if(this._passThroughOptions){n.push(l),s.length>0&&n.push(...s);break}n.push(l)}return{operands:i,unknown:r}}opts(){if(this._storeOptionsAsProperties){let e={},i=this.options.length;for(let r=0;r<i;r++){let n=this.options[r].attributeName();e[n]=n===this._versionOptionName?this._version:this[n]}return e}return this._optionValues}_displayError(e,i,r){this._outputConfiguration.outputError(`${r}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0})),this._exit(e,i,r)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in process.env){let i=e.attributeName();(this.getOptionValue(i)===void 0||["default","config","env"].includes(this.getOptionValueSource(i)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,process.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}missingArgument(e){let i=`error: missing required argument '${e}'`;this._displayError(1,"commander.missingArgument",i)}optionMissingArgument(e){let i=`error: option '${e.flags}' argument missing`;this._displayError(1,"commander.optionMissingArgument",i)}missingMandatoryOptionValue(e){let i=`error: required option '${e.flags}' not specified`;this._displayError(1,"commander.missingMandatoryOptionValue",i)}unknownOption(e){if(this._allowUnknownOption)return;let i="";if(e.startsWith("--")&&this._showSuggestionAfterError){let n=[],s=this;do{let o=s.createHelp().visibleOptions(s).filter(a=>a.long).map(a=>a.long);n=n.concat(o),s=s.parent}while(s&&!s._enablePositionalOptions);i=Gd(e,n)}let r=`error: unknown option '${e}'${i}`;this._displayError(1,"commander.unknownOption",r)}_excessArguments(e){if(this._allowExcessArguments)return;let i=this._args.length,r=i===1?"":"s",s=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${i} argument${r} but got ${e.length}.`;this._displayError(1,"commander.excessArguments",s)}unknownCommand(){let e=this.args[0],i="";if(this._showSuggestionAfterError){let n=[];this.createHelp().visibleCommands(this).forEach(s=>{n.push(s.name()),s.alias()&&n.push(s.alias())}),i=Gd(e,n)}let r=`error: unknown command '${e}'${i}`;this._displayError(1,"commander.unknownCommand",r)}version(e,i,r){if(e===void 0)return this._version;this._version=e,i=i||"-V, --version",r=r||"output the version number";let n=this.createOption(i,r);return this._versionOptionName=n.attributeName(),this.options.push(n),this.on("option:"+n.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,i){return e===void 0&&i===void 0?this._description:(this._description=e,i&&(this._argsDescription=i),this)}alias(e){if(e===void 0)return this._aliases[0];let i=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(i=this.commands[this.commands.length-1]),e===i._name)throw new Error("Command alias can't be the same as its name");return i._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(i=>this.alias(i)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;let i=this._args.map(r=>Hx(r));return[].concat(this.options.length||this._hasHelpOption?"[options]":[],this.commands.length?"[command]":[],this._args.length?i:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}helpInformation(e){let i=this.createHelp();return i.helpWidth===void 0&&(i.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()),i.formatHelp(this,i)}_getHelpContext(e){e=e||{};let i={error:!!e.error},r;return i.error?r=n=>this._outputConfiguration.writeErr(n):r=n=>this._outputConfiguration.writeOut(n),i.write=e.write||r,i.command=this,i}outputHelp(e){let i;typeof e=="function"&&(i=e,e=void 0);let r=this._getHelpContext(e);Ll(this).reverse().forEach(s=>s.emit("beforeAllHelp",r)),this.emit("beforeHelp",r);let n=this.helpInformation(r);if(i&&(n=i(n),typeof n!="string"&&!Buffer.isBuffer(n)))throw new Error("outputHelp callback must return a string or a Buffer");r.write(n),this.emit(this._helpLongFlag),this.emit("afterHelp",r),Ll(this).forEach(s=>s.emit("afterAllHelp",r))}helpOption(e,i){if(typeof e=="boolean")return this._hasHelpOption=e,this;this._helpFlags=e||this._helpFlags,this._helpDescription=i||this._helpDescription;let r=Wx(this._helpFlags);return this._helpShortFlag=r.shortFlag,this._helpLongFlag=r.longFlag,this}help(e){this.outputHelp(e);let i=process.exitCode||0;i===0&&e&&typeof e!="function"&&e.error&&(i=1),this._exit(i,"commander.help","(outputHelp)")}addHelpText(e,i){let r=["beforeAll","before","after","afterAll"];if(!r.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${r.join("', '")}'`);let n=`${e}Help`;return this.on(n,s=>{let o;typeof i=="function"?o=i({error:s.error,command:s.command}):o=i,o&&s.write(`${o}
`)}),this}};function Yd(t,e){t._hasHelpOption&&e.find(r=>r===t._helpLongFlag||r===t._helpShortFlag)&&(t.outputHelp(),t._exit(0,"commander.helpDisplayed","(outputHelp)"))}function Wd(t){return t.map(e=>{if(!e.startsWith("--inspect"))return e;let i,r="127.0.0.1",n="9229",s;return(s=e.match(/^(--inspect(-brk)?)$/))!==null?i=s[1]:(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(i=s[1],/^\d+$/.test(s[3])?n=s[3]:r=s[3]):(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(i=s[1],r=s[3],n=s[4]),i&&n!=="0"?`${i}=${r}:${parseInt(n)+1}`:e})}function Ll(t){let e=[];for(let i=t;i;i=i.parent)e.push(i);return e}zd.Command=Bl});var Xd=w((qt,Qd)=>{var{Argument:zx}=qs(),{Command:Jd}=Kd(),{CommanderError:Kx,InvalidArgumentError:Zd}=un(),{Help:Jx}=kl(),{Option:Zx}=Tl();qt=Qd.exports=new Jd;qt.program=qt;qt.Argument=zx;qt.Command=Jd;qt.CommanderError=Kx;qt.Help=Jx;qt.InvalidArgumentError=Zd;qt.InvalidOptionArgumentError=Zd;qt.Option=Zx});var im=w((em,tm)=>{em=tm.exports=Tr;function Tr(t,e){if(this.stream=e.stream||process.stderr,typeof e=="number"){var i=e;e={},e.total=i}else{if(e=e||{},typeof t!="string")throw new Error("format required");if(typeof e.total!="number")throw new Error("total required")}this.fmt=t,this.curr=e.curr||0,this.total=e.total,this.width=e.width||this.total,this.clear=e.clear,this.chars={complete:e.complete||"=",incomplete:e.incomplete||"-",head:e.head||e.complete||"="},this.renderThrottle=e.renderThrottle!==0?e.renderThrottle||16:0,this.lastRender=-1/0,this.callback=e.callback||function(){},this.tokens={},this.lastDraw=""}Tr.prototype.tick=function(t,e){if(t!==0&&(t=t||1),typeof t=="object"&&(e=t,t=1),e&&(this.tokens=e),this.curr==0&&(this.start=new Date),this.curr+=t,this.render(),this.curr>=this.total){this.render(void 0,!0),this.complete=!0,this.terminate(),this.callback(this);return}};Tr.prototype.render=function(t,e){if(e=e!==void 0?e:!1,t&&(this.tokens=t),!!this.stream.isTTY){var i=Date.now(),r=i-this.lastRender;if(!(!e&&r<this.renderThrottle)){this.lastRender=i;var n=this.curr/this.total;n=Math.min(Math.max(n,0),1);var s=Math.floor(n*100),o,a,l,c=new Date-this.start,u=s==100?0:c*(this.total/this.curr-1),f=this.curr/(c/1e3),d=this.fmt.replace(":current",this.curr).replace(":total",this.total).replace(":elapsed",isNaN(c)?"0.0":(c/1e3).toFixed(1)).replace(":eta",isNaN(u)||!isFinite(u)?"0.0":(u/1e3).toFixed(1)).replace(":percent",s.toFixed(0)+"%").replace(":rate",Math.round(f)),g=Math.max(0,this.stream.columns-d.replace(":bar","").length);g&&process.platform==="win32"&&(g=g-1);var m=Math.min(this.width,g);if(l=Math.round(m*n),a=Array(Math.max(0,l+1)).join(this.chars.complete),o=Array(Math.max(0,m-l+1)).join(this.chars.incomplete),l>0&&(a=a.slice(0,-1)+this.chars.head),d=d.replace(":bar",a+o),this.tokens)for(var v in this.tokens)d=d.replace(":"+v,this.tokens[v]);this.lastDraw!==d&&(this.stream.cursorTo(0),this.stream.write(d),this.stream.clearLine(1),this.lastDraw=d)}}};Tr.prototype.update=function(t,e){var i=Math.floor(t*this.total),r=i-this.curr;this.tick(r,e)};Tr.prototype.interrupt=function(t){this.stream.clearLine(),this.stream.cursorTo(0),this.stream.write(t),this.stream.write(`
`),this.stream.write(this.lastDraw)};Tr.prototype.terminate=function(){this.clear?this.stream.clearLine&&(this.stream.clearLine(),this.stream.cursorTo(0)):this.stream.write(`
`)}});var nm=w((j2,rm)=>{rm.exports=im()});var lm=w(ri=>{"use strict";Object.defineProperty(ri,"__esModule",{value:!0});var sm=require("buffer"),Gi={INVALID_ENCODING:"Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.",INVALID_SMARTBUFFER_SIZE:"Invalid size provided. Size must be a valid integer greater than zero.",INVALID_SMARTBUFFER_BUFFER:"Invalid Buffer provided in SmartBufferOptions.",INVALID_SMARTBUFFER_OBJECT:"Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.",INVALID_OFFSET:"An invalid offset value was provided.",INVALID_OFFSET_NON_NUMBER:"An invalid offset value was provided. A numeric value is required.",INVALID_LENGTH:"An invalid length value was provided.",INVALID_LENGTH_NON_NUMBER:"An invalid length value was provived. A numeric value is required.",INVALID_TARGET_OFFSET:"Target offset is beyond the bounds of the internal SmartBuffer data.",INVALID_TARGET_LENGTH:"Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.",INVALID_READ_BEYOND_BOUNDS:"Attempted to read beyond the bounds of the managed data.",INVALID_WRITE_BEYOND_BOUNDS:"Attempted to write beyond the bounds of the managed data."};ri.ERRORS=Gi;function Qx(t){if(!sm.Buffer.isEncoding(t))throw new Error(Gi.INVALID_ENCODING)}ri.checkEncoding=Qx;function om(t){return typeof t=="number"&&isFinite(t)&&iS(t)}ri.isFiniteInteger=om;function am(t,e){if(typeof t=="number"){if(!om(t)||t<0)throw new Error(e?Gi.INVALID_OFFSET:Gi.INVALID_LENGTH)}else throw new Error(e?Gi.INVALID_OFFSET_NON_NUMBER:Gi.INVALID_LENGTH_NON_NUMBER)}function Xx(t){am(t,!1)}ri.checkLengthValue=Xx;function eS(t){am(t,!0)}ri.checkOffsetValue=eS;function tS(t,e){if(t<0||t>e.length)throw new Error(Gi.INVALID_TARGET_OFFSET)}ri.checkTargetOffset=tS;function iS(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t}function rS(t){if(typeof BigInt=="undefined")throw new Error("Platform does not support JS BigInt type.");if(typeof sm.Buffer.prototype[t]=="undefined")throw new Error(`Platform does not support Buffer.prototype.${t}.`)}ri.bigIntAndBufferInt64Check=rS});var um=w(Pl=>{"use strict";Object.defineProperty(Pl,"__esModule",{value:!0});var pe=lm(),cm=4096,nS="utf8",Rl=class t{constructor(e){if(this.length=0,this._encoding=nS,this._writeOffset=0,this._readOffset=0,t.isSmartBufferOptions(e))if(e.encoding&&(pe.checkEncoding(e.encoding),this._encoding=e.encoding),e.size)if(pe.isFiniteInteger(e.size)&&e.size>0)this._buff=Buffer.allocUnsafe(e.size);else throw new Error(pe.ERRORS.INVALID_SMARTBUFFER_SIZE);else if(e.buff)if(Buffer.isBuffer(e.buff))this._buff=e.buff,this.length=e.buff.length;else throw new Error(pe.ERRORS.INVALID_SMARTBUFFER_BUFFER);else this._buff=Buffer.allocUnsafe(cm);else{if(typeof e!="undefined")throw new Error(pe.ERRORS.INVALID_SMARTBUFFER_OBJECT);this._buff=Buffer.allocUnsafe(cm)}}static fromSize(e,i){return new this({size:e,encoding:i})}static fromBuffer(e,i){return new this({buff:e,encoding:i})}static fromOptions(e){return new this(e)}static isSmartBufferOptions(e){let i=e;return i&&(i.encoding!==void 0||i.size!==void 0||i.buff!==void 0)}readInt8(e){return this._readNumberValue(Buffer.prototype.readInt8,1,e)}readInt16BE(e){return this._readNumberValue(Buffer.prototype.readInt16BE,2,e)}readInt16LE(e){return this._readNumberValue(Buffer.prototype.readInt16LE,2,e)}readInt32BE(e){return this._readNumberValue(Buffer.prototype.readInt32BE,4,e)}readInt32LE(e){return this._readNumberValue(Buffer.prototype.readInt32LE,4,e)}readBigInt64BE(e){return pe.bigIntAndBufferInt64Check("readBigInt64BE"),this._readNumberValue(Buffer.prototype.readBigInt64BE,8,e)}readBigInt64LE(e){return pe.bigIntAndBufferInt64Check("readBigInt64LE"),this._readNumberValue(Buffer.prototype.readBigInt64LE,8,e)}writeInt8(e,i){return this._writeNumberValue(Buffer.prototype.writeInt8,1,e,i),this}insertInt8(e,i){return this._insertNumberValue(Buffer.prototype.writeInt8,1,e,i)}writeInt16BE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt16BE,2,e,i)}insertInt16BE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt16BE,2,e,i)}writeInt16LE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt16LE,2,e,i)}insertInt16LE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt16LE,2,e,i)}writeInt32BE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt32BE,4,e,i)}insertInt32BE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt32BE,4,e,i)}writeInt32LE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt32LE,4,e,i)}insertInt32LE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt32LE,4,e,i)}writeBigInt64BE(e,i){return pe.bigIntAndBufferInt64Check("writeBigInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigInt64BE,8,e,i)}insertBigInt64BE(e,i){return pe.bigIntAndBufferInt64Check("writeBigInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigInt64BE,8,e,i)}writeBigInt64LE(e,i){return pe.bigIntAndBufferInt64Check("writeBigInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigInt64LE,8,e,i)}insertBigInt64LE(e,i){return pe.bigIntAndBufferInt64Check("writeBigInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigInt64LE,8,e,i)}readUInt8(e){return this._readNumberValue(Buffer.prototype.readUInt8,1,e)}readUInt16BE(e){return this._readNumberValue(Buffer.prototype.readUInt16BE,2,e)}readUInt16LE(e){return this._readNumberValue(Buffer.prototype.readUInt16LE,2,e)}readUInt32BE(e){return this._readNumberValue(Buffer.prototype.readUInt32BE,4,e)}readUInt32LE(e){return this._readNumberValue(Buffer.prototype.readUInt32LE,4,e)}readBigUInt64BE(e){return pe.bigIntAndBufferInt64Check("readBigUInt64BE"),this._readNumberValue(Buffer.prototype.readBigUInt64BE,8,e)}readBigUInt64LE(e){return pe.bigIntAndBufferInt64Check("readBigUInt64LE"),this._readNumberValue(Buffer.prototype.readBigUInt64LE,8,e)}writeUInt8(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt8,1,e,i)}insertUInt8(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt8,1,e,i)}writeUInt16BE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt16BE,2,e,i)}insertUInt16BE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt16BE,2,e,i)}writeUInt16LE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt16LE,2,e,i)}insertUInt16LE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt16LE,2,e,i)}writeUInt32BE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt32BE,4,e,i)}insertUInt32BE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt32BE,4,e,i)}writeUInt32LE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt32LE,4,e,i)}insertUInt32LE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt32LE,4,e,i)}writeBigUInt64BE(e,i){return pe.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,i)}insertBigUInt64BE(e,i){return pe.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,i)}writeBigUInt64LE(e,i){return pe.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,i)}insertBigUInt64LE(e,i){return pe.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,i)}readFloatBE(e){return this._readNumberValue(Buffer.prototype.readFloatBE,4,e)}readFloatLE(e){return this._readNumberValue(Buffer.prototype.readFloatLE,4,e)}writeFloatBE(e,i){return this._writeNumberValue(Buffer.prototype.writeFloatBE,4,e,i)}insertFloatBE(e,i){return this._insertNumberValue(Buffer.prototype.writeFloatBE,4,e,i)}writeFloatLE(e,i){return this._writeNumberValue(Buffer.prototype.writeFloatLE,4,e,i)}insertFloatLE(e,i){return this._insertNumberValue(Buffer.prototype.writeFloatLE,4,e,i)}readDoubleBE(e){return this._readNumberValue(Buffer.prototype.readDoubleBE,8,e)}readDoubleLE(e){return this._readNumberValue(Buffer.prototype.readDoubleLE,8,e)}writeDoubleBE(e,i){return this._writeNumberValue(Buffer.prototype.writeDoubleBE,8,e,i)}insertDoubleBE(e,i){return this._insertNumberValue(Buffer.prototype.writeDoubleBE,8,e,i)}writeDoubleLE(e,i){return this._writeNumberValue(Buffer.prototype.writeDoubleLE,8,e,i)}insertDoubleLE(e,i){return this._insertNumberValue(Buffer.prototype.writeDoubleLE,8,e,i)}readString(e,i){let r;typeof e=="number"?(pe.checkLengthValue(e),r=Math.min(e,this.length-this._readOffset)):(i=e,r=this.length-this._readOffset),typeof i!="undefined"&&pe.checkEncoding(i);let n=this._buff.slice(this._readOffset,this._readOffset+r).toString(i||this._encoding);return this._readOffset+=r,n}insertString(e,i,r){return pe.checkOffsetValue(i),this._handleString(e,!0,i,r)}writeString(e,i,r){return this._handleString(e,!1,i,r)}readStringNT(e){typeof e!="undefined"&&pe.checkEncoding(e);let i=this.length;for(let n=this._readOffset;n<this.length;n++)if(this._buff[n]===0){i=n;break}let r=this._buff.slice(this._readOffset,i);return this._readOffset=i+1,r.toString(e||this._encoding)}insertStringNT(e,i,r){return pe.checkOffsetValue(i),this.insertString(e,i,r),this.insertUInt8(0,i+e.length),this}writeStringNT(e,i,r){return this.writeString(e,i,r),this.writeUInt8(0,typeof i=="number"?i+e.length:this.writeOffset),this}readBuffer(e){typeof e!="undefined"&&pe.checkLengthValue(e);let i=typeof e=="number"?e:this.length,r=Math.min(this.length,this._readOffset+i),n=this._buff.slice(this._readOffset,r);return this._readOffset=r,n}insertBuffer(e,i){return pe.checkOffsetValue(i),this._handleBuffer(e,!0,i)}writeBuffer(e,i){return this._handleBuffer(e,!1,i)}readBufferNT(){let e=this.length;for(let r=this._readOffset;r<this.length;r++)if(this._buff[r]===0){e=r;break}let i=this._buff.slice(this._readOffset,e);return this._readOffset=e+1,i}insertBufferNT(e,i){return pe.checkOffsetValue(i),this.insertBuffer(e,i),this.insertUInt8(0,i+e.length),this}writeBufferNT(e,i){return typeof i!="undefined"&&pe.checkOffsetValue(i),this.writeBuffer(e,i),this.writeUInt8(0,typeof i=="number"?i+e.length:this._writeOffset),this}clear(){return this._writeOffset=0,this._readOffset=0,this.length=0,this}remaining(){return this.length-this._readOffset}get readOffset(){return this._readOffset}set readOffset(e){pe.checkOffsetValue(e),pe.checkTargetOffset(e,this),this._readOffset=e}get writeOffset(){return this._writeOffset}set writeOffset(e){pe.checkOffsetValue(e),pe.checkTargetOffset(e,this),this._writeOffset=e}get encoding(){return this._encoding}set encoding(e){pe.checkEncoding(e),this._encoding=e}get internalBuffer(){return this._buff}toBuffer(){return this._buff.slice(0,this.length)}toString(e){let i=typeof e=="string"?e:this._encoding;return pe.checkEncoding(i),this._buff.toString(i,0,this.length)}destroy(){return this.clear(),this}_handleString(e,i,r,n){let s=this._writeOffset,o=this._encoding;typeof r=="number"?s=r:typeof r=="string"&&(pe.checkEncoding(r),o=r),typeof n=="string"&&(pe.checkEncoding(n),o=n);let a=Buffer.byteLength(e,o);return i?this.ensureInsertable(a,s):this._ensureWriteable(a,s),this._buff.write(e,s,a,o),i?this._writeOffset+=a:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,s+a):this._writeOffset+=a,this}_handleBuffer(e,i,r){let n=typeof r=="number"?r:this._writeOffset;return i?this.ensureInsertable(e.length,n):this._ensureWriteable(e.length,n),e.copy(this._buff,n),i?this._writeOffset+=e.length:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,n+e.length):this._writeOffset+=e.length,this}ensureReadable(e,i){let r=this._readOffset;if(typeof i!="undefined"&&(pe.checkOffsetValue(i),r=i),r<0||r+e>this.length)throw new Error(pe.ERRORS.INVALID_READ_BEYOND_BOUNDS)}ensureInsertable(e,i){pe.checkOffsetValue(i),this._ensureCapacity(this.length+e),i<this.length&&this._buff.copy(this._buff,i+e,i,this._buff.length),i+e>this.length?this.length=i+e:this.length+=e}_ensureWriteable(e,i){let r=typeof i=="number"?i:this._writeOffset;this._ensureCapacity(r+e),r+e>this.length&&(this.length=r+e)}_ensureCapacity(e){let i=this._buff.length;if(e>i){let r=this._buff,n=i*3/2+1;n<e&&(n=e),this._buff=Buffer.allocUnsafe(n),r.copy(this._buff,0,0,i)}}_readNumberValue(e,i,r){this.ensureReadable(i,r);let n=e.call(this._buff,typeof r=="number"?r:this._readOffset);return typeof r=="undefined"&&(this._readOffset+=i),n}_insertNumberValue(e,i,r,n){return pe.checkOffsetValue(n),this.ensureInsertable(i,n),e.call(this._buff,r,n),this._writeOffset+=i,this}_writeNumberValue(e,i,r,n){if(typeof n=="number"){if(n<0)throw new Error(pe.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);pe.checkOffsetValue(n)}let s=typeof n=="number"?n:this._writeOffset;return this._ensureWriteable(i,s),e.call(this._buff,r,s),typeof n=="number"?this._writeOffset=Math.max(this._writeOffset,s+i):this._writeOffset+=i,this}};Pl.SmartBuffer=Rl});var Ml=w(Ne=>{"use strict";Object.defineProperty(Ne,"__esModule",{value:!0});Ne.SOCKS5_NO_ACCEPTABLE_AUTH=Ne.SOCKS5_CUSTOM_AUTH_END=Ne.SOCKS5_CUSTOM_AUTH_START=Ne.SOCKS_INCOMING_PACKET_SIZES=Ne.SocksClientState=Ne.Socks5Response=Ne.Socks5HostType=Ne.Socks5Auth=Ne.Socks4Response=Ne.SocksCommand=Ne.ERRORS=Ne.DEFAULT_TIMEOUT=void 0;var sS=3e4;Ne.DEFAULT_TIMEOUT=sS;var oS={InvalidSocksCommand:"An invalid SOCKS command was provided. Valid options are connect, bind, and associate.",InvalidSocksCommandForOperation:"An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.",InvalidSocksCommandChain:"An invalid SOCKS command was provided. Chaining currently only supports the connect command.",InvalidSocksClientOptionsDestination:"An invalid destination host was provided.",InvalidSocksClientOptionsExistingSocket:"An invalid existing socket was provided. This should be an instance of stream.Duplex.",InvalidSocksClientOptionsProxy:"Invalid SOCKS proxy details were provided.",InvalidSocksClientOptionsTimeout:"An invalid timeout value was provided. Please enter a value above 0 (in ms).",InvalidSocksClientOptionsProxiesLength:"At least two socks proxies must be provided for chaining.",InvalidSocksClientOptionsCustomAuthRange:"Custom auth must be a value between 0x80 and 0xFE.",InvalidSocksClientOptionsCustomAuthOptions:"When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.",NegotiationError:"Negotiation error",SocketClosed:"Socket closed",ProxyConnectionTimedOut:"Proxy connection timed out",InternalError:"SocksClient internal error (this should not happen)",InvalidSocks4HandshakeResponse:"Received invalid Socks4 handshake response",Socks4ProxyRejectedConnection:"Socks4 Proxy rejected connection",InvalidSocks4IncomingConnectionResponse:"Socks4 invalid incoming connection response",Socks4ProxyRejectedIncomingBoundConnection:"Socks4 Proxy rejected incoming bound connection",InvalidSocks5InitialHandshakeResponse:"Received invalid Socks5 initial handshake response",InvalidSocks5IntiailHandshakeSocksVersion:"Received invalid Socks5 initial handshake (invalid socks version)",InvalidSocks5InitialHandshakeNoAcceptedAuthType:"Received invalid Socks5 initial handshake (no accepted authentication type)",InvalidSocks5InitialHandshakeUnknownAuthType:"Received invalid Socks5 initial handshake (unknown authentication type)",Socks5AuthenticationFailed:"Socks5 Authentication failed",InvalidSocks5FinalHandshake:"Received invalid Socks5 final handshake response",InvalidSocks5FinalHandshakeRejected:"Socks5 proxy rejected connection",InvalidSocks5IncomingConnectionResponse:"Received invalid Socks5 incoming connection response",Socks5ProxyRejectedIncomingBoundConnection:"Socks5 Proxy rejected incoming bound connection"};Ne.ERRORS=oS;var aS={Socks5InitialHandshakeResponse:2,Socks5UserPassAuthenticationResponse:2,Socks5ResponseHeader:5,Socks5ResponseIPv4:10,Socks5ResponseIPv6:22,Socks5ResponseHostname:t=>t+7,Socks4Response:8};Ne.SOCKS_INCOMING_PACKET_SIZES=aS;var fm;(function(t){t[t.connect=1]="connect",t[t.bind=2]="bind",t[t.associate=3]="associate"})(fm||(Ne.SocksCommand=fm={}));var hm;(function(t){t[t.Granted=90]="Granted",t[t.Failed=91]="Failed",t[t.Rejected=92]="Rejected",t[t.RejectedIdent=93]="RejectedIdent"})(hm||(Ne.Socks4Response=hm={}));var pm;(function(t){t[t.NoAuth=0]="NoAuth",t[t.GSSApi=1]="GSSApi",t[t.UserPass=2]="UserPass"})(pm||(Ne.Socks5Auth=pm={}));var lS=128;Ne.SOCKS5_CUSTOM_AUTH_START=lS;var cS=254;Ne.SOCKS5_CUSTOM_AUTH_END=cS;var uS=255;Ne.SOCKS5_NO_ACCEPTABLE_AUTH=uS;var dm;(function(t){t[t.Granted=0]="Granted",t[t.Failure=1]="Failure",t[t.NotAllowed=2]="NotAllowed",t[t.NetworkUnreachable=3]="NetworkUnreachable",t[t.HostUnreachable=4]="HostUnreachable",t[t.ConnectionRefused=5]="ConnectionRefused",t[t.TTLExpired=6]="TTLExpired",t[t.CommandNotSupported=7]="CommandNotSupported",t[t.AddressNotSupported=8]="AddressNotSupported"})(dm||(Ne.Socks5Response=dm={}));var mm;(function(t){t[t.IPv4=1]="IPv4",t[t.Hostname=3]="Hostname",t[t.IPv6=4]="IPv6"})(mm||(Ne.Socks5HostType=mm={}));var gm;(function(t){t[t.Created=0]="Created",t[t.Connecting=1]="Connecting",t[t.Connected=2]="Connected",t[t.SentInitialHandshake=3]="SentInitialHandshake",t[t.ReceivedInitialHandshakeResponse=4]="ReceivedInitialHandshakeResponse",t[t.SentAuthentication=5]="SentAuthentication",t[t.ReceivedAuthenticationResponse=6]="ReceivedAuthenticationResponse",t[t.SentFinalHandshake=7]="SentFinalHandshake",t[t.ReceivedFinalResponse=8]="ReceivedFinalResponse",t[t.BoundWaitingForConnection=9]="BoundWaitingForConnection",t[t.Established=10]="Established",t[t.Disconnected=11]="Disconnected",t[t.Error=99]="Error"})(gm||(Ne.SocksClientState=gm={}))});var ql=w(Ar=>{"use strict";Object.defineProperty(Ar,"__esModule",{value:!0});Ar.shuffleArray=Ar.SocksClientError=void 0;var Fl=class extends Error{constructor(e,i){super(e),this.options=i}};Ar.SocksClientError=Fl;function fS(t){for(let e=t.length-1;e>0;e--){let i=Math.floor(Math.random()*(e+1));[t[e],t[i]]=[t[i],t[e]]}}Ar.shuffleArray=fS});var Dl=w(Ir=>{"use strict";Object.defineProperty(Ir,"__esModule",{value:!0});Ir.isCorrect=Ir.isInSubnet=void 0;function hS(t){return this.subnetMask<t.subnetMask?!1:this.mask(t.subnetMask)===t.mask()}Ir.isInSubnet=hS;function pS(t){return function(){return this.addressMinusSuffix!==this.correctForm()?!1:this.subnetMask===t&&!this.parsedSubnet?!0:this.parsedSubnet===String(this.subnetMask)}}Ir.isCorrect=pS});var jl=w(Ht=>{"use strict";Object.defineProperty(Ht,"__esModule",{value:!0});Ht.RE_SUBNET_STRING=Ht.RE_ADDRESS=Ht.GROUPS=Ht.BITS=void 0;Ht.BITS=32;Ht.GROUPS=4;Ht.RE_ADDRESS=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;Ht.RE_SUBNET_STRING=/\/\d{1,2}$/});var js=w(Ds=>{"use strict";Object.defineProperty(Ds,"__esModule",{value:!0});Ds.AddressError=void 0;var Ul=class extends Error{constructor(e,i){super(e),this.name="AddressError",i!==null&&(this.parseMessage=i)}};Ds.AddressError=Ul});var $l=w((Us,vm)=>{(function(){var t,e=0xdeadbeefcafe,i=(e&16777215)==15715070;function r(h,p,y){h!=null&&(typeof h=="number"?this.fromNumber(h,p,y):p==null&&typeof h!="string"?this.fromString(h,256):this.fromString(h,p))}function n(){return new r(null)}function s(h,p,y,x,B,M){for(;--M>=0;){var G=p*this[h++]+y[x]+B;B=Math.floor(G/67108864),y[x++]=G&67108863}return B}function o(h,p,y,x,B,M){for(var G=p&32767,z=p>>15;--M>=0;){var Re=this[h]&32767,Ye=this[h++]>>15,kt=z*Re+Ye*G;Re=G*Re+((kt&32767)<<15)+y[x]+(B&1073741823),B=(Re>>>30)+(kt>>>15)+z*Ye+(B>>>30),y[x++]=Re&1073741823}return B}function a(h,p,y,x,B,M){for(var G=p&16383,z=p>>14;--M>=0;){var Re=this[h]&16383,Ye=this[h++]>>14,kt=z*Re+Ye*G;Re=G*Re+((kt&16383)<<14)+y[x]+B,B=(Re>>28)+(kt>>14)+z*Ye,y[x++]=Re&268435455}return B}var l=typeof navigator!="undefined";l&&i&&navigator.appName=="Microsoft Internet Explorer"?(r.prototype.am=o,t=30):l&&i&&navigator.appName!="Netscape"?(r.prototype.am=s,t=26):(r.prototype.am=a,t=28),r.prototype.DB=t,r.prototype.DM=(1<<t)-1,r.prototype.DV=1<<t;var c=52;r.prototype.FV=Math.pow(2,c),r.prototype.F1=c-t,r.prototype.F2=2*t-c;var u="0123456789abcdefghijklmnopqrstuvwxyz",f=new Array,d,g;for(d="0".charCodeAt(0),g=0;g<=9;++g)f[d++]=g;for(d="a".charCodeAt(0),g=10;g<36;++g)f[d++]=g;for(d="A".charCodeAt(0),g=10;g<36;++g)f[d++]=g;function m(h){return u.charAt(h)}function v(h,p){var y=f[h.charCodeAt(p)];return y==null?-1:y}function b(h){for(var p=this.t-1;p>=0;--p)h[p]=this[p];h.t=this.t,h.s=this.s}function _(h){this.t=1,this.s=h<0?-1:0,h>0?this[0]=h:h<-1?this[0]=h+this.DV:this.t=0}function S(h){var p=n();return p.fromInt(h),p}function O(h,p){var y;if(p==16)y=4;else if(p==8)y=3;else if(p==256)y=8;else if(p==2)y=1;else if(p==32)y=5;else if(p==4)y=2;else{this.fromRadix(h,p);return}this.t=0,this.s=0;for(var x=h.length,B=!1,M=0;--x>=0;){var G=y==8?h[x]&255:v(h,x);if(G<0){h.charAt(x)=="-"&&(B=!0);continue}B=!1,M==0?this[this.t++]=G:M+y>this.DB?(this[this.t-1]|=(G&(1<<this.DB-M)-1)<<M,this[this.t++]=G>>this.DB-M):this[this.t-1]|=G<<M,M+=y,M>=this.DB&&(M-=this.DB)}y==8&&h[0]&128&&(this.s=-1,M>0&&(this[this.t-1]|=(1<<this.DB-M)-1<<M)),this.clamp(),B&&r.ZERO.subTo(this,this)}function k(){for(var h=this.s&this.DM;this.t>0&&this[this.t-1]==h;)--this.t}function E(h){if(this.s<0)return"-"+this.negate().toString(h);var p;if(h==16)p=4;else if(h==8)p=3;else if(h==2)p=1;else if(h==32)p=5;else if(h==4)p=2;else return this.toRadix(h);var y=(1<<p)-1,x,B=!1,M="",G=this.t,z=this.DB-G*this.DB%p;if(G-- >0)for(z<this.DB&&(x=this[G]>>z)>0&&(B=!0,M=m(x));G>=0;)z<p?(x=(this[G]&(1<<z)-1)<<p-z,x|=this[--G]>>(z+=this.DB-p)):(x=this[G]>>(z-=p)&y,z<=0&&(z+=this.DB,--G)),x>0&&(B=!0),B&&(M+=m(x));return B?M:"0"}function R(){var h=n();return r.ZERO.subTo(this,h),h}function T(){return this.s<0?this.negate():this}function A(h){var p=this.s-h.s;if(p!=0)return p;var y=this.t;if(p=y-h.t,p!=0)return this.s<0?-p:p;for(;--y>=0;)if((p=this[y]-h[y])!=0)return p;return 0}function C(h){var p=1,y;return(y=h>>>16)!=0&&(h=y,p+=16),(y=h>>8)!=0&&(h=y,p+=8),(y=h>>4)!=0&&(h=y,p+=4),(y=h>>2)!=0&&(h=y,p+=2),(y=h>>1)!=0&&(h=y,p+=1),p}function L(){return this.t<=0?0:this.DB*(this.t-1)+C(this[this.t-1]^this.s&this.DM)}function P(h,p){var y;for(y=this.t-1;y>=0;--y)p[y+h]=this[y];for(y=h-1;y>=0;--y)p[y]=0;p.t=this.t+h,p.s=this.s}function U(h,p){for(var y=h;y<this.t;++y)p[y-h]=this[y];p.t=Math.max(this.t-h,0),p.s=this.s}function q(h,p){var y=h%this.DB,x=this.DB-y,B=(1<<x)-1,M=Math.floor(h/this.DB),G=this.s<<y&this.DM,z;for(z=this.t-1;z>=0;--z)p[z+M+1]=this[z]>>x|G,G=(this[z]&B)<<y;for(z=M-1;z>=0;--z)p[z]=0;p[M]=G,p.t=this.t+M+1,p.s=this.s,p.clamp()}function H(h,p){p.s=this.s;var y=Math.floor(h/this.DB);if(y>=this.t){p.t=0;return}var x=h%this.DB,B=this.DB-x,M=(1<<x)-1;p[0]=this[y]>>x;for(var G=y+1;G<this.t;++G)p[G-y-1]|=(this[G]&M)<<B,p[G-y]=this[G]>>x;x>0&&(p[this.t-y-1]|=(this.s&M)<<B),p.t=this.t-y,p.clamp()}function j(h,p){for(var y=0,x=0,B=Math.min(h.t,this.t);y<B;)x+=this[y]-h[y],p[y++]=x&this.DM,x>>=this.DB;if(h.t<this.t){for(x-=h.s;y<this.t;)x+=this[y],p[y++]=x&this.DM,x>>=this.DB;x+=this.s}else{for(x+=this.s;y<h.t;)x-=h[y],p[y++]=x&this.DM,x>>=this.DB;x-=h.s}p.s=x<0?-1:0,x<-1?p[y++]=this.DV+x:x>0&&(p[y++]=x),p.t=y,p.clamp()}function V(h,p){var y=this.abs(),x=h.abs(),B=y.t;for(p.t=B+x.t;--B>=0;)p[B]=0;for(B=0;B<x.t;++B)p[B+y.t]=y.am(0,x[B],p,B,0,y.t);p.s=0,p.clamp(),this.s!=h.s&&r.ZERO.subTo(p,p)}function W(h){for(var p=this.abs(),y=h.t=2*p.t;--y>=0;)h[y]=0;for(y=0;y<p.t-1;++y){var x=p.am(y,p[y],h,2*y,0,1);(h[y+p.t]+=p.am(y+1,2*p[y],h,2*y+1,x,p.t-y-1))>=p.DV&&(h[y+p.t]-=p.DV,h[y+p.t+1]=1)}h.t>0&&(h[h.t-1]+=p.am(y,p[y],h,2*y,0,1)),h.s=0,h.clamp()}function Q(h,p,y){var x=h.abs();if(!(x.t<=0)){var B=this.abs();if(B.t<x.t){p!=null&&p.fromInt(0),y!=null&&this.copyTo(y);return}y==null&&(y=n());var M=n(),G=this.s,z=h.s,Re=this.DB-C(x[x.t-1]);Re>0?(x.lShiftTo(Re,M),B.lShiftTo(Re,y)):(x.copyTo(M),B.copyTo(y));var Ye=M.t,kt=M[Ye-1];if(kt!=0){var bt=kt*(1<<this.F1)+(Ye>1?M[Ye-2]>>this.F2:0),ei=this.FV/bt,ps=(1<<this.F1)/bt,Bt=1<<this.F2,Rt=y.t,ds=Rt-Ye,hi=p==null?n():p;for(M.dlShiftTo(ds,hi),y.compareTo(hi)>=0&&(y[y.t++]=1,y.subTo(hi,y)),r.ONE.dlShiftTo(Ye,hi),hi.subTo(M,M);M.t<Ye;)M[M.t++]=0;for(;--ds>=0;){var _a=y[--Rt]==kt?this.DM:Math.floor(y[Rt]*ei+(y[Rt-1]+Bt)*ps);if((y[Rt]+=M.am(0,_a,y,ds,0,Ye))<_a)for(M.dlShiftTo(ds,hi),y.subTo(hi,y);y[Rt]<--_a;)y.subTo(hi,y)}p!=null&&(y.drShiftTo(Ye,p),G!=z&&r.ZERO.subTo(p,p)),y.t=Ye,y.clamp(),Re>0&&y.rShiftTo(Re,y),G<0&&r.ZERO.subTo(y,y)}}}function Y(h){var p=n();return this.abs().divRemTo(h,null,p),this.s<0&&p.compareTo(r.ZERO)>0&&h.subTo(p,p),p}function de(h){this.m=h}function ae(h){return h.s<0||h.compareTo(this.m)>=0?h.mod(this.m):h}function ne(h){return h}function ue(h){h.divRemTo(this.m,null,h)}function N(h,p,y){h.multiplyTo(p,y),this.reduce(y)}function X(h,p){h.squareTo(p),this.reduce(p)}de.prototype.convert=ae,de.prototype.revert=ne,de.prototype.reduce=ue,de.prototype.mulTo=N,de.prototype.sqrTo=X;function ke(){if(this.t<1)return 0;var h=this[0];if(!(h&1))return 0;var p=h&3;return p=p*(2-(h&15)*p)&15,p=p*(2-(h&255)*p)&255,p=p*(2-((h&65535)*p&65535))&65535,p=p*(2-h*p%this.DV)%this.DV,p>0?this.DV-p:-p}function be(h){this.m=h,this.mp=h.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<h.DB-15)-1,this.mt2=2*h.t}function ge(h){var p=n();return h.abs().dlShiftTo(this.m.t,p),p.divRemTo(this.m,null,p),h.s<0&&p.compareTo(r.ZERO)>0&&this.m.subTo(p,p),p}function ve(h){var p=n();return h.copyTo(p),this.reduce(p),p}function fe(h){for(;h.t<=this.mt2;)h[h.t++]=0;for(var p=0;p<this.m.t;++p){var y=h[p]&32767,x=y*this.mpl+((y*this.mph+(h[p]>>15)*this.mpl&this.um)<<15)&h.DM;for(y=p+this.m.t,h[y]+=this.m.am(0,x,h,p,0,this.m.t);h[y]>=h.DV;)h[y]-=h.DV,h[++y]++}h.clamp(),h.drShiftTo(this.m.t,h),h.compareTo(this.m)>=0&&h.subTo(this.m,h)}function K(h,p){h.squareTo(p),this.reduce(p)}function $(h,p,y){h.multiplyTo(p,y),this.reduce(y)}be.prototype.convert=ge,be.prototype.revert=ve,be.prototype.reduce=fe,be.prototype.mulTo=$,be.prototype.sqrTo=K;function Ce(){return(this.t>0?this[0]&1:this.s)==0}function re(h,p){if(h>4294967295||h<1)return r.ONE;var y=n(),x=n(),B=p.convert(this),M=C(h)-1;for(B.copyTo(y);--M>=0;)if(p.sqrTo(y,x),(h&1<<M)>0)p.mulTo(x,B,y);else{var G=y;y=x,x=G}return p.revert(y)}function he(h,p){var y;return h<256||p.isEven()?y=new de(p):y=new be(p),this.exp(h,y)}r.prototype.copyTo=b,r.prototype.fromInt=_,r.prototype.fromString=O,r.prototype.clamp=k,r.prototype.dlShiftTo=P,r.prototype.drShiftTo=U,r.prototype.lShiftTo=q,r.prototype.rShiftTo=H,r.prototype.subTo=j,r.prototype.multiplyTo=V,r.prototype.squareTo=W,r.prototype.divRemTo=Q,r.prototype.invDigit=ke,r.prototype.isEven=Ce,r.prototype.exp=re,r.prototype.toString=E,r.prototype.negate=R,r.prototype.abs=T,r.prototype.compareTo=A,r.prototype.bitLength=L,r.prototype.mod=Y,r.prototype.modPowInt=he,r.ZERO=S(0),r.ONE=S(1);function ft(){var h=n();return this.copyTo(h),h}function vt(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function I(){return this.t==0?this.s:this[0]<<24>>24}function Z(){return this.t==0?this.s:this[0]<<16>>16}function te(h){return Math.floor(Math.LN2*this.DB/Math.log(h))}function ee(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function le(h){if(h==null&&(h=10),this.signum()==0||h<2||h>36)return"0";var p=this.chunkSize(h),y=Math.pow(h,p),x=S(y),B=n(),M=n(),G="";for(this.divRemTo(x,B,M);B.signum()>0;)G=(y+M.intValue()).toString(h).substr(1)+G,B.divRemTo(x,B,M);return M.intValue().toString(h)+G}function ce(h,p){this.fromInt(0),p==null&&(p=10);for(var y=this.chunkSize(p),x=Math.pow(p,y),B=!1,M=0,G=0,z=0;z<h.length;++z){var Re=v(h,z);if(Re<0){h.charAt(z)=="-"&&this.signum()==0&&(B=!0);continue}G=p*G+Re,++M>=y&&(this.dMultiply(x),this.dAddOffset(G,0),M=0,G=0)}M>0&&(this.dMultiply(Math.pow(p,M)),this.dAddOffset(G,0)),B&&r.ZERO.subTo(this,this)}function _e(h,p,y){if(typeof p=="number")if(h<2)this.fromInt(1);else for(this.fromNumber(h,y),this.testBit(h-1)||this.bitwiseTo(r.ONE.shiftLeft(h-1),oe,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(p);)this.dAddOffset(2,0),this.bitLength()>h&&this.subTo(r.ONE.shiftLeft(h-1),this);else{var x=new Array,B=h&7;x.length=(h>>3)+1,p.nextBytes(x),B>0?x[0]&=(1<<B)-1:x[0]=0,this.fromString(x,256)}}function we(){var h=this.t,p=new Array;p[0]=this.s;var y=this.DB-h*this.DB%8,x,B=0;if(h-- >0)for(y<this.DB&&(x=this[h]>>y)!=(this.s&this.DM)>>y&&(p[B++]=x|this.s<<this.DB-y);h>=0;)y<8?(x=(this[h]&(1<<y)-1)<<8-y,x|=this[--h]>>(y+=this.DB-8)):(x=this[h]>>(y-=8)&255,y<=0&&(y+=this.DB,--h)),x&128&&(x|=-256),B==0&&(this.s&128)!=(x&128)&&++B,(B>0||x!=this.s)&&(p[B++]=x);return p}function Be(h){return this.compareTo(h)==0}function Te(h){return this.compareTo(h)<0?this:h}function D(h){return this.compareTo(h)>0?this:h}function J(h,p,y){var x,B,M=Math.min(h.t,this.t);for(x=0;x<M;++x)y[x]=p(this[x],h[x]);if(h.t<this.t){for(B=h.s&this.DM,x=M;x<this.t;++x)y[x]=p(this[x],B);y.t=this.t}else{for(B=this.s&this.DM,x=M;x<h.t;++x)y[x]=p(B,h[x]);y.t=h.t}y.s=p(this.s,h.s),y.clamp()}function se(h,p){return h&p}function Ie(h){var p=n();return this.bitwiseTo(h,se,p),p}function oe(h,p){return h|p}function me(h){var p=n();return this.bitwiseTo(h,oe,p),p}function Ee(h,p){return h^p}function ie(h){var p=n();return this.bitwiseTo(h,Ee,p),p}function xe(h,p){return h&~p}function Ue(h){var p=n();return this.bitwiseTo(h,xe,p),p}function Ae(){for(var h=n(),p=0;p<this.t;++p)h[p]=this.DM&~this[p];return h.t=this.t,h.s=~this.s,h}function ht(h){var p=n();return h<0?this.rShiftTo(-h,p):this.lShiftTo(h,p),p}function Et(h){var p=n();return h<0?this.lShiftTo(-h,p):this.rShiftTo(h,p),p}function Qt(h){if(h==0)return-1;var p=0;return h&65535||(h>>=16,p+=16),h&255||(h>>=8,p+=8),h&15||(h>>=4,p+=4),h&3||(h>>=2,p+=2),h&1||++p,p}function ui(){for(var h=0;h<this.t;++h)if(this[h]!=0)return h*this.DB+Qt(this[h]);return this.s<0?this.t*this.DB:-1}function fi(h){for(var p=0;h!=0;)h&=h-1,++p;return p}function Ni(){for(var h=0,p=this.s&this.DM,y=0;y<this.t;++y)h+=fi(this[y]^p);return h}function Li(h){var p=Math.floor(h/this.DB);return p>=this.t?this.s!=0:(this[p]&1<<h%this.DB)!=0}function fr(h,p){var y=r.ONE.shiftLeft(h);return this.bitwiseTo(y,p,y),y}function Bi(h){return this.changeBit(h,oe)}function Ri(h){return this.changeBit(h,xe)}function Pi(h){return this.changeBit(h,Ee)}function Mi(h,p){for(var y=0,x=0,B=Math.min(h.t,this.t);y<B;)x+=this[y]+h[y],p[y++]=x&this.DM,x>>=this.DB;if(h.t<this.t){for(x+=h.s;y<this.t;)x+=this[y],p[y++]=x&this.DM,x>>=this.DB;x+=this.s}else{for(x+=this.s;y<h.t;)x+=h[y],p[y++]=x&this.DM,x>>=this.DB;x+=h.s}p.s=x<0?-1:0,x>0?p[y++]=x:x<-1&&(p[y++]=this.DV+x),p.t=y,p.clamp()}function es(h){var p=n();return this.addTo(h,p),p}function Xr(h){var p=n();return this.subTo(h,p),p}function ts(h){var p=n();return this.multiplyTo(h,p),p}function is(){var h=n();return this.squareTo(h),h}function rs(h){var p=n();return this.divRemTo(h,p,null),p}function ns(h){var p=n();return this.divRemTo(h,null,p),p}function ss(h){var p=n(),y=n();return this.divRemTo(h,p,y),new Array(p,y)}function ga(h){this[this.t]=this.am(0,h-1,this,0,0,this.t),++this.t,this.clamp()}function Fi(h,p){if(h!=0){for(;this.t<=p;)this[this.t++]=0;for(this[p]+=h;this[p]>=this.DV;)this[p]-=this.DV,++p>=this.t&&(this[this.t++]=0),++this[p]}}function Xt(){}function qi(h){return h}function hr(h,p,y){h.multiplyTo(p,y)}function os(h,p){h.squareTo(p)}Xt.prototype.convert=qi,Xt.prototype.revert=qi,Xt.prototype.mulTo=hr,Xt.prototype.sqrTo=os;function as(h){return this.exp(h,new Xt)}function ls(h,p,y){var x=Math.min(this.t+h.t,p);for(y.s=0,y.t=x;x>0;)y[--x]=0;var B;for(B=y.t-this.t;x<B;++x)y[x+this.t]=this.am(0,h[x],y,x,0,this.t);for(B=Math.min(h.t,p);x<B;++x)this.am(0,h[x],y,x,0,p-x);y.clamp()}function cs(h,p,y){--p;var x=y.t=this.t+h.t-p;for(y.s=0;--x>=0;)y[x]=0;for(x=Math.max(p-this.t,0);x<h.t;++x)y[this.t+x-p]=this.am(p-x,h[x],y,0,0,this.t+x-p);y.clamp(),y.drShiftTo(1,y)}function $t(h){this.r2=n(),this.q3=n(),r.ONE.dlShiftTo(2*h.t,this.r2),this.mu=this.r2.divide(h),this.m=h}function us(h){if(h.s<0||h.t>2*this.m.t)return h.mod(this.m);if(h.compareTo(this.m)<0)return h;var p=n();return h.copyTo(p),this.reduce(p),p}function fs(h){return h}function pr(h){for(h.drShiftTo(this.m.t-1,this.r2),h.t>this.m.t+1&&(h.t=this.m.t+1,h.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);h.compareTo(this.r2)<0;)h.dAddOffset(1,this.m.t+1);for(h.subTo(this.r2,h);h.compareTo(this.m)>=0;)h.subTo(this.m,h)}function Ab(h,p){h.squareTo(p),this.reduce(p)}function Ib(h,p,y){h.multiplyTo(p,y),this.reduce(y)}$t.prototype.convert=us,$t.prototype.revert=fs,$t.prototype.reduce=pr,$t.prototype.mulTo=Ib,$t.prototype.sqrTo=Ab;function Nb(h,p){var y=h.bitLength(),x,B=S(1),M;if(y<=0)return B;y<18?x=1:y<48?x=3:y<144?x=4:y<768?x=5:x=6,y<8?M=new de(p):p.isEven()?M=new $t(p):M=new be(p);var G=new Array,z=3,Re=x-1,Ye=(1<<x)-1;if(G[1]=M.convert(this),x>1){var kt=n();for(M.sqrTo(G[1],kt);z<=Ye;)G[z]=n(),M.mulTo(kt,G[z-2],G[z]),z+=2}var bt=h.t-1,ei,ps=!0,Bt=n(),Rt;for(y=C(h[bt])-1;bt>=0;){for(y>=Re?ei=h[bt]>>y-Re&Ye:(ei=(h[bt]&(1<<y+1)-1)<<Re-y,bt>0&&(ei|=h[bt-1]>>this.DB+y-Re)),z=x;!(ei&1);)ei>>=1,--z;if((y-=z)<0&&(y+=this.DB,--bt),ps)G[ei].copyTo(B),ps=!1;else{for(;z>1;)M.sqrTo(B,Bt),M.sqrTo(Bt,B),z-=2;z>0?M.sqrTo(B,Bt):(Rt=B,B=Bt,Bt=Rt),M.mulTo(Bt,G[ei],B)}for(;bt>=0&&!(h[bt]&1<<y);)M.sqrTo(B,Bt),Rt=B,B=Bt,Bt=Rt,--y<0&&(y=this.DB-1,--bt)}return M.revert(B)}function Lb(h){var p=this.s<0?this.negate():this.clone(),y=h.s<0?h.negate():h.clone();if(p.compareTo(y)<0){var x=p;p=y,y=x}var B=p.getLowestSetBit(),M=y.getLowestSetBit();if(M<0)return p;for(B<M&&(M=B),M>0&&(p.rShiftTo(M,p),y.rShiftTo(M,y));p.signum()>0;)(B=p.getLowestSetBit())>0&&p.rShiftTo(B,p),(B=y.getLowestSetBit())>0&&y.rShiftTo(B,y),p.compareTo(y)>=0?(p.subTo(y,p),p.rShiftTo(1,p)):(y.subTo(p,y),y.rShiftTo(1,y));return M>0&&y.lShiftTo(M,y),y}function Bb(h){if(h<=0)return 0;var p=this.DV%h,y=this.s<0?h-1:0;if(this.t>0)if(p==0)y=this[0]%h;else for(var x=this.t-1;x>=0;--x)y=(p*y+this[x])%h;return y}function Rb(h){var p=h.isEven();if(this.isEven()&&p||h.signum()==0)return r.ZERO;for(var y=h.clone(),x=this.clone(),B=S(1),M=S(0),G=S(0),z=S(1);y.signum()!=0;){for(;y.isEven();)y.rShiftTo(1,y),p?((!B.isEven()||!M.isEven())&&(B.addTo(this,B),M.subTo(h,M)),B.rShiftTo(1,B)):M.isEven()||M.subTo(h,M),M.rShiftTo(1,M);for(;x.isEven();)x.rShiftTo(1,x),p?((!G.isEven()||!z.isEven())&&(G.addTo(this,G),z.subTo(h,z)),G.rShiftTo(1,G)):z.isEven()||z.subTo(h,z),z.rShiftTo(1,z);y.compareTo(x)>=0?(y.subTo(x,y),p&&B.subTo(G,B),M.subTo(z,M)):(x.subTo(y,x),p&&G.subTo(B,G),z.subTo(M,z))}if(x.compareTo(r.ONE)!=0)return r.ZERO;if(z.compareTo(h)>=0)return z.subtract(h);if(z.signum()<0)z.addTo(h,z);else return z;return z.signum()<0?z.add(h):z}var st=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Pb=(1<<26)/st[st.length-1];function Mb(h){var p,y=this.abs();if(y.t==1&&y[0]<=st[st.length-1]){for(p=0;p<st.length;++p)if(y[0]==st[p])return!0;return!1}if(y.isEven())return!1;for(p=1;p<st.length;){for(var x=st[p],B=p+1;B<st.length&&x<Pb;)x*=st[B++];for(x=y.modInt(x);p<B;)if(x%st[p++]==0)return!1}return y.millerRabin(h)}function Fb(h){var p=this.subtract(r.ONE),y=p.getLowestSetBit();if(y<=0)return!1;var x=p.shiftRight(y);h=h+1>>1,h>st.length&&(h=st.length);for(var B=n(),M=0;M<h;++M){B.fromInt(st[Math.floor(Math.random()*st.length)]);var G=B.modPow(x,this);if(G.compareTo(r.ONE)!=0&&G.compareTo(p)!=0){for(var z=1;z++<y&&G.compareTo(p)!=0;)if(G=G.modPowInt(2,this),G.compareTo(r.ONE)==0)return!1;if(G.compareTo(p)!=0)return!1}}return!0}r.prototype.chunkSize=te,r.prototype.toRadix=le,r.prototype.fromRadix=ce,r.prototype.fromNumber=_e,r.prototype.bitwiseTo=J,r.prototype.changeBit=fr,r.prototype.addTo=Mi,r.prototype.dMultiply=ga,r.prototype.dAddOffset=Fi,r.prototype.multiplyLowerTo=ls,r.prototype.multiplyUpperTo=cs,r.prototype.modInt=Bb,r.prototype.millerRabin=Fb,r.prototype.clone=ft,r.prototype.intValue=vt,r.prototype.byteValue=I,r.prototype.shortValue=Z,r.prototype.signum=ee,r.prototype.toByteArray=we,r.prototype.equals=Be,r.prototype.min=Te,r.prototype.max=D,r.prototype.and=Ie,r.prototype.or=me,r.prototype.xor=ie,r.prototype.andNot=Ue,r.prototype.not=Ae,r.prototype.shiftLeft=ht,r.prototype.shiftRight=Et,r.prototype.getLowestSetBit=ui,r.prototype.bitCount=Ni,r.prototype.testBit=Li,r.prototype.setBit=Bi,r.prototype.clearBit=Ri,r.prototype.flipBit=Pi,r.prototype.add=es,r.prototype.subtract=Xr,r.prototype.multiply=ts,r.prototype.divide=rs,r.prototype.remainder=ns,r.prototype.divideAndRemainder=ss,r.prototype.modPow=Nb,r.prototype.modInverse=Rb,r.prototype.pow=as,r.prototype.gcd=Lb,r.prototype.isProbablePrime=Mb,r.prototype.square=is,r.prototype.Barrett=$t;var hs,yt,Ge;function qb(h){yt[Ge++]^=h&255,yt[Ge++]^=h>>8&255,yt[Ge++]^=h>>16&255,yt[Ge++]^=h>>24&255,Ge>=ba&&(Ge-=ba)}function wf(){qb(new Date().getTime())}if(yt==null){yt=new Array,Ge=0;var Lt;if(typeof window!="undefined"&&window.crypto){if(window.crypto.getRandomValues){var xf=new Uint8Array(32);for(window.crypto.getRandomValues(xf),Lt=0;Lt<32;++Lt)yt[Ge++]=xf[Lt]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var Sf=window.crypto.random(32);for(Lt=0;Lt<Sf.length;++Lt)yt[Ge++]=Sf.charCodeAt(Lt)&255}}for(;Ge<ba;)Lt=Math.floor(65536*Math.random()),yt[Ge++]=Lt>>>8,yt[Ge++]=Lt&255;Ge=0,wf()}function Db(){if(hs==null){for(wf(),hs=Vb(),hs.init(yt),Ge=0;Ge<yt.length;++Ge)yt[Ge]=0;Ge=0}return hs.next()}function jb(h){var p;for(p=0;p<h.length;++p)h[p]=Db()}function va(){}va.prototype.nextBytes=jb;function ya(){this.i=0,this.j=0,this.S=new Array}function Ub(h){var p,y,x;for(p=0;p<256;++p)this.S[p]=p;for(y=0,p=0;p<256;++p)y=y+this.S[p]+h[p%h.length]&255,x=this.S[p],this.S[p]=this.S[y],this.S[y]=x;this.i=0,this.j=0}function $b(){var h;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,h=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=h,this.S[h+this.S[this.i]&255]}ya.prototype.init=Ub,ya.prototype.next=$b;function Vb(){return new ya}var ba=256;typeof Us!="undefined"?Us=vm.exports={default:r,BigInteger:r,SecureRandom:va}:this.jsbn={BigInteger:r,SecureRandom:va}}).call(Us)});var fn=w($s=>{(function(){"use strict";var t={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function e(o){return r(s(o),arguments)}function i(o,a){return e.apply(null,[o].concat(a||[]))}function r(o,a){var l=1,c=o.length,u,f="",d,g,m,v,b,_,S,O;for(d=0;d<c;d++)if(typeof o[d]=="string")f+=o[d];else if(typeof o[d]=="object"){if(m=o[d],m.keys)for(u=a[l],g=0;g<m.keys.length;g++){if(u==null)throw new Error(e('[sprintf] Cannot access property "%s" of undefined value "%s"',m.keys[g],m.keys[g-1]));u=u[m.keys[g]]}else m.param_no?u=a[m.param_no]:u=a[l++];if(t.not_type.test(m.type)&&t.not_primitive.test(m.type)&&u instanceof Function&&(u=u()),t.numeric_arg.test(m.type)&&typeof u!="number"&&isNaN(u))throw new TypeError(e("[sprintf] expecting number but found %T",u));switch(t.number.test(m.type)&&(S=u>=0),m.type){case"b":u=parseInt(u,10).toString(2);break;case"c":u=String.fromCharCode(parseInt(u,10));break;case"d":case"i":u=parseInt(u,10);break;case"j":u=JSON.stringify(u,null,m.width?parseInt(m.width):0);break;case"e":u=m.precision?parseFloat(u).toExponential(m.precision):parseFloat(u).toExponential();break;case"f":u=m.precision?parseFloat(u).toFixed(m.precision):parseFloat(u);break;case"g":u=m.precision?String(Number(u.toPrecision(m.precision))):parseFloat(u);break;case"o":u=(parseInt(u,10)>>>0).toString(8);break;case"s":u=String(u),u=m.precision?u.substring(0,m.precision):u;break;case"t":u=String(!!u),u=m.precision?u.substring(0,m.precision):u;break;case"T":u=Object.prototype.toString.call(u).slice(8,-1).toLowerCase(),u=m.precision?u.substring(0,m.precision):u;break;case"u":u=parseInt(u,10)>>>0;break;case"v":u=u.valueOf(),u=m.precision?u.substring(0,m.precision):u;break;case"x":u=(parseInt(u,10)>>>0).toString(16);break;case"X":u=(parseInt(u,10)>>>0).toString(16).toUpperCase();break}t.json.test(m.type)?f+=u:(t.number.test(m.type)&&(!S||m.sign)?(O=S?"+":"-",u=u.toString().replace(t.sign,"")):O="",b=m.pad_char?m.pad_char==="0"?"0":m.pad_char.charAt(1):" ",_=m.width-(O+u).length,v=m.width&&_>0?b.repeat(_):"",f+=m.align?O+u+v:b==="0"?O+v+u:v+O+u)}return f}var n=Object.create(null);function s(o){if(n[o])return n[o];for(var a=o,l,c=[],u=0;a;){if((l=t.text.exec(a))!==null)c.push(l[0]);else if((l=t.modulo.exec(a))!==null)c.push("%");else if((l=t.placeholder.exec(a))!==null){if(l[2]){u|=1;var f=[],d=l[2],g=[];if((g=t.key.exec(d))!==null)for(f.push(g[1]);(d=d.substring(g[0].length))!=="";)if((g=t.key_access.exec(d))!==null)f.push(g[1]);else if((g=t.index_access.exec(d))!==null)f.push(g[1]);else throw new SyntaxError("[sprintf] failed to parse named argument key");else throw new SyntaxError("[sprintf] failed to parse named argument key");l[2]=f}else u|=2;if(u===3)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");c.push({placeholder:l[0],param_no:l[1],keys:l[2],sign:l[3],pad_char:l[4],align:l[5],width:l[6],precision:l[7],type:l[8]})}else throw new SyntaxError("[sprintf] unexpected placeholder");a=a.substring(l[0].length)}return n[o]=c}typeof $s!="undefined"&&($s.sprintf=e,$s.vsprintf=i),typeof window!="undefined"&&(window.sprintf=e,window.vsprintf=i,typeof define=="function"&&define.amd&&define(function(){return{sprintf:e,vsprintf:i}}))})()});var Hl=w(Gt=>{"use strict";var dS=Gt&&Gt.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),mS=Gt&&Gt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),_m=Gt&&Gt.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&dS(e,t,i);return mS(e,t),e};Object.defineProperty(Gt,"__esModule",{value:!0});Gt.Address4=void 0;var ym=_m(Dl()),Dt=_m(jl()),bm=js(),hn=$l(),Nr=fn(),Vl=class t{constructor(e){this.groups=Dt.GROUPS,this.parsedAddress=[],this.parsedSubnet="",this.subnet="/32",this.subnetMask=32,this.v4=!0,this.isCorrect=ym.isCorrect(Dt.BITS),this.isInSubnet=ym.isInSubnet,this.address=e;let i=Dt.RE_SUBNET_STRING.exec(e);if(i){if(this.parsedSubnet=i[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,this.subnetMask<0||this.subnetMask>Dt.BITS)throw new bm.AddressError("Invalid subnet mask.");e=e.replace(Dt.RE_SUBNET_STRING,"")}this.addressMinusSuffix=e,this.parsedAddress=this.parse(e)}static isValid(e){try{return new t(e),!0}catch{return!1}}parse(e){let i=e.split(".");if(!e.match(Dt.RE_ADDRESS))throw new bm.AddressError("Invalid IPv4 address.");return i}correctForm(){return this.parsedAddress.map(e=>parseInt(e,10)).join(".")}static fromHex(e){let i=e.replace(/:/g,"").padStart(8,"0"),r=[],n;for(n=0;n<8;n+=2){let s=i.slice(n,n+2);r.push(parseInt(s,16))}return new t(r.join("."))}static fromInteger(e){return t.fromHex(e.toString(16))}static fromArpa(e){let r=e.replace(/(\.in-addr\.arpa)?\.$/,"").split(".").reverse().join(".");return new t(r)}toHex(){return this.parsedAddress.map(e=>(0,Nr.sprintf)("%02x",parseInt(e,10))).join(":")}toArray(){return this.parsedAddress.map(e=>parseInt(e,10))}toGroup6(){let e=[],i;for(i=0;i<Dt.GROUPS;i+=2){let r=(0,Nr.sprintf)("%02x%02x",parseInt(this.parsedAddress[i],10),parseInt(this.parsedAddress[i+1],10));e.push((0,Nr.sprintf)("%x",parseInt(r,16)))}return e.join(":")}bigInteger(){return new hn.BigInteger(this.parsedAddress.map(e=>(0,Nr.sprintf)("%02x",parseInt(e,10))).join(""),16)}_startAddress(){return new hn.BigInteger(this.mask()+"0".repeat(Dt.BITS-this.subnetMask),2)}startAddress(){return t.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new hn.BigInteger("1");return t.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new hn.BigInteger(this.mask()+"1".repeat(Dt.BITS-this.subnetMask),2)}endAddress(){return t.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new hn.BigInteger("1");return t.fromBigInteger(this._endAddress().subtract(e))}static fromBigInteger(e){return t.fromInteger(parseInt(e.toString(),10))}mask(e){return e===void 0&&(e=this.subnetMask),this.getBitsBase2(0,e)}getBitsBase2(e,i){return this.binaryZeroPad().slice(e,i)}reverseForm(e){e||(e={});let i=this.correctForm().split(".").reverse().join(".");return e.omitSuffix?i:(0,Nr.sprintf)("%s.in-addr.arpa.",i)}isMulticast(){return this.isInSubnet(new t("*********/4"))}binaryZeroPad(){return this.bigInteger().toString(2).padStart(Dt.BITS,"0")}groupForV6(){let e=this.parsedAddress;return this.address.replace(Dt.RE_ADDRESS,(0,Nr.sprintf)('<span class="hover-group group-v4 group-6">%s</span>.<span class="hover-group group-v4 group-7">%s</span>',e.slice(0,2).join("."),e.slice(2,4).join(".")))}};Gt.Address4=Vl});var Gl=w(Fe=>{"use strict";Object.defineProperty(Fe,"__esModule",{value:!0});Fe.RE_URL_WITH_PORT=Fe.RE_URL=Fe.RE_ZONE_STRING=Fe.RE_SUBNET_STRING=Fe.RE_BAD_ADDRESS=Fe.RE_BAD_CHARACTERS=Fe.TYPES=Fe.SCOPES=Fe.GROUPS=Fe.BITS=void 0;Fe.BITS=128;Fe.GROUPS=8;Fe.SCOPES={0:"Reserved",1:"Interface local",2:"Link local",4:"Admin local",5:"Site local",8:"Organization local",14:"Global",15:"Reserved"};Fe.TYPES={"ff01::1/128":"Multicast (All nodes on this interface)","ff01::2/128":"Multicast (All routers on this interface)","ff02::1/128":"Multicast (All nodes on this link)","ff02::2/128":"Multicast (All routers on this link)","ff05::2/128":"Multicast (All routers in this site)","ff02::5/128":"Multicast (OSPFv3 AllSPF routers)","ff02::6/128":"Multicast (OSPFv3 AllDR routers)","ff02::9/128":"Multicast (RIP routers)","ff02::a/128":"Multicast (EIGRP routers)","ff02::d/128":"Multicast (PIM routers)","ff02::16/128":"Multicast (MLDv2 reports)","ff01::fb/128":"Multicast (mDNSv6)","ff02::fb/128":"Multicast (mDNSv6)","ff05::fb/128":"Multicast (mDNSv6)","ff02::1:2/128":"Multicast (All DHCP servers and relay agents on this link)","ff05::1:2/128":"Multicast (All DHCP servers and relay agents in this site)","ff02::1:3/128":"Multicast (All DHCP servers on this link)","ff05::1:3/128":"Multicast (All DHCP servers in this site)","::/128":"Unspecified","::1/128":"Loopback","ff00::/8":"Multicast","fe80::/10":"Link-local unicast"};Fe.RE_BAD_CHARACTERS=/([^0-9a-f:/%])/gi;Fe.RE_BAD_ADDRESS=/([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\/$)/gi;Fe.RE_SUBNET_STRING=/\/\d{1,3}(?=%|$)/;Fe.RE_ZONE_STRING=/%.*$/;Fe.RE_URL=new RegExp(/^\[{0,1}([0-9a-f:]+)\]{0,1}/);Fe.RE_URL_WITH_PORT=new RegExp(/\[([0-9a-f:]+)\]:([0-9]{1,5})/)});var Yl=w(Yt=>{"use strict";Object.defineProperty(Yt,"__esModule",{value:!0});Yt.simpleGroup=Yt.spanLeadingZeroes=Yt.spanAll=Yt.spanAllZeroes=void 0;var wm=fn();function xm(t){return t.replace(/(0+)/g,'<span class="zero">$1</span>')}Yt.spanAllZeroes=xm;function gS(t,e=0){return t.split("").map((r,n)=>(0,wm.sprintf)('<span class="digit value-%s position-%d">%s</span>',r,n+e,xm(r))).join("")}Yt.spanAll=gS;function Sm(t){return t.replace(/^(0+)/,'<span class="zero">$1</span>')}function vS(t){return t.split(":").map(i=>Sm(i)).join(":")}Yt.spanLeadingZeroes=vS;function yS(t,e=0){return t.split(":").map((r,n)=>/group-v4/.test(r)?r:(0,wm.sprintf)('<span class="hover-group group-%d">%s</span>',n+e,Sm(r)))}Yt.simpleGroup=yS});var Em=w(ze=>{"use strict";var bS=ze&&ze.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),_S=ze&&ze.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),wS=ze&&ze.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&bS(e,t,i);return _S(e,t),e};Object.defineProperty(ze,"__esModule",{value:!0});ze.possibleElisions=ze.simpleRegularExpression=ze.ADDRESS_BOUNDARY=ze.padGroup=ze.groupPossibilities=void 0;var xS=wS(Gl()),Lr=fn();function Hs(t){return(0,Lr.sprintf)("(%s)",t.join("|"))}ze.groupPossibilities=Hs;function Vs(t){return t.length<4?(0,Lr.sprintf)("0{0,%d}%s",4-t.length,t):t}ze.padGroup=Vs;ze.ADDRESS_BOUNDARY="[^A-Fa-f0-9:]";function SS(t){let e=[];t.forEach((r,n)=>{parseInt(r,16)===0&&e.push(n)});let i=e.map(r=>t.map((n,s)=>{if(s===r){let o=s===0||s===xS.GROUPS-1?":":"";return Hs([Vs(n),o])}return Vs(n)}).join(":"));return i.push(t.map(Vs).join(":")),Hs(i)}ze.simpleRegularExpression=SS;function ES(t,e,i){let r=e?"":":",n=i?"":":",s=[];!e&&!i&&s.push("::"),e&&i&&s.push(""),(i&&!e||!i&&e)&&s.push(":"),s.push((0,Lr.sprintf)("%s(:0{1,4}){1,%d}",r,t-1)),s.push((0,Lr.sprintf)("(0{1,4}:){1,%d}%s",t-1,n)),s.push((0,Lr.sprintf)("(0{1,4}:){%d}0{1,4}",t-1));for(let o=1;o<t-1;o++)for(let a=1;a<t-o;a++)s.push((0,Lr.sprintf)("(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}",a,t-a-o-1));return Hs(s)}ze.possibleElisions=ES});var Tm=w(Wt=>{"use strict";var kS=Wt&&Wt.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),OS=Wt&&Wt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Ys=Wt&&Wt.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&kS(e,t,i);return OS(e,t),e};Object.defineProperty(Wt,"__esModule",{value:!0});Wt.Address6=void 0;var km=Ys(Dl()),Wl=Ys(jl()),Le=Ys(Gl()),zl=Ys(Yl()),Yi=Hl(),Wi=Em(),ni=js(),lt=$l(),ct=fn();function Gs(t){if(!t)throw new Error("Assertion failed.")}function CS(t){let e=/(\d+)(\d{3})/;for(;e.test(t);)t=t.replace(e,"$1,$2");return t}function TS(t){return t=t.replace(/^(0{1,})([1-9]+)$/,'<span class="parse-error">$1</span>$2'),t=t.replace(/^(0{1,})(0)$/,'<span class="parse-error">$1</span>$2'),t}function AS(t,e){let i=[],r=[],n;for(n=0;n<t.length;n++)n<e[0]?i.push(t[n]):n>e[1]&&r.push(t[n]);return i.concat(["compact"]).concat(r)}function Om(t){return(0,ct.sprintf)("%04x",parseInt(t,16))}function Cm(t){return t&255}var Kl=class t{constructor(e,i){this.addressMinusSuffix="",this.parsedSubnet="",this.subnet="/128",this.subnetMask=128,this.v4=!1,this.zone="",this.isInSubnet=km.isInSubnet,this.isCorrect=km.isCorrect(Le.BITS),i===void 0?this.groups=Le.GROUPS:this.groups=i,this.address=e;let r=Le.RE_SUBNET_STRING.exec(e);if(r){if(this.parsedSubnet=r[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,Number.isNaN(this.subnetMask)||this.subnetMask<0||this.subnetMask>Le.BITS)throw new ni.AddressError("Invalid subnet mask.");e=e.replace(Le.RE_SUBNET_STRING,"")}else if(/\//.test(e))throw new ni.AddressError("Invalid subnet mask.");let n=Le.RE_ZONE_STRING.exec(e);n&&(this.zone=n[0],e=e.replace(Le.RE_ZONE_STRING,"")),this.addressMinusSuffix=e,this.parsedAddress=this.parse(this.addressMinusSuffix)}static isValid(e){try{return new t(e),!0}catch{return!1}}static fromBigInteger(e){let i=e.toString(16).padStart(32,"0"),r=[],n;for(n=0;n<Le.GROUPS;n++)r.push(i.slice(n*4,(n+1)*4));return new t(r.join(":"))}static fromURL(e){let i,r=null,n;if(e.indexOf("[")!==-1&&e.indexOf("]:")!==-1){if(n=Le.RE_URL_WITH_PORT.exec(e),n===null)return{error:"failed to parse address with port",address:null,port:null};i=n[1],r=n[2]}else if(e.indexOf("/")!==-1){if(e=e.replace(/^[a-z0-9]+:\/\//,""),n=Le.RE_URL.exec(e),n===null)return{error:"failed to parse address from URL",address:null,port:null};i=n[1]}else i=e;return r?(r=parseInt(r,10),(r<0||r>65536)&&(r=null)):r=null,{address:new t(i),port:r}}static fromAddress4(e){let i=new Yi.Address4(e),r=Le.BITS-(Wl.BITS-i.subnetMask);return new t(`::ffff:${i.correctForm()}/${r}`)}static fromArpa(e){let i=e.replace(/(\.ip6\.arpa)?\.$/,""),r=7;if(i.length!==63)throw new ni.AddressError("Invalid 'ip6.arpa' form.");let n=i.split(".").reverse();for(let s=r;s>0;s--){let o=s*4;n.splice(o,0,":")}return i=n.join(""),new t(i)}microsoftTranscription(){return(0,ct.sprintf)("%s.ipv6-literal.net",this.correctForm().replace(/:/g,"-"))}mask(e=this.subnetMask){return this.getBitsBase2(0,e)}possibleSubnets(e=128){let i=Le.BITS-this.subnetMask,r=Math.abs(e-Le.BITS),n=i-r;return n<0?"0":CS(new lt.BigInteger("2",10).pow(n).toString(10))}_startAddress(){return new lt.BigInteger(this.mask()+"0".repeat(Le.BITS-this.subnetMask),2)}startAddress(){return t.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new lt.BigInteger("1");return t.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new lt.BigInteger(this.mask()+"1".repeat(Le.BITS-this.subnetMask),2)}endAddress(){return t.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new lt.BigInteger("1");return t.fromBigInteger(this._endAddress().subtract(e))}getScope(){let e=Le.SCOPES[this.getBits(12,16).intValue()];return this.getType()==="Global unicast"&&e!=="Link local"&&(e="Global"),e||"Unknown"}getType(){for(let e of Object.keys(Le.TYPES))if(this.isInSubnet(new t(e)))return Le.TYPES[e];return"Global unicast"}getBits(e,i){return new lt.BigInteger(this.getBitsBase2(e,i),2)}getBitsBase2(e,i){return this.binaryZeroPad().slice(e,i)}getBitsBase16(e,i){let r=i-e;if(r%4!==0)throw new Error("Length of bits to retrieve must be divisible by four");return this.getBits(e,i).toString(16).padStart(r/4,"0")}getBitsPastSubnet(){return this.getBitsBase2(this.subnetMask,Le.BITS)}reverseForm(e){e||(e={});let i=Math.floor(this.subnetMask/4),r=this.canonicalForm().replace(/:/g,"").split("").slice(0,i).reverse().join(".");return i>0?e.omitSuffix?r:(0,ct.sprintf)("%s.ip6.arpa.",r):e.omitSuffix?"":"ip6.arpa."}correctForm(){let e,i=[],r=0,n=[];for(e=0;e<this.parsedAddress.length;e++){let a=parseInt(this.parsedAddress[e],16);a===0&&r++,a!==0&&r>0&&(r>1&&n.push([e-r,e-1]),r=0)}r>1&&n.push([this.parsedAddress.length-r,this.parsedAddress.length-1]);let s=n.map(a=>a[1]-a[0]+1);if(n.length>0){let a=s.indexOf(Math.max(...s));i=AS(this.parsedAddress,n[a])}else i=this.parsedAddress;for(e=0;e<i.length;e++)i[e]!=="compact"&&(i[e]=parseInt(i[e],16).toString(16));let o=i.join(":");return o=o.replace(/^compact$/,"::"),o=o.replace(/^compact|compact$/,":"),o=o.replace(/compact/,""),o}binaryZeroPad(){return this.bigInteger().toString(2).padStart(Le.BITS,"0")}parse4in6(e){let i=e.split(":"),n=i.slice(-1)[0].match(Wl.RE_ADDRESS);if(n){this.parsedAddress4=n[0],this.address4=new Yi.Address4(this.parsedAddress4);for(let s=0;s<this.address4.groups;s++)if(/^0[0-9]+/.test(this.address4.parsedAddress[s]))throw new ni.AddressError("IPv4 addresses can't have leading zeroes.",e.replace(Wl.RE_ADDRESS,this.address4.parsedAddress.map(TS).join(".")));this.v4=!0,i[i.length-1]=this.address4.toGroup6(),e=i.join(":")}return e}parse(e){e=this.parse4in6(e);let i=e.match(Le.RE_BAD_CHARACTERS);if(i)throw new ni.AddressError((0,ct.sprintf)("Bad character%s detected in address: %s",i.length>1?"s":"",i.join("")),e.replace(Le.RE_BAD_CHARACTERS,'<span class="parse-error">$1</span>'));let r=e.match(Le.RE_BAD_ADDRESS);if(r)throw new ni.AddressError((0,ct.sprintf)("Address failed regex: %s",r.join("")),e.replace(Le.RE_BAD_ADDRESS,'<span class="parse-error">$1</span>'));let n=[],s=e.split("::");if(s.length===2){let o=s[0].split(":"),a=s[1].split(":");o.length===1&&o[0]===""&&(o=[]),a.length===1&&a[0]===""&&(a=[]);let l=this.groups-(o.length+a.length);if(!l)throw new ni.AddressError("Error parsing groups");this.elidedGroups=l,this.elisionBegin=o.length,this.elisionEnd=o.length+this.elidedGroups,n=n.concat(o);for(let c=0;c<l;c++)n.push("0");n=n.concat(a)}else if(s.length===1)n=e.split(":"),this.elidedGroups=0;else throw new ni.AddressError("Too many :: groups found");if(n=n.map(o=>(0,ct.sprintf)("%x",parseInt(o,16))),n.length!==this.groups)throw new ni.AddressError("Incorrect number of groups found");return n}canonicalForm(){return this.parsedAddress.map(Om).join(":")}decimal(){return this.parsedAddress.map(e=>(0,ct.sprintf)("%05d",parseInt(e,16))).join(":")}bigInteger(){return new lt.BigInteger(this.parsedAddress.map(Om).join(""),16)}to4(){let e=this.binaryZeroPad().split("");return Yi.Address4.fromHex(new lt.BigInteger(e.slice(96,128).join(""),2).toString(16))}to4in6(){let e=this.to4(),r=new t(this.parsedAddress.slice(0,6).join(":"),6).correctForm(),n="";return/:$/.test(r)||(n=":"),r+n+e.address}inspectTeredo(){let e=this.getBitsBase16(0,32),i=this.getBits(80,96).xor(new lt.BigInteger("ffff",16)).toString(),r=Yi.Address4.fromHex(this.getBitsBase16(32,64)),n=Yi.Address4.fromHex(this.getBits(96,128).xor(new lt.BigInteger("ffffffff",16)).toString(16)),s=this.getBits(64,80),o=this.getBitsBase2(64,80),a=s.testBit(15),l=s.testBit(14),c=s.testBit(8),u=s.testBit(9),f=new lt.BigInteger(o.slice(2,6)+o.slice(8,16),2).toString(10);return{prefix:(0,ct.sprintf)("%s:%s",e.slice(0,4),e.slice(4,8)),server4:r.address,client4:n.address,flags:o,coneNat:a,microsoft:{reserved:l,universalLocal:u,groupIndividual:c,nonce:f},udpPort:i}}inspect6to4(){let e=this.getBitsBase16(0,16),i=Yi.Address4.fromHex(this.getBitsBase16(16,48));return{prefix:(0,ct.sprintf)("%s",e.slice(0,4)),gateway:i.address}}to6to4(){if(!this.is4())return null;let e=["2002",this.getBitsBase16(96,112),this.getBitsBase16(112,128),"","/16"].join(":");return new t(e)}toByteArray(){let e=this.bigInteger().toByteArray();return e.length===17&&e[0]===0?e.slice(1):e}toUnsignedByteArray(){return this.toByteArray().map(Cm)}static fromByteArray(e){return this.fromUnsignedByteArray(e.map(Cm))}static fromUnsignedByteArray(e){let i=new lt.BigInteger("256",10),r=new lt.BigInteger("0",10),n=new lt.BigInteger("1",10);for(let s=e.length-1;s>=0;s--)r=r.add(n.multiply(new lt.BigInteger(e[s].toString(10),10))),n=n.multiply(i);return t.fromBigInteger(r)}isCanonical(){return this.addressMinusSuffix===this.canonicalForm()}isLinkLocal(){return this.getBitsBase2(0,64)==="1111111010000000000000000000000000000000000000000000000000000000"}isMulticast(){return this.getType()==="Multicast"}is4(){return this.v4}isTeredo(){return this.isInSubnet(new t("2001::/32"))}is6to4(){return this.isInSubnet(new t("2002::/16"))}isLoopback(){return this.getType()==="Loopback"}href(e){return e===void 0?e="":e=(0,ct.sprintf)(":%s",e),(0,ct.sprintf)("http://[%s]%s/",this.correctForm(),e)}link(e){e||(e={}),e.className===void 0&&(e.className=""),e.prefix===void 0&&(e.prefix="/#address="),e.v4===void 0&&(e.v4=!1);let i=this.correctForm;return e.v4&&(i=this.to4in6),e.className?(0,ct.sprintf)('<a href="%1$s%2$s" class="%3$s">%2$s</a>',e.prefix,i.call(this),e.className):(0,ct.sprintf)('<a href="%1$s%2$s">%2$s</a>',e.prefix,i.call(this))}group(){if(this.elidedGroups===0)return zl.simpleGroup(this.address).join(":");Gs(typeof this.elidedGroups=="number"),Gs(typeof this.elisionBegin=="number");let e=[],[i,r]=this.address.split("::");i.length?e.push(...zl.simpleGroup(i)):e.push("");let n=["hover-group"];for(let s=this.elisionBegin;s<this.elisionBegin+this.elidedGroups;s++)n.push((0,ct.sprintf)("group-%d",s));return e.push((0,ct.sprintf)('<span class="%s"></span>',n.join(" "))),r.length?e.push(...zl.simpleGroup(r,this.elisionEnd)):e.push(""),this.is4()&&(Gs(this.address4 instanceof Yi.Address4),e.pop(),e.push(this.address4.groupForV6())),e.join(":")}regularExpressionString(e=!1){let i=[],r=new t(this.correctForm());if(r.elidedGroups===0)i.push((0,Wi.simpleRegularExpression)(r.parsedAddress));else if(r.elidedGroups===Le.GROUPS)i.push((0,Wi.possibleElisions)(Le.GROUPS));else{let n=r.address.split("::");n[0].length&&i.push((0,Wi.simpleRegularExpression)(n[0].split(":"))),Gs(typeof r.elidedGroups=="number"),i.push((0,Wi.possibleElisions)(r.elidedGroups,n[0].length!==0,n[1].length!==0)),n[1].length&&i.push((0,Wi.simpleRegularExpression)(n[1].split(":"))),i=[i.join(":")]}return e||(i=["(?=^|",Wi.ADDRESS_BOUNDARY,"|[^\\w\\:])(",...i,")(?=[^\\w\\:]|",Wi.ADDRESS_BOUNDARY,"|$)"]),i.join("")}regularExpression(e=!1){return new RegExp(this.regularExpressionString(e),"i")}};Wt.Address6=Kl});var Jl=w(rt=>{"use strict";var IS=rt&&rt.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),NS=rt&&rt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),LS=rt&&rt.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&IS(e,t,i);return NS(e,t),e};Object.defineProperty(rt,"__esModule",{value:!0});rt.v6=rt.AddressError=rt.Address6=rt.Address4=void 0;var BS=Hl();Object.defineProperty(rt,"Address4",{enumerable:!0,get:function(){return BS.Address4}});var RS=Tm();Object.defineProperty(rt,"Address6",{enumerable:!0,get:function(){return RS.Address6}});var PS=js();Object.defineProperty(rt,"AddressError",{enumerable:!0,get:function(){return PS.AddressError}});var MS=LS(Yl());rt.v6={helpers:MS}});var Rm=w(Tt=>{"use strict";Object.defineProperty(Tt,"__esModule",{value:!0});Tt.ipToBuffer=Tt.int32ToIpv4=Tt.ipv4ToInt32=Tt.validateSocksClientChainOptions=Tt.validateSocksClientOptions=void 0;var ut=ql(),Ke=Ml(),FS=require("stream"),Zl=Jl(),Am=require("net");function qS(t,e=["connect","bind","associate"]){if(!Ke.SocksCommand[t.command])throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksCommand,t);if(e.indexOf(t.command)===-1)throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksCommandForOperation,t);if(!Nm(t.destination))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsDestination,t);if(!Lm(t.proxy))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsProxy,t);if(Im(t.proxy,t),t.timeout&&!Bm(t.timeout))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsTimeout,t);if(t.existing_socket&&!(t.existing_socket instanceof FS.Duplex))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsExistingSocket,t)}Tt.validateSocksClientOptions=qS;function DS(t){if(t.command!=="connect")throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksCommandChain,t);if(!Nm(t.destination))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsDestination,t);if(!(t.proxies&&Array.isArray(t.proxies)&&t.proxies.length>=2))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsProxiesLength,t);if(t.proxies.forEach(e=>{if(!Lm(e))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsProxy,t);Im(e,t)}),t.timeout&&!Bm(t.timeout))throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsTimeout,t)}Tt.validateSocksClientChainOptions=DS;function Im(t,e){if(t.custom_auth_method!==void 0){if(t.custom_auth_method<Ke.SOCKS5_CUSTOM_AUTH_START||t.custom_auth_method>Ke.SOCKS5_CUSTOM_AUTH_END)throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsCustomAuthRange,e);if(t.custom_auth_request_handler===void 0||typeof t.custom_auth_request_handler!="function")throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_size===void 0)throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_handler===void 0||typeof t.custom_auth_response_handler!="function")throw new ut.SocksClientError(Ke.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e)}}function Nm(t){return t&&typeof t.host=="string"&&typeof t.port=="number"&&t.port>=0&&t.port<=65535}function Lm(t){return t&&(typeof t.host=="string"||typeof t.ipaddress=="string")&&typeof t.port=="number"&&t.port>=0&&t.port<=65535&&(t.type===4||t.type===5)}function Bm(t){return typeof t=="number"&&t>0}function jS(t){return new Zl.Address4(t).toArray().reduce((i,r)=>(i<<8)+r,0)}Tt.ipv4ToInt32=jS;function US(t){let e=t>>>24&255,i=t>>>16&255,r=t>>>8&255,n=t&255;return[e,i,r,n].join(".")}Tt.int32ToIpv4=US;function $S(t){if(Am.isIPv4(t)){let e=new Zl.Address4(t);return Buffer.from(e.toArray())}else if(Am.isIPv6(t)){let e=new Zl.Address6(t);return Buffer.from(e.canonicalForm().split(":").map(i=>i.padStart(4,"0")).join(""),"hex")}else throw new Error("Invalid IP address format")}Tt.ipToBuffer=$S});var Pm=w(Ws=>{"use strict";Object.defineProperty(Ws,"__esModule",{value:!0});Ws.ReceiveBuffer=void 0;var Ql=class{constructor(e=4096){this.buffer=Buffer.allocUnsafe(e),this.offset=0,this.originalSize=e}get length(){return this.offset}append(e){if(!Buffer.isBuffer(e))throw new Error("Attempted to append a non-buffer instance to ReceiveBuffer.");if(this.offset+e.length>=this.buffer.length){let i=this.buffer;this.buffer=Buffer.allocUnsafe(Math.max(this.buffer.length+this.originalSize,this.buffer.length+e.length)),i.copy(this.buffer)}return e.copy(this.buffer,this.offset),this.offset+=e.length}peek(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");return this.buffer.slice(0,e)}get(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");let i=Buffer.allocUnsafe(e);return this.buffer.slice(0,e).copy(i),this.buffer.copyWithin(0,e,e+this.offset-e),this.offset-=e,i}};Ws.ReceiveBuffer=Ql});var Mm=w(gi=>{"use strict";var Br=gi&&gi.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function a(u){try{c(r.next(u))}catch(f){o(f)}}function l(u){try{c(r.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((r=r.apply(t,e||[])).next())})};Object.defineProperty(gi,"__esModule",{value:!0});gi.SocksClientError=gi.SocksClient=void 0;var VS=require("events"),Rr=require("net"),dt=um(),F=Ml(),xt=Rm(),HS=Pm(),ec=ql();Object.defineProperty(gi,"SocksClientError",{enumerable:!0,get:function(){return ec.SocksClientError}});var Xl=Jl(),tc=class t extends VS.EventEmitter{constructor(e){super(),this.options=Object.assign({},e),(0,xt.validateSocksClientOptions)(e),this.setState(F.SocksClientState.Created)}static createConnection(e,i){return new Promise((r,n)=>{try{(0,xt.validateSocksClientOptions)(e,["connect"])}catch(o){return typeof i=="function"?(i(o),r(o)):n(o)}let s=new t(e);s.connect(e.existing_socket),s.once("established",o=>{s.removeAllListeners(),typeof i=="function"&&i(null,o),r(o)}),s.once("error",o=>{s.removeAllListeners(),typeof i=="function"?(i(o),r(o)):n(o)})})}static createConnectionChain(e,i){return new Promise((r,n)=>Br(this,void 0,void 0,function*(){try{(0,xt.validateSocksClientChainOptions)(e)}catch(s){return typeof i=="function"?(i(s),r(s)):n(s)}e.randomizeChain&&(0,ec.shuffleArray)(e.proxies);try{let s;for(let o=0;o<e.proxies.length;o++){let a=e.proxies[o],l=o===e.proxies.length-1?e.destination:{host:e.proxies[o+1].host||e.proxies[o+1].ipaddress,port:e.proxies[o+1].port},c=yield t.createConnection({command:"connect",proxy:a,destination:l,existing_socket:s});s=s||c.socket}typeof i=="function"?(i(null,{socket:s}),r({socket:s})):r({socket:s})}catch(s){typeof i=="function"?(i(s),r(s)):n(s)}}))}static createUDPFrame(e){let i=new dt.SmartBuffer;return i.writeUInt16BE(0),i.writeUInt8(e.frameNumber||0),Rr.isIPv4(e.remoteHost.host)?(i.writeUInt8(F.Socks5HostType.IPv4),i.writeUInt32BE((0,xt.ipv4ToInt32)(e.remoteHost.host))):Rr.isIPv6(e.remoteHost.host)?(i.writeUInt8(F.Socks5HostType.IPv6),i.writeBuffer((0,xt.ipToBuffer)(e.remoteHost.host))):(i.writeUInt8(F.Socks5HostType.Hostname),i.writeUInt8(Buffer.byteLength(e.remoteHost.host)),i.writeString(e.remoteHost.host)),i.writeUInt16BE(e.remoteHost.port),i.writeBuffer(e.data),i.toBuffer()}static parseUDPFrame(e){let i=dt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r=i.readUInt8(),n=i.readUInt8(),s;n===F.Socks5HostType.IPv4?s=(0,xt.int32ToIpv4)(i.readUInt32BE()):n===F.Socks5HostType.IPv6?s=Xl.Address6.fromByteArray(Array.from(i.readBuffer(16))).canonicalForm():s=i.readString(i.readUInt8());let o=i.readUInt16BE();return{frameNumber:r,remoteHost:{host:s,port:o},data:i.readBuffer()}}setState(e){this.state!==F.SocksClientState.Error&&(this.state=e)}connect(e){this.onDataReceived=r=>this.onDataReceivedHandler(r),this.onClose=()=>this.onCloseHandler(),this.onError=r=>this.onErrorHandler(r),this.onConnect=()=>this.onConnectHandler();let i=setTimeout(()=>this.onEstablishedTimeout(),this.options.timeout||F.DEFAULT_TIMEOUT);i.unref&&typeof i.unref=="function"&&i.unref(),e?this.socket=e:this.socket=new Rr.Socket,this.socket.once("close",this.onClose),this.socket.once("error",this.onError),this.socket.once("connect",this.onConnect),this.socket.on("data",this.onDataReceived),this.setState(F.SocksClientState.Connecting),this.receiveBuffer=new HS.ReceiveBuffer,e?this.socket.emit("connect"):(this.socket.connect(this.getSocketOptions()),this.options.set_tcp_nodelay!==void 0&&this.options.set_tcp_nodelay!==null&&this.socket.setNoDelay(!!this.options.set_tcp_nodelay)),this.prependOnceListener("established",r=>{setImmediate(()=>{if(this.receiveBuffer.length>0){let n=this.receiveBuffer.get(this.receiveBuffer.length);r.socket.emit("data",n)}r.socket.resume()})})}getSocketOptions(){return Object.assign(Object.assign({},this.options.socket_options),{host:this.options.proxy.host||this.options.proxy.ipaddress,port:this.options.proxy.port})}onEstablishedTimeout(){this.state!==F.SocksClientState.Established&&this.state!==F.SocksClientState.BoundWaitingForConnection&&this.closeSocket(F.ERRORS.ProxyConnectionTimedOut)}onConnectHandler(){this.setState(F.SocksClientState.Connected),this.options.proxy.type===4?this.sendSocks4InitialHandshake():this.sendSocks5InitialHandshake(),this.setState(F.SocksClientState.SentInitialHandshake)}onDataReceivedHandler(e){this.receiveBuffer.append(e),this.processData()}processData(){for(;this.state!==F.SocksClientState.Established&&this.state!==F.SocksClientState.Error&&this.receiveBuffer.length>=this.nextRequiredPacketBufferSize;)if(this.state===F.SocksClientState.SentInitialHandshake)this.options.proxy.type===4?this.handleSocks4FinalHandshakeResponse():this.handleInitialSocks5HandshakeResponse();else if(this.state===F.SocksClientState.SentAuthentication)this.handleInitialSocks5AuthenticationHandshakeResponse();else if(this.state===F.SocksClientState.SentFinalHandshake)this.handleSocks5FinalHandshakeResponse();else if(this.state===F.SocksClientState.BoundWaitingForConnection)this.options.proxy.type===4?this.handleSocks4IncomingConnectionResponse():this.handleSocks5IncomingConnectionResponse();else{this.closeSocket(F.ERRORS.InternalError);break}}onCloseHandler(){this.closeSocket(F.ERRORS.SocketClosed)}onErrorHandler(e){this.closeSocket(e.message)}removeInternalSocketHandlers(){this.socket.pause(),this.socket.removeListener("data",this.onDataReceived),this.socket.removeListener("close",this.onClose),this.socket.removeListener("error",this.onError),this.socket.removeListener("connect",this.onConnect)}closeSocket(e){this.state!==F.SocksClientState.Error&&(this.setState(F.SocksClientState.Error),this.socket.destroy(),this.removeInternalSocketHandlers(),this.emit("error",new ec.SocksClientError(e,this.options)))}sendSocks4InitialHandshake(){let e=this.options.proxy.userId||"",i=new dt.SmartBuffer;i.writeUInt8(4),i.writeUInt8(F.SocksCommand[this.options.command]),i.writeUInt16BE(this.options.destination.port),Rr.isIPv4(this.options.destination.host)?(i.writeBuffer((0,xt.ipToBuffer)(this.options.destination.host)),i.writeStringNT(e)):(i.writeUInt8(0),i.writeUInt8(0),i.writeUInt8(0),i.writeUInt8(1),i.writeStringNT(e),i.writeStringNT(this.options.destination.host)),this.nextRequiredPacketBufferSize=F.SOCKS_INCOMING_PACKET_SIZES.Socks4Response,this.socket.write(i.toBuffer())}handleSocks4FinalHandshakeResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==F.Socks4Response.Granted)this.closeSocket(`${F.ERRORS.Socks4ProxyRejectedConnection} - (${F.Socks4Response[e[1]]})`);else if(F.SocksCommand[this.options.command]===F.SocksCommand.bind){let i=dt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r={port:i.readUInt16BE(),host:(0,xt.int32ToIpv4)(i.readUInt32BE())};r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress),this.setState(F.SocksClientState.BoundWaitingForConnection),this.emit("bound",{remoteHost:r,socket:this.socket})}else this.setState(F.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{socket:this.socket})}handleSocks4IncomingConnectionResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==F.Socks4Response.Granted)this.closeSocket(`${F.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${F.Socks4Response[e[1]]})`);else{let i=dt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r={port:i.readUInt16BE(),host:(0,xt.int32ToIpv4)(i.readUInt32BE())};this.setState(F.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}sendSocks5InitialHandshake(){let e=new dt.SmartBuffer,i=[F.Socks5Auth.NoAuth];(this.options.proxy.userId||this.options.proxy.password)&&i.push(F.Socks5Auth.UserPass),this.options.proxy.custom_auth_method!==void 0&&i.push(this.options.proxy.custom_auth_method),e.writeUInt8(5),e.writeUInt8(i.length);for(let r of i)e.writeUInt8(r);this.nextRequiredPacketBufferSize=F.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse,this.socket.write(e.toBuffer()),this.setState(F.SocksClientState.SentInitialHandshake)}handleInitialSocks5HandshakeResponse(){let e=this.receiveBuffer.get(2);e[0]!==5?this.closeSocket(F.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion):e[1]===F.SOCKS5_NO_ACCEPTABLE_AUTH?this.closeSocket(F.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType):e[1]===F.Socks5Auth.NoAuth?(this.socks5ChosenAuthType=F.Socks5Auth.NoAuth,this.sendSocks5CommandRequest()):e[1]===F.Socks5Auth.UserPass?(this.socks5ChosenAuthType=F.Socks5Auth.UserPass,this.sendSocks5UserPassAuthentication()):e[1]===this.options.proxy.custom_auth_method?(this.socks5ChosenAuthType=this.options.proxy.custom_auth_method,this.sendSocks5CustomAuthentication()):this.closeSocket(F.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType)}sendSocks5UserPassAuthentication(){let e=this.options.proxy.userId||"",i=this.options.proxy.password||"",r=new dt.SmartBuffer;r.writeUInt8(1),r.writeUInt8(Buffer.byteLength(e)),r.writeString(e),r.writeUInt8(Buffer.byteLength(i)),r.writeString(i),this.nextRequiredPacketBufferSize=F.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse,this.socket.write(r.toBuffer()),this.setState(F.SocksClientState.SentAuthentication)}sendSocks5CustomAuthentication(){return Br(this,void 0,void 0,function*(){this.nextRequiredPacketBufferSize=this.options.proxy.custom_auth_response_size,this.socket.write(yield this.options.proxy.custom_auth_request_handler()),this.setState(F.SocksClientState.SentAuthentication)})}handleSocks5CustomAuthHandshakeResponse(e){return Br(this,void 0,void 0,function*(){return yield this.options.proxy.custom_auth_response_handler(e)})}handleSocks5AuthenticationNoAuthHandshakeResponse(e){return Br(this,void 0,void 0,function*(){return e[1]===0})}handleSocks5AuthenticationUserPassHandshakeResponse(e){return Br(this,void 0,void 0,function*(){return e[1]===0})}handleInitialSocks5AuthenticationHandshakeResponse(){return Br(this,void 0,void 0,function*(){this.setState(F.SocksClientState.ReceivedAuthenticationResponse);let e=!1;this.socks5ChosenAuthType===F.Socks5Auth.NoAuth?e=yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===F.Socks5Auth.UserPass?e=yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===this.options.proxy.custom_auth_method&&(e=yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size))),e?this.sendSocks5CommandRequest():this.closeSocket(F.ERRORS.Socks5AuthenticationFailed)})}sendSocks5CommandRequest(){let e=new dt.SmartBuffer;e.writeUInt8(5),e.writeUInt8(F.SocksCommand[this.options.command]),e.writeUInt8(0),Rr.isIPv4(this.options.destination.host)?(e.writeUInt8(F.Socks5HostType.IPv4),e.writeBuffer((0,xt.ipToBuffer)(this.options.destination.host))):Rr.isIPv6(this.options.destination.host)?(e.writeUInt8(F.Socks5HostType.IPv6),e.writeBuffer((0,xt.ipToBuffer)(this.options.destination.host))):(e.writeUInt8(F.Socks5HostType.Hostname),e.writeUInt8(this.options.destination.host.length),e.writeString(this.options.destination.host)),e.writeUInt16BE(this.options.destination.port),this.nextRequiredPacketBufferSize=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.socket.write(e.toBuffer()),this.setState(F.SocksClientState.SentFinalHandshake)}handleSocks5FinalHandshakeResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==F.Socks5Response.Granted)this.closeSocket(`${F.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${F.Socks5Response[e[1]]}`);else{let i=e[3],r,n;if(i===F.Socks5HostType.IPv4){let s=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=dt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,xt.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(i===F.Socks5HostType.Hostname){let s=e[4],o=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=dt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(i===F.Socks5HostType.IPv6){let s=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=dt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:Xl.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(F.SocksClientState.ReceivedFinalResponse),F.SocksCommand[this.options.command]===F.SocksCommand.connect?(this.setState(F.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})):F.SocksCommand[this.options.command]===F.SocksCommand.bind?(this.setState(F.SocksClientState.BoundWaitingForConnection),this.nextRequiredPacketBufferSize=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.emit("bound",{remoteHost:r,socket:this.socket})):F.SocksCommand[this.options.command]===F.SocksCommand.associate&&(this.setState(F.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket}))}}handleSocks5IncomingConnectionResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==F.Socks5Response.Granted)this.closeSocket(`${F.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${F.Socks5Response[e[1]]}`);else{let i=e[3],r,n;if(i===F.Socks5HostType.IPv4){let s=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=dt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,xt.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(i===F.Socks5HostType.Hostname){let s=e[4],o=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=dt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(i===F.Socks5HostType.IPv6){let s=F.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=dt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:Xl.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(F.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}get socksClientOptions(){return Object.assign({},this.options)}};gi.SocksClient=tc});var Fm=w(zi=>{"use strict";var GS=zi&&zi.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),YS=zi&&zi.__exportStar||function(t,e){for(var i in t)i!=="default"&&!Object.prototype.hasOwnProperty.call(e,i)&&GS(e,t,i)};Object.defineProperty(zi,"__esModule",{value:!0});YS(Mm(),zi)});var qm=w(Ki=>{"use strict";var WS=Ki&&Ki.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function a(u){try{c(r.next(u))}catch(f){o(f)}}function l(u){try{c(r.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((r=r.apply(t,e||[])).next())})},zs=Ki&&Ki.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ki,"__esModule",{value:!0});var zS=zs(require("dns")),KS=zs(require("tls")),JS=zs(require("url")),ZS=zs(br()),QS=ja(),XS=Fm(),ic=ZS.default("socks-proxy-agent");function eE(t){return new Promise((e,i)=>{zS.default.lookup(t,(r,n)=>{r?i(r):e(n)})})}function tE(t){let e=0,i=!1,r=5,n=t.hostname||t.host;if(!n)throw new TypeError('No "host"');if(typeof t.port=="number"?e=t.port:typeof t.port=="string"&&(e=parseInt(t.port,10)),e||(e=1080),t.protocol)switch(t.protocol.replace(":","")){case"socks4":i=!0;case"socks4a":r=4;break;case"socks5":i=!0;case"socks":case"socks5h":r=5;break;default:throw new TypeError(`A "socks" protocol must be specified! Got: ${t.protocol}`)}if(typeof t.type!="undefined")if(t.type===4||t.type===5)r=t.type;else throw new TypeError(`"type" must be 4 or 5, got: ${t.type}`);let s={host:n,port:e,type:r},o=t.userId||t.username,a=t.password;if(t.auth){let l=t.auth.split(":");o=l[0],a=l[1]}return o&&Object.defineProperty(s,"userId",{value:o,enumerable:!1}),a&&Object.defineProperty(s,"password",{value:a,enumerable:!1}),{lookup:i,proxy:s}}var rc=class extends QS.Agent{constructor(e){let i;if(typeof e=="string"?i=JS.default.parse(e):i=e,!i)throw new TypeError("a SOCKS proxy server `host` and `port` must be specified!");super(i);let r=tE(i);this.lookup=r.lookup,this.proxy=r.proxy,this.tlsConnectionOptions=i.tls||{}}callback(e,i){return WS(this,void 0,void 0,function*(){let{lookup:r,proxy:n}=this,{host:s,port:o,timeout:a}=i;if(!s)throw new Error("No `host` defined!");r&&(s=yield eE(s));let l={proxy:n,destination:{host:s,port:o},command:"connect",timeout:a};ic("Creating socks proxy connection: %o",l);let{socket:c}=yield XS.SocksClient.createConnection(l);if(ic("Successfully created socks proxy connection"),i.secureEndpoint){ic("Upgrading socket connection to TLS");let u=i.servername||i.host;return KS.default.connect(Object.assign(Object.assign(Object.assign({},iE(i,"host","hostname","path","port")),{socket:c,servername:u}),this.tlsConnectionOptions))}return c})}};Ki.default=rc;function iE(t,...e){let i={},r;for(r in t)e.includes(r)||(i[r]=t[r]);return i}});var jm=w((oc,Dm)=>{"use strict";var rE=oc&&oc.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},nc=rE(qm());function sc(t){return new nc.default(t)}(function(t){t.SocksProxyAgent=nc.default,t.prototype=nc.default.prototype})(sc||(sc={}));Dm.exports=sc});var $m=w((oN,Um)=>{"use strict";var nE=/[|\\{}()[\]^$+*?.-]/g;Um.exports=t=>{if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(nE,"\\$&")}});var Ym=w((aN,Gm)=>{"use strict";var sE=$m(),oE=typeof process=="object"&&process&&typeof process.cwd=="function"?process.cwd():".",Hm=[].concat(require("module").builtinModules,"bootstrap_node","node").map(t=>new RegExp(`(?:\\((?:node:)?${t}(?:\\.js)?:\\d+:\\d+\\)$|^\\s*at (?:node:)?${t}(?:\\.js)?:\\d+:\\d+$)`));Hm.push(/\((?:node:)?internal\/[^:]+:\d+:\d+\)$/,/\s*at (?:node:)?internal\/[^:]+:\d+:\d+$/,/\/\.node-spawn-wrap-\w+-\w+\/node:\d+:\d+\)?$/);var ac=class t{constructor(e){e={ignoredPackages:[],...e},"internals"in e||(e.internals=t.nodeInternals()),"cwd"in e||(e.cwd=oE),this._cwd=e.cwd.replace(/\\/g,"/"),this._internals=[].concat(e.internals,aE(e.ignoredPackages)),this._wrapCallSite=e.wrapCallSite||!1}static nodeInternals(){return[...Hm]}clean(e,i=0){i=" ".repeat(i),Array.isArray(e)||(e=e.split(`
`)),!/^\s*at /.test(e[0])&&/^\s*at /.test(e[1])&&(e=e.slice(1));let r=!1,n=null,s=[];return e.forEach(o=>{if(o=o.replace(/\\/g,"/"),this._internals.some(l=>l.test(o)))return;let a=/^\s*at /.test(o);r?o=o.trimEnd().replace(/^(\s+)at /,"$1"):(o=o.trim(),a&&(o=o.slice(3))),o=o.replace(`${this._cwd}/`,""),o&&(a?(n&&(s.push(n),n=null),s.push(o)):(r=!0,n=o))}),s.map(o=>`${i}${o}
`).join("")}captureString(e,i=this.captureString){typeof e=="function"&&(i=e,e=1/0);let{stackTraceLimit:r}=Error;e&&(Error.stackTraceLimit=e);let n={};Error.captureStackTrace(n,i);let{stack:s}=n;return Error.stackTraceLimit=r,this.clean(s)}capture(e,i=this.capture){typeof e=="function"&&(i=e,e=1/0);let{prepareStackTrace:r,stackTraceLimit:n}=Error;Error.prepareStackTrace=(a,l)=>this._wrapCallSite?l.map(this._wrapCallSite):l,e&&(Error.stackTraceLimit=e);let s={};Error.captureStackTrace(s,i);let{stack:o}=s;return Object.assign(Error,{prepareStackTrace:r,stackTraceLimit:n}),o}at(e=this.at){let[i]=this.capture(1,e);if(!i)return{};let r={line:i.getLineNumber(),column:i.getColumnNumber()};Vm(r,i.getFileName(),this._cwd),i.isConstructor()&&(r.constructor=!0),i.isEval()&&(r.evalOrigin=i.getEvalOrigin()),i.isNative()&&(r.native=!0);let n;try{n=i.getTypeName()}catch{}n&&n!=="Object"&&n!=="[object Object]"&&(r.type=n);let s=i.getFunctionName();s&&(r.function=s);let o=i.getMethodName();return o&&s!==o&&(r.method=o),r}parseLine(e){let i=e&&e.match(lE);if(!i)return null;let r=i[1]==="new",n=i[2],s=i[3],o=i[4],a=Number(i[5]),l=Number(i[6]),c=i[7],u=i[8],f=i[9],d=i[10]==="native",g=i[11]===")",m,v={};if(u&&(v.line=Number(u)),f&&(v.column=Number(f)),g&&c){let b=0;for(let _=c.length-1;_>0;_--)if(c.charAt(_)===")")b++;else if(c.charAt(_)==="("&&c.charAt(_-1)===" "&&(b--,b===-1&&c.charAt(_-1)===" ")){let S=c.slice(0,_-1);c=c.slice(_+1),n+=` (${S}`;break}}if(n){let b=n.match(cE);b&&(n=b[1],m=b[2])}return Vm(v,c,this._cwd),r&&(v.constructor=!0),s&&(v.evalOrigin=s,v.evalLine=a,v.evalColumn=l,v.evalFile=o&&o.replace(/\\/g,"/")),d&&(v.native=!0),n&&(v.function=n),m&&n!==m&&(v.method=m),v}};function Vm(t,e,i){e&&(e=e.replace(/\\/g,"/"),e.startsWith(`${i}/`)&&(e=e.slice(i.length+1)),t.file=e)}function aE(t){if(t.length===0)return[];let e=t.map(i=>sE(i));return new RegExp(`[/\\\\]node_modules[/\\\\](?:${e.join("|")})[/\\\\][^:]+:\\d+:\\d+`)}var lE=new RegExp("^(?:\\s*at )?(?:(new) )?(?:(.*?) \\()?(?:eval at ([^ ]+) \\((.+?):(\\d+):(\\d+)\\), )?(?:(.+?):(\\d+):(\\d+)|(native))(\\)?)$"),cE=/^(.*?) \[as (.*?)\]$/;Gm.exports=ac});var Se=w(Xe=>{"use strict";var lc=Symbol.for("yaml.alias"),Wm=Symbol.for("yaml.document"),Ks=Symbol.for("yaml.map"),zm=Symbol.for("yaml.pair"),cc=Symbol.for("yaml.scalar"),Js=Symbol.for("yaml.seq"),si=Symbol.for("yaml.node.type"),uE=t=>!!t&&typeof t=="object"&&t[si]===lc,fE=t=>!!t&&typeof t=="object"&&t[si]===Wm,hE=t=>!!t&&typeof t=="object"&&t[si]===Ks,pE=t=>!!t&&typeof t=="object"&&t[si]===zm,Km=t=>!!t&&typeof t=="object"&&t[si]===cc,dE=t=>!!t&&typeof t=="object"&&t[si]===Js;function Jm(t){if(t&&typeof t=="object")switch(t[si]){case Ks:case Js:return!0}return!1}function mE(t){if(t&&typeof t=="object")switch(t[si]){case lc:case Ks:case cc:case Js:return!0}return!1}var gE=t=>(Km(t)||Jm(t))&&!!t.anchor;Xe.ALIAS=lc;Xe.DOC=Wm;Xe.MAP=Ks;Xe.NODE_TYPE=si;Xe.PAIR=zm;Xe.SCALAR=cc;Xe.SEQ=Js;Xe.hasAnchor=gE;Xe.isAlias=uE;Xe.isCollection=Jm;Xe.isDocument=fE;Xe.isMap=hE;Xe.isNode=mE;Xe.isPair=pE;Xe.isScalar=Km;Xe.isSeq=dE});var pn=w(uc=>{"use strict";var He=Se(),mt=Symbol("break visit"),Zm=Symbol("skip children"),zt=Symbol("remove node");function Zs(t,e){let i=Qm(e);He.isDocument(t)?Pr(null,t.contents,i,Object.freeze([t]))===zt&&(t.contents=null):Pr(null,t,i,Object.freeze([]))}Zs.BREAK=mt;Zs.SKIP=Zm;Zs.REMOVE=zt;function Pr(t,e,i,r){let n=Xm(t,e,i,r);if(He.isNode(n)||He.isPair(n))return eg(t,r,n),Pr(t,n,i,r);if(typeof n!="symbol"){if(He.isCollection(e)){r=Object.freeze(r.concat(e));for(let s=0;s<e.items.length;++s){let o=Pr(s,e.items[s],i,r);if(typeof o=="number")s=o-1;else{if(o===mt)return mt;o===zt&&(e.items.splice(s,1),s-=1)}}}else if(He.isPair(e)){r=Object.freeze(r.concat(e));let s=Pr("key",e.key,i,r);if(s===mt)return mt;s===zt&&(e.key=null);let o=Pr("value",e.value,i,r);if(o===mt)return mt;o===zt&&(e.value=null)}}return n}async function Qs(t,e){let i=Qm(e);He.isDocument(t)?await Mr(null,t.contents,i,Object.freeze([t]))===zt&&(t.contents=null):await Mr(null,t,i,Object.freeze([]))}Qs.BREAK=mt;Qs.SKIP=Zm;Qs.REMOVE=zt;async function Mr(t,e,i,r){let n=await Xm(t,e,i,r);if(He.isNode(n)||He.isPair(n))return eg(t,r,n),Mr(t,n,i,r);if(typeof n!="symbol"){if(He.isCollection(e)){r=Object.freeze(r.concat(e));for(let s=0;s<e.items.length;++s){let o=await Mr(s,e.items[s],i,r);if(typeof o=="number")s=o-1;else{if(o===mt)return mt;o===zt&&(e.items.splice(s,1),s-=1)}}}else if(He.isPair(e)){r=Object.freeze(r.concat(e));let s=await Mr("key",e.key,i,r);if(s===mt)return mt;s===zt&&(e.key=null);let o=await Mr("value",e.value,i,r);if(o===mt)return mt;o===zt&&(e.value=null)}}return n}function Qm(t){return typeof t=="object"&&(t.Collection||t.Node||t.Value)?Object.assign({Alias:t.Node,Map:t.Node,Scalar:t.Node,Seq:t.Node},t.Value&&{Map:t.Value,Scalar:t.Value,Seq:t.Value},t.Collection&&{Map:t.Collection,Seq:t.Collection},t):t}function Xm(t,e,i,r){var n,s,o,a,l;if(typeof i=="function")return i(t,e,r);if(He.isMap(e))return(n=i.Map)==null?void 0:n.call(i,t,e,r);if(He.isSeq(e))return(s=i.Seq)==null?void 0:s.call(i,t,e,r);if(He.isPair(e))return(o=i.Pair)==null?void 0:o.call(i,t,e,r);if(He.isScalar(e))return(a=i.Scalar)==null?void 0:a.call(i,t,e,r);if(He.isAlias(e))return(l=i.Alias)==null?void 0:l.call(i,t,e,r)}function eg(t,e,i){let r=e[e.length-1];if(He.isCollection(r))r.items[t]=i;else if(He.isPair(r))t==="key"?r.key=i:r.value=i;else if(He.isDocument(r))r.contents=i;else{let n=He.isAlias(r)?"alias":"scalar";throw new Error(`Cannot replace node with ${n} parent`)}}uc.visit=Zs;uc.visitAsync=Qs});var fc=w(ig=>{"use strict";var tg=Se(),vE=pn(),yE={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},bE=t=>t.replace(/[!,[\]{}]/g,e=>yE[e]),dn=class t{constructor(e,i){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},t.defaultYaml,e),this.tags=Object.assign({},t.defaultTags,i)}clone(){let e=new t(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){let e=new t(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:t.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},t.defaultTags);break}return e}add(e,i){this.atNextDocument&&(this.yaml={explicit:t.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},t.defaultTags),this.atNextDocument=!1);let r=e.trim().split(/[ \t]+/),n=r.shift();switch(n){case"%TAG":{if(r.length!==2&&(i(0,"%TAG directive should contain exactly two parts"),r.length<2))return!1;let[s,o]=r;return this.tags[s]=o,!0}case"%YAML":{if(this.yaml.explicit=!0,r.length!==1)return i(0,"%YAML directive should contain exactly one part"),!1;let[s]=r;if(s==="1.1"||s==="1.2")return this.yaml.version=s,!0;{let o=/^\d+\.\d+$/.test(s);return i(6,`Unsupported YAML version ${s}`,o),!1}}default:return i(0,`Unknown directive ${n}`,!0),!1}}tagName(e,i){if(e==="!")return"!";if(e[0]!=="!")return i(`Not a valid tag: ${e}`),null;if(e[1]==="<"){let o=e.slice(2,-1);return o==="!"||o==="!!"?(i(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(e[e.length-1]!==">"&&i("Verbatim tags must end with a >"),o)}let[,r,n]=e.match(/^(.*!)([^!]*)$/s);n||i(`The ${e} tag has no suffix`);let s=this.tags[r];if(s)try{return s+decodeURIComponent(n)}catch(o){return i(String(o)),null}return r==="!"?e:(i(`Could not resolve tag: ${e}`),null)}tagString(e){for(let[i,r]of Object.entries(this.tags))if(e.startsWith(r))return i+bE(e.substring(r.length));return e[0]==="!"?e:`!<${e}>`}toString(e){let i=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],r=Object.entries(this.tags),n;if(e&&r.length>0&&tg.isNode(e.contents)){let s={};vE.visit(e.contents,(o,a)=>{tg.isNode(a)&&a.tag&&(s[a.tag]=!0)}),n=Object.keys(s)}else n=[];for(let[s,o]of r)s==="!!"&&o==="tag:yaml.org,2002:"||(!e||n.some(a=>a.startsWith(o)))&&i.push(`%TAG ${s} ${o}`);return i.join(`
`)}};dn.defaultYaml={explicit:!1,version:"1.2"};dn.defaultTags={"!!":"tag:yaml.org,2002:"};ig.Directives=dn});var Xs=w(mn=>{"use strict";var rg=Se(),_E=pn();function wE(t){if(/[\x00-\x19\s,[\]{}]/.test(t)){let i=`Anchor must not contain whitespace or control characters: ${JSON.stringify(t)}`;throw new Error(i)}return!0}function ng(t){let e=new Set;return _E.visit(t,{Value(i,r){r.anchor&&e.add(r.anchor)}}),e}function sg(t,e){for(let i=1;;++i){let r=`${t}${i}`;if(!e.has(r))return r}}function xE(t,e){let i=[],r=new Map,n=null;return{onAnchor:s=>{i.push(s),n||(n=ng(t));let o=sg(e,n);return n.add(o),o},setAnchors:()=>{for(let s of i){let o=r.get(s);if(typeof o=="object"&&o.anchor&&(rg.isScalar(o.node)||rg.isCollection(o.node)))o.node.anchor=o.anchor;else{let a=new Error("Failed to resolve repeated object (this should not happen)");throw a.source=s,a}}},sourceObjects:r}}mn.anchorIsValid=wE;mn.anchorNames=ng;mn.createNodeAnchors=xE;mn.findNewAnchor=sg});var hc=w(og=>{"use strict";function gn(t,e,i,r){if(r&&typeof r=="object")if(Array.isArray(r))for(let n=0,s=r.length;n<s;++n){let o=r[n],a=gn(t,r,String(n),o);a===void 0?delete r[n]:a!==o&&(r[n]=a)}else if(r instanceof Map)for(let n of Array.from(r.keys())){let s=r.get(n),o=gn(t,r,n,s);o===void 0?r.delete(n):o!==s&&r.set(n,o)}else if(r instanceof Set)for(let n of Array.from(r)){let s=gn(t,r,n,n);s===void 0?r.delete(n):s!==n&&(r.delete(n),r.add(s))}else for(let[n,s]of Object.entries(r)){let o=gn(t,r,n,s);o===void 0?delete r[n]:o!==s&&(r[n]=o)}return t.call(e,i,r)}og.applyReviver=gn});var vi=w(lg=>{"use strict";var SE=Se();function ag(t,e,i){if(Array.isArray(t))return t.map((r,n)=>ag(r,String(n),i));if(t&&typeof t.toJSON=="function"){if(!i||!SE.hasAnchor(t))return t.toJSON(e,i);let r={aliasCount:0,count:1,res:void 0};i.anchors.set(t,r),i.onCreate=s=>{r.res=s,delete i.onCreate};let n=t.toJSON(e,i);return i.onCreate&&i.onCreate(n),n}return typeof t=="bigint"&&!(i!=null&&i.keep)?Number(t):t}lg.toJS=ag});var eo=w(ug=>{"use strict";var EE=hc(),cg=Se(),kE=vi(),pc=class{constructor(e){Object.defineProperty(this,cg.NODE_TYPE,{value:e})}clone(){let e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:i,maxAliasCount:r,onAnchor:n,reviver:s}={}){if(!cg.isDocument(e))throw new TypeError("A document argument is required");let o={anchors:new Map,doc:e,keep:!0,mapAsMap:i===!0,mapKeyWarned:!1,maxAliasCount:typeof r=="number"?r:100},a=kE.toJS(this,"",o);if(typeof n=="function")for(let{count:l,res:c}of o.anchors.values())n(c,l);return typeof s=="function"?EE.applyReviver(s,{"":a},"",a):a}};ug.NodeBase=pc});var vn=w(hg=>{"use strict";var OE=Xs(),fg=pn(),to=Se(),CE=eo(),TE=vi(),dc=class extends CE.NodeBase{constructor(e){super(to.ALIAS),this.source=e,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(e){let i;return fg.visit(e,{Node:(r,n)=>{if(n===this)return fg.visit.BREAK;n.anchor===this.source&&(i=n)}}),i}toJSON(e,i){if(!i)return{source:this.source};let{anchors:r,doc:n,maxAliasCount:s}=i,o=this.resolve(n);if(!o){let l=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(l)}let a=r.get(o);if(a||(TE.toJS(o,null,i),a=r.get(o)),!a||a.res===void 0){let l="This should not happen: Alias anchor was not resolved?";throw new ReferenceError(l)}if(s>=0&&(a.count+=1,a.aliasCount===0&&(a.aliasCount=io(n,o,r)),a.count*a.aliasCount>s)){let l="Excessive alias count indicates a resource exhaustion attack";throw new ReferenceError(l)}return a.res}toString(e,i,r){let n=`*${this.source}`;if(e){if(OE.anchorIsValid(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source)){let s=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(s)}if(e.implicitKey)return`${n} `}return n}};function io(t,e,i){if(to.isAlias(e)){let r=e.resolve(t),n=i&&r&&i.get(r);return n?n.count*n.aliasCount:0}else if(to.isCollection(e)){let r=0;for(let n of e.items){let s=io(t,n,i);s>r&&(r=s)}return r}else if(to.isPair(e)){let r=io(t,e.key,i),n=io(t,e.value,i);return Math.max(r,n)}return 1}hg.Alias=dc});var je=w(mc=>{"use strict";var AE=Se(),IE=eo(),NE=vi(),LE=t=>!t||typeof t!="function"&&typeof t!="object",yi=class extends IE.NodeBase{constructor(e){super(AE.SCALAR),this.value=e}toJSON(e,i){return i!=null&&i.keep?this.value:NE.toJS(this.value,e,i)}toString(){return String(this.value)}};yi.BLOCK_FOLDED="BLOCK_FOLDED";yi.BLOCK_LITERAL="BLOCK_LITERAL";yi.PLAIN="PLAIN";yi.QUOTE_DOUBLE="QUOTE_DOUBLE";yi.QUOTE_SINGLE="QUOTE_SINGLE";mc.Scalar=yi;mc.isScalarValue=LE});var yn=w(dg=>{"use strict";var BE=vn(),Ji=Se(),pg=je(),RE="tag:yaml.org,2002:";function PE(t,e,i){var r;if(e){let n=i.filter(o=>o.tag===e),s=(r=n.find(o=>!o.format))!=null?r:n[0];if(!s)throw new Error(`Tag ${e} not found`);return s}return i.find(n=>{var s;return((s=n.identify)==null?void 0:s.call(n,t))&&!n.format})}function ME(t,e,i){var f,d,g;if(Ji.isDocument(t)&&(t=t.contents),Ji.isNode(t))return t;if(Ji.isPair(t)){let m=(d=(f=i.schema[Ji.MAP]).createNode)==null?void 0:d.call(f,i.schema,null,i);return m.items.push(t),m}(t instanceof String||t instanceof Number||t instanceof Boolean||typeof BigInt!="undefined"&&t instanceof BigInt)&&(t=t.valueOf());let{aliasDuplicateObjects:r,onAnchor:n,onTagObj:s,schema:o,sourceObjects:a}=i,l;if(r&&t&&typeof t=="object"){if(l=a.get(t),l)return l.anchor||(l.anchor=n(t)),new BE.Alias(l.anchor);l={anchor:null,node:null},a.set(t,l)}e!=null&&e.startsWith("!!")&&(e=RE+e.slice(2));let c=PE(t,e,o.tags);if(!c){if(t&&typeof t.toJSON=="function"&&(t=t.toJSON()),!t||typeof t!="object"){let m=new pg.Scalar(t);return l&&(l.node=m),m}c=t instanceof Map?o[Ji.MAP]:Symbol.iterator in Object(t)?o[Ji.SEQ]:o[Ji.MAP]}s&&(s(c),delete i.onTagObj);let u=c!=null&&c.createNode?c.createNode(i.schema,t,i):typeof((g=c==null?void 0:c.nodeClass)==null?void 0:g.from)=="function"?c.nodeClass.from(i.schema,t,i):new pg.Scalar(t);return e?u.tag=e:c.default||(u.tag=c.tag),l&&(l.node=u),u}dg.createNode=ME});var no=w(ro=>{"use strict";var FE=yn(),Kt=Se(),qE=eo();function gc(t,e,i){let r=i;for(let n=e.length-1;n>=0;--n){let s=e[n];if(typeof s=="number"&&Number.isInteger(s)&&s>=0){let o=[];o[s]=r,r=o}else r=new Map([[s,r]])}return FE.createNode(r,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:t,sourceObjects:new Map})}var mg=t=>t==null||typeof t=="object"&&!!t[Symbol.iterator]().next().done,vc=class extends qE.NodeBase{constructor(e,i){super(e),Object.defineProperty(this,"schema",{value:i,configurable:!0,enumerable:!1,writable:!0})}clone(e){let i=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(i.schema=e),i.items=i.items.map(r=>Kt.isNode(r)||Kt.isPair(r)?r.clone(e):r),this.range&&(i.range=this.range.slice()),i}addIn(e,i){if(mg(e))this.add(i);else{let[r,...n]=e,s=this.get(r,!0);if(Kt.isCollection(s))s.addIn(n,i);else if(s===void 0&&this.schema)this.set(r,gc(this.schema,n,i));else throw new Error(`Expected YAML collection at ${r}. Remaining path: ${n}`)}}deleteIn(e){let[i,...r]=e;if(r.length===0)return this.delete(i);let n=this.get(i,!0);if(Kt.isCollection(n))return n.deleteIn(r);throw new Error(`Expected YAML collection at ${i}. Remaining path: ${r}`)}getIn(e,i){let[r,...n]=e,s=this.get(r,!0);return n.length===0?!i&&Kt.isScalar(s)?s.value:s:Kt.isCollection(s)?s.getIn(n,i):void 0}hasAllNullValues(e){return this.items.every(i=>{if(!Kt.isPair(i))return!1;let r=i.value;return r==null||e&&Kt.isScalar(r)&&r.value==null&&!r.commentBefore&&!r.comment&&!r.tag})}hasIn(e){let[i,...r]=e;if(r.length===0)return this.has(i);let n=this.get(i,!0);return Kt.isCollection(n)?n.hasIn(r):!1}setIn(e,i){let[r,...n]=e;if(n.length===0)this.set(r,i);else{let s=this.get(r,!0);if(Kt.isCollection(s))s.setIn(n,i);else if(s===void 0&&this.schema)this.set(r,gc(this.schema,n,i));else throw new Error(`Expected YAML collection at ${r}. Remaining path: ${n}`)}}};ro.Collection=vc;ro.collectionFromPath=gc;ro.isEmptyPath=mg});var bn=w(so=>{"use strict";var DE=t=>t.replace(/^(?!$)(?: $)?/gm,"#");function yc(t,e){return/^\n+$/.test(t)?t.substring(1):e?t.replace(/^(?! *$)/gm,e):t}var jE=(t,e,i)=>t.endsWith(`
`)?yc(i,e):i.includes(`
`)?`
`+yc(i,e):(t.endsWith(" ")?"":" ")+i;so.indentComment=yc;so.lineComment=jE;so.stringifyComment=DE});var vg=w(_n=>{"use strict";var UE="flow",bc="block",oo="quoted";function $E(t,e,i="flow",{indentAtStart:r,lineWidth:n=80,minContentWidth:s=20,onFold:o,onOverflow:a}={}){if(!n||n<0)return t;n<s&&(s=0);let l=Math.max(1+s,1+n-e.length);if(t.length<=l)return t;let c=[],u={},f=n-e.length;typeof r=="number"&&(r>n-Math.max(2,s)?c.push(0):f=n-r);let d,g,m=!1,v=-1,b=-1,_=-1;i===bc&&(v=gg(t,v,e.length),v!==-1&&(f=v+l));for(let O;O=t[v+=1];){if(i===oo&&O==="\\"){switch(b=v,t[v+1]){case"x":v+=3;break;case"u":v+=5;break;case"U":v+=9;break;default:v+=1}_=v}if(O===`
`)i===bc&&(v=gg(t,v,e.length)),f=v+e.length+l,d=void 0;else{if(O===" "&&g&&g!==" "&&g!==`
`&&g!=="	"){let k=t[v+1];k&&k!==" "&&k!==`
`&&k!=="	"&&(d=v)}if(v>=f)if(d)c.push(d),f=d+l,d=void 0;else if(i===oo){for(;g===" "||g==="	";)g=O,O=t[v+=1],m=!0;let k=v>_+1?v-2:b-1;if(u[k])return t;c.push(k),u[k]=!0,f=k+l,d=void 0}else m=!0}g=O}if(m&&a&&a(),c.length===0)return t;o&&o();let S=t.slice(0,c[0]);for(let O=0;O<c.length;++O){let k=c[O],E=c[O+1]||t.length;k===0?S=`
${e}${t.slice(0,E)}`:(i===oo&&u[k]&&(S+=`${t[k]}\\`),S+=`
${e}${t.slice(k+1,E)}`)}return S}function gg(t,e,i){let r=e,n=e+1,s=t[n];for(;s===" "||s==="	";)if(e<n+i)s=t[++e];else{do s=t[++e];while(s&&s!==`
`);r=e,n=e+1,s=t[n]}return r}_n.FOLD_BLOCK=bc;_n.FOLD_FLOW=UE;_n.FOLD_QUOTED=oo;_n.foldFlowLines=$E});var xn=w(yg=>{"use strict";var Jt=je(),bi=vg(),lo=(t,e)=>({indentAtStart:e?t.indent.length:t.indentAtStart,lineWidth:t.options.lineWidth,minContentWidth:t.options.minContentWidth}),co=t=>/^(%|---|\.\.\.)/m.test(t);function VE(t,e,i){if(!e||e<0)return!1;let r=e-i,n=t.length;if(n<=r)return!1;for(let s=0,o=0;s<n;++s)if(t[s]===`
`){if(s-o>r)return!0;if(o=s+1,n-o<=r)return!1}return!0}function wn(t,e){let i=JSON.stringify(t);if(e.options.doubleQuotedAsJSON)return i;let{implicitKey:r}=e,n=e.options.doubleQuotedMinMultiLineLength,s=e.indent||(co(t)?"  ":""),o="",a=0;for(let l=0,c=i[l];c;c=i[++l])if(c===" "&&i[l+1]==="\\"&&i[l+2]==="n"&&(o+=i.slice(a,l)+"\\ ",l+=1,a=l,c="\\"),c==="\\")switch(i[l+1]){case"u":{o+=i.slice(a,l);let u=i.substr(l+2,4);switch(u){case"0000":o+="\\0";break;case"0007":o+="\\a";break;case"000b":o+="\\v";break;case"001b":o+="\\e";break;case"0085":o+="\\N";break;case"00a0":o+="\\_";break;case"2028":o+="\\L";break;case"2029":o+="\\P";break;default:u.substr(0,2)==="00"?o+="\\x"+u.substr(2):o+=i.substr(l,6)}l+=5,a=l+1}break;case"n":if(r||i[l+2]==='"'||i.length<n)l+=1;else{for(o+=i.slice(a,l)+`

`;i[l+2]==="\\"&&i[l+3]==="n"&&i[l+4]!=='"';)o+=`
`,l+=2;o+=s,i[l+2]===" "&&(o+="\\"),l+=1,a=l+1}break;default:l+=1}return o=a?o+i.slice(a):i,r?o:bi.foldFlowLines(o,s,bi.FOLD_QUOTED,lo(e,!1))}function _c(t,e){if(e.options.singleQuote===!1||e.implicitKey&&t.includes(`
`)||/[ \t]\n|\n[ \t]/.test(t))return wn(t,e);let i=e.indent||(co(t)?"  ":""),r="'"+t.replace(/'/g,"''").replace(/\n+/g,`$&
${i}`)+"'";return e.implicitKey?r:bi.foldFlowLines(r,i,bi.FOLD_FLOW,lo(e,!1))}function Fr(t,e){let{singleQuote:i}=e.options,r;if(i===!1)r=wn;else{let n=t.includes('"'),s=t.includes("'");n&&!s?r=_c:s&&!n?r=wn:r=i?_c:wn}return r(t,e)}var wc;try{wc=new RegExp(`(^|(?<!
))
+(?!
|$)`,"g")}catch{wc=/\n+(?!\n|$)/g}function ao({comment:t,type:e,value:i},r,n,s){let{blockQuote:o,commentString:a,lineWidth:l}=r.options;if(!o||/\n[\t ]+$/.test(i)||/^\s*$/.test(i))return Fr(i,r);let c=r.indent||(r.forceBlockIndent||co(i)?"  ":""),u=o==="literal"?!0:o==="folded"||e===Jt.Scalar.BLOCK_FOLDED?!1:e===Jt.Scalar.BLOCK_LITERAL?!0:!VE(i,l,c.length);if(!i)return u?`|
`:`>
`;let f,d;for(d=i.length;d>0;--d){let R=i[d-1];if(R!==`
`&&R!=="	"&&R!==" ")break}let g=i.substring(d),m=g.indexOf(`
`);m===-1?f="-":i===g||m!==g.length-1?(f="+",s&&s()):f="",g&&(i=i.slice(0,-g.length),g[g.length-1]===`
`&&(g=g.slice(0,-1)),g=g.replace(wc,`$&${c}`));let v=!1,b,_=-1;for(b=0;b<i.length;++b){let R=i[b];if(R===" ")v=!0;else if(R===`
`)_=b;else break}let S=i.substring(0,_<b?_+1:b);S&&(i=i.substring(S.length),S=S.replace(/\n+/g,`$&${c}`));let k=(u?"|":">")+(v?c?"2":"1":"")+f;if(t&&(k+=" "+a(t.replace(/ ?[\r\n]+/g," ")),n&&n()),u)return i=i.replace(/\n+/g,`$&${c}`),`${k}
${c}${S}${i}${g}`;i=i.replace(/\n+/g,`
$&`).replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${c}`);let E=bi.foldFlowLines(`${S}${i}${g}`,c,bi.FOLD_BLOCK,lo(r,!0));return`${k}
${c}${E}`}function HE(t,e,i,r){let{type:n,value:s}=t,{actualString:o,implicitKey:a,indent:l,indentStep:c,inFlow:u}=e;if(a&&s.includes(`
`)||u&&/[[\]{},]/.test(s))return Fr(s,e);if(!s||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(s))return a||u||!s.includes(`
`)?Fr(s,e):ao(t,e,i,r);if(!a&&!u&&n!==Jt.Scalar.PLAIN&&s.includes(`
`))return ao(t,e,i,r);if(co(s)){if(l==="")return e.forceBlockIndent=!0,ao(t,e,i,r);if(a&&l===c)return Fr(s,e)}let f=s.replace(/\n+/g,`$&
${l}`);if(o){let d=v=>{var b;return v.default&&v.tag!=="tag:yaml.org,2002:str"&&((b=v.test)==null?void 0:b.test(f))},{compat:g,tags:m}=e.doc.schema;if(m.some(d)||g!=null&&g.some(d))return Fr(s,e)}return a?f:bi.foldFlowLines(f,l,bi.FOLD_FLOW,lo(e,!1))}function GE(t,e,i,r){let{implicitKey:n,inFlow:s}=e,o=typeof t.value=="string"?t:Object.assign({},t,{value:String(t.value)}),{type:a}=t;a!==Jt.Scalar.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(o.value)&&(a=Jt.Scalar.QUOTE_DOUBLE);let l=u=>{switch(u){case Jt.Scalar.BLOCK_FOLDED:case Jt.Scalar.BLOCK_LITERAL:return n||s?Fr(o.value,e):ao(o,e,i,r);case Jt.Scalar.QUOTE_DOUBLE:return wn(o.value,e);case Jt.Scalar.QUOTE_SINGLE:return _c(o.value,e);case Jt.Scalar.PLAIN:return HE(o,e,i,r);default:return null}},c=l(a);if(c===null){let{defaultKeyType:u,defaultStringType:f}=e.options,d=n&&u||f;if(c=l(d),c===null)throw new Error(`Unsupported default string type ${d}`)}return c}yg.stringifyString=GE});var Sn=w(xc=>{"use strict";var YE=Xs(),_i=Se(),WE=bn(),zE=xn();function KE(t,e){let i=Object.assign({blockQuote:!0,commentString:WE.stringifyComment,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},t.schema.toStringOptions,e),r;switch(i.collectionStyle){case"block":r=!1;break;case"flow":r=!0;break;default:r=null}return{anchors:new Set,doc:t,flowCollectionPadding:i.flowCollectionPadding?" ":"",indent:"",indentStep:typeof i.indent=="number"?" ".repeat(i.indent):"  ",inFlow:r,options:i}}function JE(t,e){var n,s,o,a;if(e.tag){let l=t.filter(c=>c.tag===e.tag);if(l.length>0)return(n=l.find(c=>c.format===e.format))!=null?n:l[0]}let i,r;if(_i.isScalar(e)){r=e.value;let l=t.filter(c=>{var u;return(u=c.identify)==null?void 0:u.call(c,r)});if(l.length>1){let c=l.filter(u=>u.test);c.length>0&&(l=c)}i=(s=l.find(c=>c.format===e.format))!=null?s:l.find(c=>!c.format)}else r=e,i=t.find(l=>l.nodeClass&&r instanceof l.nodeClass);if(!i){let l=(a=(o=r==null?void 0:r.constructor)==null?void 0:o.name)!=null?a:typeof r;throw new Error(`Tag not resolved for ${l} value`)}return i}function ZE(t,e,{anchors:i,doc:r}){if(!r.directives)return"";let n=[],s=(_i.isScalar(t)||_i.isCollection(t))&&t.anchor;s&&YE.anchorIsValid(s)&&(i.add(s),n.push(`&${s}`));let o=t.tag?t.tag:e.default?null:e.tag;return o&&n.push(r.directives.tagString(o)),n.join(" ")}function QE(t,e,i,r){var l,c;if(_i.isPair(t))return t.toString(e,i,r);if(_i.isAlias(t)){if(e.doc.directives)return t.toString(e);if((l=e.resolvedAliases)!=null&&l.has(t))throw new TypeError("Cannot stringify circular structure without alias nodes");e.resolvedAliases?e.resolvedAliases.add(t):e.resolvedAliases=new Set([t]),t=t.resolve(e.doc)}let n,s=_i.isNode(t)?t:e.doc.createNode(t,{onTagObj:u=>n=u});n||(n=JE(e.doc.schema.tags,s));let o=ZE(s,n,e);o.length>0&&(e.indentAtStart=((c=e.indentAtStart)!=null?c:0)+o.length+1);let a=typeof n.stringify=="function"?n.stringify(s,e,i,r):_i.isScalar(s)?zE.stringifyString(s,e,i,r):s.toString(e,i,r);return o?_i.isScalar(s)||a[0]==="{"||a[0]==="["?`${o} ${a}`:`${o}
${e.indent}${a}`:a}xc.createStringifyContext=KE;xc.stringify=QE});var xg=w(wg=>{"use strict";var oi=Se(),bg=je(),_g=Sn(),En=bn();function XE({key:t,value:e},i,r,n){var T,A;let{allNullValues:s,doc:o,indent:a,indentStep:l,options:{commentString:c,indentSeq:u,simpleKeys:f}}=i,d=oi.isNode(t)&&t.comment||null;if(f){if(d)throw new Error("With simple keys, key nodes cannot have comments");if(oi.isCollection(t)||!oi.isNode(t)&&typeof t=="object"){let C="With simple keys, collection cannot be used as a key value";throw new Error(C)}}let g=!f&&(!t||d&&e==null&&!i.inFlow||oi.isCollection(t)||(oi.isScalar(t)?t.type===bg.Scalar.BLOCK_FOLDED||t.type===bg.Scalar.BLOCK_LITERAL:typeof t=="object"));i=Object.assign({},i,{allNullValues:!1,implicitKey:!g&&(f||!s),indent:a+l});let m=!1,v=!1,b=_g.stringify(t,i,()=>m=!0,()=>v=!0);if(!g&&!i.inFlow&&b.length>1024){if(f)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");g=!0}if(i.inFlow){if(s||e==null)return m&&r&&r(),b===""?"?":g?`? ${b}`:b}else if(s&&!f||e==null&&g)return b=`? ${b}`,d&&!m?b+=En.lineComment(b,i.indent,c(d)):v&&n&&n(),b;m&&(d=null),g?(d&&(b+=En.lineComment(b,i.indent,c(d))),b=`? ${b}
${a}:`):(b=`${b}:`,d&&(b+=En.lineComment(b,i.indent,c(d))));let _,S,O;oi.isNode(e)?(_=!!e.spaceBefore,S=e.commentBefore,O=e.comment):(_=!1,S=null,O=null,e&&typeof e=="object"&&(e=o.createNode(e))),i.implicitKey=!1,!g&&!d&&oi.isScalar(e)&&(i.indentAtStart=b.length+1),v=!1,!u&&l.length>=2&&!i.inFlow&&!g&&oi.isSeq(e)&&!e.flow&&!e.tag&&!e.anchor&&(i.indent=i.indent.substring(2));let k=!1,E=_g.stringify(e,i,()=>k=!0,()=>v=!0),R=" ";if(d||_||S){if(R=_?`
`:"",S){let C=c(S);R+=`
${En.indentComment(C,i.indent)}`}E===""&&!i.inFlow?R===`
`&&(R=`

`):R+=`
${i.indent}`}else if(!g&&oi.isCollection(e)){let C=E[0],L=E.indexOf(`
`),P=L!==-1,U=(A=(T=i.inFlow)!=null?T:e.flow)!=null?A:e.items.length===0;if(P||!U){let q=!1;if(P&&(C==="&"||C==="!")){let H=E.indexOf(" ");C==="&"&&H!==-1&&H<L&&E[H+1]==="!"&&(H=E.indexOf(" ",H+1)),(H===-1||L<H)&&(q=!0)}q||(R=`
${i.indent}`)}}else(E===""||E[0]===`
`)&&(R="");return b+=R+E,i.inFlow?k&&r&&r():O&&!k?b+=En.lineComment(b,i.indent,c(O)):v&&n&&n(),b}wg.stringifyPair=XE});var Ec=w(Sc=>{"use strict";function ek(t,...e){t==="debug"&&console.log(...e)}function tk(t,e){(t==="debug"||t==="warn")&&(typeof process!="undefined"&&process.emitWarning?process.emitWarning(e):console.warn(e))}Sc.debug=ek;Sc.warn=tk});var po=w(ho=>{"use strict";var kn=Se(),Sg=je(),uo="<<",fo={identify:t=>t===uo||typeof t=="symbol"&&t.description===uo,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new Sg.Scalar(Symbol(uo)),{addToJSMap:Eg}),stringify:()=>uo},ik=(t,e)=>(fo.identify(e)||kn.isScalar(e)&&(!e.type||e.type===Sg.Scalar.PLAIN)&&fo.identify(e.value))&&(t==null?void 0:t.doc.schema.tags.some(i=>i.tag===fo.tag&&i.default));function Eg(t,e,i){if(i=t&&kn.isAlias(i)?i.resolve(t.doc):i,kn.isSeq(i))for(let r of i.items)kc(t,e,r);else if(Array.isArray(i))for(let r of i)kc(t,e,r);else kc(t,e,i)}function kc(t,e,i){let r=t&&kn.isAlias(i)?i.resolve(t.doc):i;if(!kn.isMap(r))throw new Error("Merge sources must be maps or map aliases");let n=r.toJSON(null,t,Map);for(let[s,o]of n)e instanceof Map?e.has(s)||e.set(s,o):e instanceof Set?e.add(s):Object.prototype.hasOwnProperty.call(e,s)||Object.defineProperty(e,s,{value:o,writable:!0,enumerable:!0,configurable:!0});return e}ho.addMergeToJSMap=Eg;ho.isMergeKey=ik;ho.merge=fo});var Cc=w(Cg=>{"use strict";var rk=Ec(),kg=po(),nk=Sn(),Og=Se(),Oc=vi();function sk(t,e,{key:i,value:r}){if(Og.isNode(i)&&i.addToJSMap)i.addToJSMap(t,e,r);else if(kg.isMergeKey(t,i))kg.addMergeToJSMap(t,e,r);else{let n=Oc.toJS(i,"",t);if(e instanceof Map)e.set(n,Oc.toJS(r,n,t));else if(e instanceof Set)e.add(n);else{let s=ok(i,n,t),o=Oc.toJS(r,s,t);s in e?Object.defineProperty(e,s,{value:o,writable:!0,enumerable:!0,configurable:!0}):e[s]=o}}return e}function ok(t,e,i){if(e===null)return"";if(typeof e!="object")return String(e);if(Og.isNode(t)&&(i!=null&&i.doc)){let r=nk.createStringifyContext(i.doc,{});r.anchors=new Set;for(let s of i.anchors.keys())r.anchors.add(s.anchor);r.inFlow=!0,r.inStringifyKey=!0;let n=t.toString(r);if(!i.mapKeyWarned){let s=JSON.stringify(n);s.length>40&&(s=s.substring(0,36)+'..."'),rk.warn(i.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${s}. Set mapAsMap: true to use object keys.`),i.mapKeyWarned=!0}return n}return JSON.stringify(e)}Cg.addPairToJSMap=sk});var wi=w(Tc=>{"use strict";var Tg=yn(),ak=xg(),lk=Cc(),mo=Se();function ck(t,e,i){let r=Tg.createNode(t,void 0,i),n=Tg.createNode(e,void 0,i);return new go(r,n)}var go=class t{constructor(e,i=null){Object.defineProperty(this,mo.NODE_TYPE,{value:mo.PAIR}),this.key=e,this.value=i}clone(e){let{key:i,value:r}=this;return mo.isNode(i)&&(i=i.clone(e)),mo.isNode(r)&&(r=r.clone(e)),new t(i,r)}toJSON(e,i){let r=i!=null&&i.mapAsMap?new Map:{};return lk.addPairToJSMap(i,r,this)}toString(e,i,r){return e!=null&&e.doc?ak.stringifyPair(this,e,i,r):JSON.stringify(this)}};Tc.Pair=go;Tc.createPair=ck});var Ac=w(Ig=>{"use strict";var Zi=Se(),Ag=Sn(),vo=bn();function uk(t,e,i){var s;return(((s=e.inFlow)!=null?s:t.flow)?hk:fk)(t,e,i)}function fk({comment:t,items:e},i,{blockItemPrefix:r,flowChars:n,itemIndent:s,onChompKeep:o,onComment:a}){let{indent:l,options:{commentString:c}}=i,u=Object.assign({},i,{indent:s,type:null}),f=!1,d=[];for(let m=0;m<e.length;++m){let v=e[m],b=null;if(Zi.isNode(v))!f&&v.spaceBefore&&d.push(""),yo(i,d,v.commentBefore,f),v.comment&&(b=v.comment);else if(Zi.isPair(v)){let S=Zi.isNode(v.key)?v.key:null;S&&(!f&&S.spaceBefore&&d.push(""),yo(i,d,S.commentBefore,f))}f=!1;let _=Ag.stringify(v,u,()=>b=null,()=>f=!0);b&&(_+=vo.lineComment(_,s,c(b))),f&&b&&(f=!1),d.push(r+_)}let g;if(d.length===0)g=n.start+n.end;else{g=d[0];for(let m=1;m<d.length;++m){let v=d[m];g+=v?`
${l}${v}`:`
`}}return t?(g+=`
`+vo.indentComment(c(t),l),a&&a()):f&&o&&o(),g}function hk({items:t},e,{flowChars:i,itemIndent:r}){let{indent:n,indentStep:s,flowCollectionPadding:o,options:{commentString:a}}=e;r+=s;let l=Object.assign({},e,{indent:r,inFlow:!0,type:null}),c=!1,u=0,f=[];for(let m=0;m<t.length;++m){let v=t[m],b=null;if(Zi.isNode(v))v.spaceBefore&&f.push(""),yo(e,f,v.commentBefore,!1),v.comment&&(b=v.comment);else if(Zi.isPair(v)){let S=Zi.isNode(v.key)?v.key:null;S&&(S.spaceBefore&&f.push(""),yo(e,f,S.commentBefore,!1),S.comment&&(c=!0));let O=Zi.isNode(v.value)?v.value:null;O?(O.comment&&(b=O.comment),O.commentBefore&&(c=!0)):v.value==null&&(S!=null&&S.comment)&&(b=S.comment)}b&&(c=!0);let _=Ag.stringify(v,l,()=>b=null);m<t.length-1&&(_+=","),b&&(_+=vo.lineComment(_,r,a(b))),!c&&(f.length>u||_.includes(`
`))&&(c=!0),f.push(_),u=f.length}let{start:d,end:g}=i;if(f.length===0)return d+g;if(!c){let m=f.reduce((v,b)=>v+b.length+2,2);c=e.options.lineWidth>0&&m>e.options.lineWidth}if(c){let m=d;for(let v of f)m+=v?`
${s}${n}${v}`:`
`;return`${m}
${n}${g}`}else return`${d}${o}${f.join(" ")}${o}${g}`}function yo({indent:t,options:{commentString:e}},i,r,n){if(r&&n&&(r=r.replace(/^\n+/,"")),r){let s=vo.indentComment(e(r),t);i.push(s.trimStart())}}Ig.stringifyCollection=uk});var Si=w(Nc=>{"use strict";var pk=Ac(),dk=Cc(),mk=no(),xi=Se(),bo=wi(),gk=je();function On(t,e){let i=xi.isScalar(e)?e.value:e;for(let r of t)if(xi.isPair(r)&&(r.key===e||r.key===i||xi.isScalar(r.key)&&r.key.value===i))return r}var Ic=class extends mk.Collection{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(xi.MAP,e),this.items=[]}static from(e,i,r){let{keepUndefined:n,replacer:s}=r,o=new this(e),a=(l,c)=>{if(typeof s=="function")c=s.call(i,l,c);else if(Array.isArray(s)&&!s.includes(l))return;(c!==void 0||n)&&o.items.push(bo.createPair(l,c,r))};if(i instanceof Map)for(let[l,c]of i)a(l,c);else if(i&&typeof i=="object")for(let l of Object.keys(i))a(l,i[l]);return typeof e.sortMapEntries=="function"&&o.items.sort(e.sortMapEntries),o}add(e,i){var o;let r;xi.isPair(e)?r=e:!e||typeof e!="object"||!("key"in e)?r=new bo.Pair(e,e==null?void 0:e.value):r=new bo.Pair(e.key,e.value);let n=On(this.items,r.key),s=(o=this.schema)==null?void 0:o.sortMapEntries;if(n){if(!i)throw new Error(`Key ${r.key} already set`);xi.isScalar(n.value)&&gk.isScalarValue(r.value)?n.value.value=r.value:n.value=r.value}else if(s){let a=this.items.findIndex(l=>s(r,l)<0);a===-1?this.items.push(r):this.items.splice(a,0,r)}else this.items.push(r)}delete(e){let i=On(this.items,e);return i?this.items.splice(this.items.indexOf(i),1).length>0:!1}get(e,i){var s;let r=On(this.items,e),n=r==null?void 0:r.value;return(s=!i&&xi.isScalar(n)?n.value:n)!=null?s:void 0}has(e){return!!On(this.items,e)}set(e,i){this.add(new bo.Pair(e,i),!0)}toJSON(e,i,r){let n=r?new r:i!=null&&i.mapAsMap?new Map:{};i!=null&&i.onCreate&&i.onCreate(n);for(let s of this.items)dk.addPairToJSMap(i,n,s);return n}toString(e,i,r){if(!e)return JSON.stringify(this);for(let n of this.items)if(!xi.isPair(n))throw new Error(`Map items must all be pairs; found ${JSON.stringify(n)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),pk.stringifyCollection(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:r,onComment:i})}};Nc.YAMLMap=Ic;Nc.findPair=On});var qr=w(Lg=>{"use strict";var vk=Se(),Ng=Si(),yk={collection:"map",default:!0,nodeClass:Ng.YAMLMap,tag:"tag:yaml.org,2002:map",resolve(t,e){return vk.isMap(t)||e("Expected a mapping for this tag"),t},createNode:(t,e,i)=>Ng.YAMLMap.from(t,e,i)};Lg.map=yk});var Ei=w(Bg=>{"use strict";var bk=yn(),_k=Ac(),wk=no(),wo=Se(),xk=je(),Sk=vi(),Lc=class extends wk.Collection{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(wo.SEQ,e),this.items=[]}add(e){this.items.push(e)}delete(e){let i=_o(e);return typeof i!="number"?!1:this.items.splice(i,1).length>0}get(e,i){let r=_o(e);if(typeof r!="number")return;let n=this.items[r];return!i&&wo.isScalar(n)?n.value:n}has(e){let i=_o(e);return typeof i=="number"&&i<this.items.length}set(e,i){let r=_o(e);if(typeof r!="number")throw new Error(`Expected a valid index, not ${e}.`);let n=this.items[r];wo.isScalar(n)&&xk.isScalarValue(i)?n.value=i:this.items[r]=i}toJSON(e,i){let r=[];i!=null&&i.onCreate&&i.onCreate(r);let n=0;for(let s of this.items)r.push(Sk.toJS(s,String(n++),i));return r}toString(e,i,r){return e?_k.stringifyCollection(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:r,onComment:i}):JSON.stringify(this)}static from(e,i,r){let{replacer:n}=r,s=new this(e);if(i&&Symbol.iterator in Object(i)){let o=0;for(let a of i){if(typeof n=="function"){let l=i instanceof Set?a:String(o++);a=n.call(i,l,a)}s.items.push(bk.createNode(a,void 0,r))}}return s}};function _o(t){let e=wo.isScalar(t)?t.value:t;return e&&typeof e=="string"&&(e=Number(e)),typeof e=="number"&&Number.isInteger(e)&&e>=0?e:null}Bg.YAMLSeq=Lc});var Dr=w(Pg=>{"use strict";var Ek=Se(),Rg=Ei(),kk={collection:"seq",default:!0,nodeClass:Rg.YAMLSeq,tag:"tag:yaml.org,2002:seq",resolve(t,e){return Ek.isSeq(t)||e("Expected a sequence for this tag"),t},createNode:(t,e,i)=>Rg.YAMLSeq.from(t,e,i)};Pg.seq=kk});var Cn=w(Mg=>{"use strict";var Ok=xn(),Ck={identify:t=>typeof t=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:t=>t,stringify(t,e,i,r){return e=Object.assign({actualString:!0},e),Ok.stringifyString(t,e,i,r)}};Mg.string=Ck});var xo=w(Dg=>{"use strict";var Fg=je(),qg={identify:t=>t==null,createNode:()=>new Fg.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new Fg.Scalar(null),stringify:({source:t},e)=>typeof t=="string"&&qg.test.test(t)?t:e.options.nullStr};Dg.nullTag=qg});var Bc=w(Ug=>{"use strict";var Tk=je(),jg={identify:t=>typeof t=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:t=>new Tk.Scalar(t[0]==="t"||t[0]==="T"),stringify({source:t,value:e},i){if(t&&jg.test.test(t)){let r=t[0]==="t"||t[0]==="T";if(e===r)return t}return e?i.options.trueStr:i.options.falseStr}};Ug.boolTag=jg});var jr=w($g=>{"use strict";function Ak({format:t,minFractionDigits:e,tag:i,value:r}){if(typeof r=="bigint")return String(r);let n=typeof r=="number"?r:Number(r);if(!isFinite(n))return isNaN(n)?".nan":n<0?"-.inf":".inf";let s=JSON.stringify(r);if(!t&&e&&(!i||i==="tag:yaml.org,2002:float")&&/^\d/.test(s)){let o=s.indexOf(".");o<0&&(o=s.length,s+=".");let a=e-(s.length-o-1);for(;a-- >0;)s+="0"}return s}$g.stringifyNumber=Ak});var Pc=w(So=>{"use strict";var Ik=je(),Rc=jr(),Nk={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:t=>t.slice(-3).toLowerCase()==="nan"?NaN:t[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Rc.stringifyNumber},Lk={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:t=>parseFloat(t),stringify(t){let e=Number(t.value);return isFinite(e)?e.toExponential():Rc.stringifyNumber(t)}},Bk={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(t){let e=new Ik.Scalar(parseFloat(t)),i=t.indexOf(".");return i!==-1&&t[t.length-1]==="0"&&(e.minFractionDigits=t.length-i-1),e},stringify:Rc.stringifyNumber};So.float=Bk;So.floatExp=Lk;So.floatNaN=Nk});var Fc=w(ko=>{"use strict";var Vg=jr(),Eo=t=>typeof t=="bigint"||Number.isInteger(t),Mc=(t,e,i,{intAsBigInt:r})=>r?BigInt(t):parseInt(t.substring(e),i);function Hg(t,e,i){let{value:r}=t;return Eo(r)&&r>=0?i+r.toString(e):Vg.stringifyNumber(t)}var Rk={identify:t=>Eo(t)&&t>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(t,e,i)=>Mc(t,2,8,i),stringify:t=>Hg(t,8,"0o")},Pk={identify:Eo,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(t,e,i)=>Mc(t,0,10,i),stringify:Vg.stringifyNumber},Mk={identify:t=>Eo(t)&&t>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(t,e,i)=>Mc(t,2,16,i),stringify:t=>Hg(t,16,"0x")};ko.int=Pk;ko.intHex=Mk;ko.intOct=Rk});var Yg=w(Gg=>{"use strict";var Fk=qr(),qk=xo(),Dk=Dr(),jk=Cn(),Uk=Bc(),qc=Pc(),Dc=Fc(),$k=[Fk.map,Dk.seq,jk.string,qk.nullTag,Uk.boolTag,Dc.intOct,Dc.int,Dc.intHex,qc.floatNaN,qc.floatExp,qc.float];Gg.schema=$k});var Kg=w(zg=>{"use strict";var Vk=je(),Hk=qr(),Gk=Dr();function Wg(t){return typeof t=="bigint"||Number.isInteger(t)}var Oo=({value:t})=>JSON.stringify(t),Yk=[{identify:t=>typeof t=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:t=>t,stringify:Oo},{identify:t=>t==null,createNode:()=>new Vk.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:Oo},{identify:t=>typeof t=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true|false$/,resolve:t=>t==="true",stringify:Oo},{identify:Wg,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(t,e,{intAsBigInt:i})=>i?BigInt(t):parseInt(t,10),stringify:({value:t})=>Wg(t)?t.toString():JSON.stringify(t)},{identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:t=>parseFloat(t),stringify:Oo}],Wk={default:!0,tag:"",test:/^/,resolve(t,e){return e(`Unresolved plain scalar ${JSON.stringify(t)}`),t}},zk=[Hk.map,Gk.seq].concat(Yk,Wk);zg.schema=zk});var Uc=w(Jg=>{"use strict";var jc=je(),Kk=xn(),Jk={identify:t=>t instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(t,e){if(typeof Buffer=="function")return Buffer.from(t,"base64");if(typeof atob=="function"){let i=atob(t.replace(/[\n\r]/g,"")),r=new Uint8Array(i.length);for(let n=0;n<i.length;++n)r[n]=i.charCodeAt(n);return r}else return e("This environment does not support reading binary tags; either Buffer or atob is required"),t},stringify({comment:t,type:e,value:i},r,n,s){let o=i,a;if(typeof Buffer=="function")a=o instanceof Buffer?o.toString("base64"):Buffer.from(o.buffer).toString("base64");else if(typeof btoa=="function"){let l="";for(let c=0;c<o.length;++c)l+=String.fromCharCode(o[c]);a=btoa(l)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(e||(e=jc.Scalar.BLOCK_LITERAL),e!==jc.Scalar.QUOTE_DOUBLE){let l=Math.max(r.options.lineWidth-r.indent.length,r.options.minContentWidth),c=Math.ceil(a.length/l),u=new Array(c);for(let f=0,d=0;f<c;++f,d+=l)u[f]=a.substr(d,l);a=u.join(e===jc.Scalar.BLOCK_LITERAL?`
`:" ")}return Kk.stringifyString({comment:t,type:e,value:a},r,n,s)}};Jg.binary=Jk});var Ao=w(To=>{"use strict";var Co=Se(),$c=wi(),Zk=je(),Qk=Ei();function Zg(t,e){var i;if(Co.isSeq(t))for(let r=0;r<t.items.length;++r){let n=t.items[r];if(!Co.isPair(n)){if(Co.isMap(n)){n.items.length>1&&e("Each pair must have its own sequence indicator");let s=n.items[0]||new $c.Pair(new Zk.Scalar(null));if(n.commentBefore&&(s.key.commentBefore=s.key.commentBefore?`${n.commentBefore}
${s.key.commentBefore}`:n.commentBefore),n.comment){let o=(i=s.value)!=null?i:s.key;o.comment=o.comment?`${n.comment}
${o.comment}`:n.comment}n=s}t.items[r]=Co.isPair(n)?n:new $c.Pair(n)}}else e("Expected a sequence for this tag");return t}function Qg(t,e,i){let{replacer:r}=i,n=new Qk.YAMLSeq(t);n.tag="tag:yaml.org,2002:pairs";let s=0;if(e&&Symbol.iterator in Object(e))for(let o of e){typeof r=="function"&&(o=r.call(e,String(s++),o));let a,l;if(Array.isArray(o))if(o.length===2)a=o[0],l=o[1];else throw new TypeError(`Expected [key, value] tuple: ${o}`);else if(o&&o instanceof Object){let c=Object.keys(o);if(c.length===1)a=c[0],l=o[a];else throw new TypeError(`Expected tuple with one key, not ${c.length} keys`)}else a=o;n.items.push($c.createPair(a,l,i))}return n}var Xk={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:Zg,createNode:Qg};To.createPairs=Qg;To.pairs=Xk;To.resolvePairs=Zg});var Gc=w(Hc=>{"use strict";var Xg=Se(),Vc=vi(),Tn=Si(),eO=Ei(),e0=Ao(),Qi=class t extends eO.YAMLSeq{constructor(){super(),this.add=Tn.YAMLMap.prototype.add.bind(this),this.delete=Tn.YAMLMap.prototype.delete.bind(this),this.get=Tn.YAMLMap.prototype.get.bind(this),this.has=Tn.YAMLMap.prototype.has.bind(this),this.set=Tn.YAMLMap.prototype.set.bind(this),this.tag=t.tag}toJSON(e,i){if(!i)return super.toJSON(e);let r=new Map;i!=null&&i.onCreate&&i.onCreate(r);for(let n of this.items){let s,o;if(Xg.isPair(n)?(s=Vc.toJS(n.key,"",i),o=Vc.toJS(n.value,s,i)):s=Vc.toJS(n,"",i),r.has(s))throw new Error("Ordered maps must not include duplicate keys");r.set(s,o)}return r}static from(e,i,r){let n=e0.createPairs(e,i,r),s=new this;return s.items=n.items,s}};Qi.tag="tag:yaml.org,2002:omap";var tO={collection:"seq",identify:t=>t instanceof Map,nodeClass:Qi,default:!1,tag:"tag:yaml.org,2002:omap",resolve(t,e){let i=e0.resolvePairs(t,e),r=[];for(let{key:n}of i.items)Xg.isScalar(n)&&(r.includes(n.value)?e(`Ordered maps must not include duplicate keys: ${n.value}`):r.push(n.value));return Object.assign(new Qi,i)},createNode:(t,e,i)=>Qi.from(t,e,i)};Hc.YAMLOMap=Qi;Hc.omap=tO});var s0=w(Yc=>{"use strict";var t0=je();function i0({value:t,source:e},i){return e&&(t?r0:n0).test.test(e)?e:t?i.options.trueStr:i.options.falseStr}var r0={identify:t=>t===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new t0.Scalar(!0),stringify:i0},n0={identify:t=>t===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new t0.Scalar(!1),stringify:i0};Yc.falseTag=n0;Yc.trueTag=r0});var o0=w(Io=>{"use strict";var iO=je(),Wc=jr(),rO={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:t=>t.slice(-3).toLowerCase()==="nan"?NaN:t[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Wc.stringifyNumber},nO={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:t=>parseFloat(t.replace(/_/g,"")),stringify(t){let e=Number(t.value);return isFinite(e)?e.toExponential():Wc.stringifyNumber(t)}},sO={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(t){let e=new iO.Scalar(parseFloat(t.replace(/_/g,""))),i=t.indexOf(".");if(i!==-1){let r=t.substring(i+1).replace(/_/g,"");r[r.length-1]==="0"&&(e.minFractionDigits=r.length)}return e},stringify:Wc.stringifyNumber};Io.float=sO;Io.floatExp=nO;Io.floatNaN=rO});var l0=w(In=>{"use strict";var a0=jr(),An=t=>typeof t=="bigint"||Number.isInteger(t);function No(t,e,i,{intAsBigInt:r}){let n=t[0];if((n==="-"||n==="+")&&(e+=1),t=t.substring(e).replace(/_/g,""),r){switch(i){case 2:t=`0b${t}`;break;case 8:t=`0o${t}`;break;case 16:t=`0x${t}`;break}let o=BigInt(t);return n==="-"?BigInt(-1)*o:o}let s=parseInt(t,i);return n==="-"?-1*s:s}function zc(t,e,i){let{value:r}=t;if(An(r)){let n=r.toString(e);return r<0?"-"+i+n.substr(1):i+n}return a0.stringifyNumber(t)}var oO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(t,e,i)=>No(t,2,2,i),stringify:t=>zc(t,2,"0b")},aO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(t,e,i)=>No(t,1,8,i),stringify:t=>zc(t,8,"0")},lO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(t,e,i)=>No(t,0,10,i),stringify:a0.stringifyNumber},cO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(t,e,i)=>No(t,2,16,i),stringify:t=>zc(t,16,"0x")};In.int=lO;In.intBin=oO;In.intHex=cO;In.intOct=aO});var Jc=w(Kc=>{"use strict";var Ro=Se(),Lo=wi(),Bo=Si(),Xi=class t extends Bo.YAMLMap{constructor(e){super(e),this.tag=t.tag}add(e){let i;Ro.isPair(e)?i=e:e&&typeof e=="object"&&"key"in e&&"value"in e&&e.value===null?i=new Lo.Pair(e.key,null):i=new Lo.Pair(e,null),Bo.findPair(this.items,i.key)||this.items.push(i)}get(e,i){let r=Bo.findPair(this.items,e);return!i&&Ro.isPair(r)?Ro.isScalar(r.key)?r.key.value:r.key:r}set(e,i){if(typeof i!="boolean")throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof i}`);let r=Bo.findPair(this.items,e);r&&!i?this.items.splice(this.items.indexOf(r),1):!r&&i&&this.items.push(new Lo.Pair(e))}toJSON(e,i){return super.toJSON(e,i,Set)}toString(e,i,r){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),i,r);throw new Error("Set items must all have null values")}static from(e,i,r){let{replacer:n}=r,s=new this(e);if(i&&Symbol.iterator in Object(i))for(let o of i)typeof n=="function"&&(o=n.call(i,o,o)),s.items.push(Lo.createPair(o,null,r));return s}};Xi.tag="tag:yaml.org,2002:set";var uO={collection:"map",identify:t=>t instanceof Set,nodeClass:Xi,default:!1,tag:"tag:yaml.org,2002:set",createNode:(t,e,i)=>Xi.from(t,e,i),resolve(t,e){if(Ro.isMap(t)){if(t.hasAllNullValues(!0))return Object.assign(new Xi,t);e("Set items must all have null values")}else e("Expected a mapping for this tag");return t}};Kc.YAMLSet=Xi;Kc.set=uO});var Qc=w(Po=>{"use strict";var fO=jr();function Zc(t,e){let i=t[0],r=i==="-"||i==="+"?t.substring(1):t,n=o=>e?BigInt(o):Number(o),s=r.replace(/_/g,"").split(":").reduce((o,a)=>o*n(60)+n(a),n(0));return i==="-"?n(-1)*s:s}function c0(t){let{value:e}=t,i=o=>o;if(typeof e=="bigint")i=o=>BigInt(o);else if(isNaN(e)||!isFinite(e))return fO.stringifyNumber(t);let r="";e<0&&(r="-",e*=i(-1));let n=i(60),s=[e%n];return e<60?s.unshift(0):(e=(e-s[0])/n,s.unshift(e%n),e>=60&&(e=(e-s[0])/n,s.unshift(e))),r+s.map(o=>String(o).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}var hO={identify:t=>typeof t=="bigint"||Number.isInteger(t),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(t,e,{intAsBigInt:i})=>Zc(t,i),stringify:c0},pO={identify:t=>typeof t=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:t=>Zc(t,!1),stringify:c0},u0={identify:t=>t instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(t){let e=t.match(u0.test);if(!e)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");let[,i,r,n,s,o,a]=e.map(Number),l=e[7]?Number((e[7]+"00").substr(1,3)):0,c=Date.UTC(i,r-1,n,s||0,o||0,a||0,l),u=e[8];if(u&&u!=="Z"){let f=Zc(u,!1);Math.abs(f)<30&&(f*=60),c-=6e4*f}return new Date(c)},stringify:({value:t})=>t.toISOString().replace(/((T00:00)?:00)?\.000Z$/,"")};Po.floatTime=pO;Po.intTime=hO;Po.timestamp=u0});var p0=w(h0=>{"use strict";var dO=qr(),mO=xo(),gO=Dr(),vO=Cn(),yO=Uc(),f0=s0(),Xc=o0(),Mo=l0(),bO=po(),_O=Gc(),wO=Ao(),xO=Jc(),eu=Qc(),SO=[dO.map,gO.seq,vO.string,mO.nullTag,f0.trueTag,f0.falseTag,Mo.intBin,Mo.intOct,Mo.int,Mo.intHex,Xc.floatNaN,Xc.floatExp,Xc.float,yO.binary,bO.merge,_O.omap,wO.pairs,xO.set,eu.intTime,eu.floatTime,eu.timestamp];h0.schema=SO});var S0=w(ru=>{"use strict";var v0=qr(),EO=xo(),y0=Dr(),kO=Cn(),OO=Bc(),tu=Pc(),iu=Fc(),CO=Yg(),TO=Kg(),b0=Uc(),Nn=po(),_0=Gc(),w0=Ao(),d0=p0(),x0=Jc(),Fo=Qc(),m0=new Map([["core",CO.schema],["failsafe",[v0.map,y0.seq,kO.string]],["json",TO.schema],["yaml11",d0.schema],["yaml-1.1",d0.schema]]),g0={binary:b0.binary,bool:OO.boolTag,float:tu.float,floatExp:tu.floatExp,floatNaN:tu.floatNaN,floatTime:Fo.floatTime,int:iu.int,intHex:iu.intHex,intOct:iu.intOct,intTime:Fo.intTime,map:v0.map,merge:Nn.merge,null:EO.nullTag,omap:_0.omap,pairs:w0.pairs,seq:y0.seq,set:x0.set,timestamp:Fo.timestamp},AO={"tag:yaml.org,2002:binary":b0.binary,"tag:yaml.org,2002:merge":Nn.merge,"tag:yaml.org,2002:omap":_0.omap,"tag:yaml.org,2002:pairs":w0.pairs,"tag:yaml.org,2002:set":x0.set,"tag:yaml.org,2002:timestamp":Fo.timestamp};function IO(t,e,i){let r=m0.get(e);if(r&&!t)return i&&!r.includes(Nn.merge)?r.concat(Nn.merge):r.slice();let n=r;if(!n)if(Array.isArray(t))n=[];else{let s=Array.from(m0.keys()).filter(o=>o!=="yaml11").map(o=>JSON.stringify(o)).join(", ");throw new Error(`Unknown schema "${e}"; use one of ${s} or define customTags array`)}if(Array.isArray(t))for(let s of t)n=n.concat(s);else typeof t=="function"&&(n=t(n.slice()));return i&&(n=n.concat(Nn.merge)),n.reduce((s,o)=>{let a=typeof o=="string"?g0[o]:o;if(!a){let l=JSON.stringify(o),c=Object.keys(g0).map(u=>JSON.stringify(u)).join(", ");throw new Error(`Unknown custom tag ${l}; use one of ${c}`)}return s.includes(a)||s.push(a),s},[])}ru.coreKnownTags=AO;ru.getTags=IO});var ou=w(E0=>{"use strict";var nu=Se(),NO=qr(),LO=Dr(),BO=Cn(),qo=S0(),RO=(t,e)=>t.key<e.key?-1:t.key>e.key?1:0,su=class t{constructor({compat:e,customTags:i,merge:r,resolveKnownTags:n,schema:s,sortMapEntries:o,toStringDefaults:a}){this.compat=Array.isArray(e)?qo.getTags(e,"compat"):e?qo.getTags(null,e):null,this.name=typeof s=="string"&&s||"core",this.knownTags=n?qo.coreKnownTags:{},this.tags=qo.getTags(i,this.name,r),this.toStringOptions=a!=null?a:null,Object.defineProperty(this,nu.MAP,{value:NO.map}),Object.defineProperty(this,nu.SCALAR,{value:BO.string}),Object.defineProperty(this,nu.SEQ,{value:LO.seq}),this.sortMapEntries=typeof o=="function"?o:o===!0?RO:null}clone(){let e=Object.create(t.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}};E0.Schema=su});var O0=w(k0=>{"use strict";var PO=Se(),au=Sn(),Ln=bn();function MO(t,e){var l;let i=[],r=e.directives===!0;if(e.directives!==!1&&t.directives){let c=t.directives.toString(t);c?(i.push(c),r=!0):t.directives.docStart&&(r=!0)}r&&i.push("---");let n=au.createStringifyContext(t,e),{commentString:s}=n.options;if(t.commentBefore){i.length!==1&&i.unshift("");let c=s(t.commentBefore);i.unshift(Ln.indentComment(c,""))}let o=!1,a=null;if(t.contents){if(PO.isNode(t.contents)){if(t.contents.spaceBefore&&r&&i.push(""),t.contents.commentBefore){let f=s(t.contents.commentBefore);i.push(Ln.indentComment(f,""))}n.forceBlockIndent=!!t.comment,a=t.contents.comment}let c=a?void 0:()=>o=!0,u=au.stringify(t.contents,n,()=>a=null,c);a&&(u+=Ln.lineComment(u,"",s(a))),(u[0]==="|"||u[0]===">")&&i[i.length-1]==="---"?i[i.length-1]=`--- ${u}`:i.push(u)}else i.push(au.stringify(t.contents,n));if((l=t.directives)!=null&&l.docEnd)if(t.comment){let c=s(t.comment);c.includes(`
`)?(i.push("..."),i.push(Ln.indentComment(c,""))):i.push(`... ${c}`)}else i.push("...");else{let c=t.comment;c&&o&&(c=c.replace(/^\n+/,"")),c&&((!o||a)&&i[i.length-1]!==""&&i.push(""),i.push(Ln.indentComment(s(c),"")))}return i.join(`
`)+`
`}k0.stringifyDocument=MO});var Bn=w(C0=>{"use strict";var FO=vn(),Ur=no(),At=Se(),qO=wi(),DO=vi(),jO=ou(),UO=O0(),lu=Xs(),$O=hc(),VO=yn(),cu=fc(),uu=class t{constructor(e,i,r){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,At.NODE_TYPE,{value:At.DOC});let n=null;typeof i=="function"||Array.isArray(i)?n=i:r===void 0&&i&&(r=i,i=void 0);let s=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},r);this.options=s;let{version:o}=s;r!=null&&r._directives?(this.directives=r._directives.atDocument(),this.directives.yaml.explicit&&(o=this.directives.yaml.version)):this.directives=new cu.Directives({version:o}),this.setSchema(o,r),this.contents=e===void 0?null:this.createNode(e,n,r)}clone(){let e=Object.create(t.prototype,{[At.NODE_TYPE]:{value:At.DOC}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=At.isNode(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){$r(this.contents)&&this.contents.add(e)}addIn(e,i){$r(this.contents)&&this.contents.addIn(e,i)}createAlias(e,i){if(!e.anchor){let r=lu.anchorNames(this);e.anchor=!i||r.has(i)?lu.findNewAnchor(i||"a",r):i}return new FO.Alias(e.anchor)}createNode(e,i,r){let n;if(typeof i=="function")e=i.call({"":e},"",e),n=i;else if(Array.isArray(i)){let b=S=>typeof S=="number"||S instanceof String||S instanceof Number,_=i.filter(b).map(String);_.length>0&&(i=i.concat(_)),n=i}else r===void 0&&i&&(r=i,i=void 0);let{aliasDuplicateObjects:s,anchorPrefix:o,flow:a,keepUndefined:l,onTagObj:c,tag:u}=r!=null?r:{},{onAnchor:f,setAnchors:d,sourceObjects:g}=lu.createNodeAnchors(this,o||"a"),m={aliasDuplicateObjects:s!=null?s:!0,keepUndefined:l!=null?l:!1,onAnchor:f,onTagObj:c,replacer:n,schema:this.schema,sourceObjects:g},v=VO.createNode(e,u,m);return a&&At.isCollection(v)&&(v.flow=!0),d(),v}createPair(e,i,r={}){let n=this.createNode(e,null,r),s=this.createNode(i,null,r);return new qO.Pair(n,s)}delete(e){return $r(this.contents)?this.contents.delete(e):!1}deleteIn(e){return Ur.isEmptyPath(e)?this.contents==null?!1:(this.contents=null,!0):$r(this.contents)?this.contents.deleteIn(e):!1}get(e,i){return At.isCollection(this.contents)?this.contents.get(e,i):void 0}getIn(e,i){return Ur.isEmptyPath(e)?!i&&At.isScalar(this.contents)?this.contents.value:this.contents:At.isCollection(this.contents)?this.contents.getIn(e,i):void 0}has(e){return At.isCollection(this.contents)?this.contents.has(e):!1}hasIn(e){return Ur.isEmptyPath(e)?this.contents!==void 0:At.isCollection(this.contents)?this.contents.hasIn(e):!1}set(e,i){this.contents==null?this.contents=Ur.collectionFromPath(this.schema,[e],i):$r(this.contents)&&this.contents.set(e,i)}setIn(e,i){Ur.isEmptyPath(e)?this.contents=i:this.contents==null?this.contents=Ur.collectionFromPath(this.schema,Array.from(e),i):$r(this.contents)&&this.contents.setIn(e,i)}setSchema(e,i={}){typeof e=="number"&&(e=String(e));let r;switch(e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new cu.Directives({version:"1.1"}),r={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new cu.Directives({version:e}),r={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,r=null;break;default:{let n=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${n}`)}}if(i.schema instanceof Object)this.schema=i.schema;else if(r)this.schema=new jO.Schema(Object.assign(r,i));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:i,mapAsMap:r,maxAliasCount:n,onAnchor:s,reviver:o}={}){let a={anchors:new Map,doc:this,keep:!e,mapAsMap:r===!0,mapKeyWarned:!1,maxAliasCount:typeof n=="number"?n:100},l=DO.toJS(this.contents,i!=null?i:"",a);if(typeof s=="function")for(let{count:c,res:u}of a.anchors.values())s(u,c);return typeof o=="function"?$O.applyReviver(o,{"":l},"",l):l}toJSON(e,i){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:i})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){let i=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${i}`)}return UO.stringifyDocument(this,e)}};function $r(t){if(At.isCollection(t))return!0;throw new Error("Expected a YAML collection as document contents")}C0.Document=uu});var Mn=w(Pn=>{"use strict";var Rn=class extends Error{constructor(e,i,r,n){super(),this.name=e,this.code=r,this.message=n,this.pos=i}},fu=class extends Rn{constructor(e,i,r){super("YAMLParseError",e,i,r)}},hu=class extends Rn{constructor(e,i,r){super("YAMLWarning",e,i,r)}},HO=(t,e)=>i=>{if(i.pos[0]===-1)return;i.linePos=i.pos.map(a=>e.linePos(a));let{line:r,col:n}=i.linePos[0];i.message+=` at line ${r}, column ${n}`;let s=n-1,o=t.substring(e.lineStarts[r-1],e.lineStarts[r]).replace(/[\n\r]+$/,"");if(s>=60&&o.length>80){let a=Math.min(s-39,o.length-79);o="\u2026"+o.substring(a),s-=a-1}if(o.length>80&&(o=o.substring(0,79)+"\u2026"),r>1&&/^ *$/.test(o.substring(0,s))){let a=t.substring(e.lineStarts[r-2],e.lineStarts[r-1]);a.length>80&&(a=a.substring(0,79)+`\u2026
`),o=a+o}if(/[^ ]/.test(o)){let a=1,l=i.linePos[1];l&&l.line===r&&l.col>n&&(a=Math.max(1,Math.min(l.col-n,80-s)));let c=" ".repeat(s)+"^".repeat(a);i.message+=`:

${o}
${c}
`}};Pn.YAMLError=Rn;Pn.YAMLParseError=fu;Pn.YAMLWarning=hu;Pn.prettifyError=HO});var Fn=w(T0=>{"use strict";function GO(t,{flow:e,indicator:i,next:r,offset:n,onError:s,parentIndent:o,startOnNewline:a}){let l=!1,c=a,u=a,f="",d="",g=!1,m=!1,v=null,b=null,_=null,S=null,O=null,k=null,E=null;for(let A of t)switch(m&&(A.type!=="space"&&A.type!=="newline"&&A.type!=="comma"&&s(A.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),m=!1),v&&(c&&A.type!=="comment"&&A.type!=="newline"&&s(v,"TAB_AS_INDENT","Tabs are not allowed as indentation"),v=null),A.type){case"space":!e&&(i!=="doc-start"||(r==null?void 0:r.type)!=="flow-collection")&&A.source.includes("	")&&(v=A),u=!0;break;case"comment":{u||s(A,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let C=A.source.substring(1)||" ";f?f+=d+C:f=C,d="",c=!1;break}case"newline":c?f?f+=A.source:l=!0:d+=A.source,c=!0,g=!0,(b||_)&&(S=A),u=!0;break;case"anchor":b&&s(A,"MULTIPLE_ANCHORS","A node can have at most one anchor"),A.source.endsWith(":")&&s(A.offset+A.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),b=A,E===null&&(E=A.offset),c=!1,u=!1,m=!0;break;case"tag":{_&&s(A,"MULTIPLE_TAGS","A node can have at most one tag"),_=A,E===null&&(E=A.offset),c=!1,u=!1,m=!0;break}case i:(b||_)&&s(A,"BAD_PROP_ORDER",`Anchors and tags must be after the ${A.source} indicator`),k&&s(A,"UNEXPECTED_TOKEN",`Unexpected ${A.source} in ${e!=null?e:"collection"}`),k=A,c=i==="seq-item-ind"||i==="explicit-key-ind",u=!1;break;case"comma":if(e){O&&s(A,"UNEXPECTED_TOKEN",`Unexpected , in ${e}`),O=A,c=!1,u=!1;break}default:s(A,"UNEXPECTED_TOKEN",`Unexpected ${A.type} token`),c=!1,u=!1}let R=t[t.length-1],T=R?R.offset+R.source.length:n;return m&&r&&r.type!=="space"&&r.type!=="newline"&&r.type!=="comma"&&(r.type!=="scalar"||r.source!=="")&&s(r.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),v&&(c&&v.indent<=o||(r==null?void 0:r.type)==="block-map"||(r==null?void 0:r.type)==="block-seq")&&s(v,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:O,found:k,spaceBefore:l,comment:f,hasNewline:g,anchor:b,tag:_,newlineAfterProp:S,end:T,start:E!=null?E:T}}T0.resolveProps=GO});var Do=w(A0=>{"use strict";function pu(t){if(!t)return null;switch(t.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(t.source.includes(`
`))return!0;if(t.end){for(let e of t.end)if(e.type==="newline")return!0}return!1;case"flow-collection":for(let e of t.items){for(let i of e.start)if(i.type==="newline")return!0;if(e.sep){for(let i of e.sep)if(i.type==="newline")return!0}if(pu(e.key)||pu(e.value))return!0}return!1;default:return!0}}A0.containsNewline=pu});var du=w(I0=>{"use strict";var YO=Do();function WO(t,e,i){if((e==null?void 0:e.type)==="flow-collection"){let r=e.end[0];r.indent===t&&(r.source==="]"||r.source==="}")&&YO.containsNewline(e)&&i(r,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}I0.flowIndentCheck=WO});var mu=w(L0=>{"use strict";var N0=Se();function zO(t,e,i){let{uniqueKeys:r}=t.options;if(r===!1)return!1;let n=typeof r=="function"?r:(s,o)=>s===o||N0.isScalar(s)&&N0.isScalar(o)&&s.value===o.value;return e.some(s=>n(s.key,i))}L0.mapIncludes=zO});var q0=w(F0=>{"use strict";var B0=wi(),KO=Si(),R0=Fn(),JO=Do(),P0=du(),ZO=mu(),M0="All mapping items must start at the same column";function QO({composeNode:t,composeEmptyNode:e},i,r,n,s){var u,f;let o=(u=s==null?void 0:s.nodeClass)!=null?u:KO.YAMLMap,a=new o(i.schema);i.atRoot&&(i.atRoot=!1);let l=r.offset,c=null;for(let d of r.items){let{start:g,key:m,sep:v,value:b}=d,_=R0.resolveProps(g,{indicator:"explicit-key-ind",next:m!=null?m:v==null?void 0:v[0],offset:l,onError:n,parentIndent:r.indent,startOnNewline:!0}),S=!_.found;if(S){if(m&&(m.type==="block-seq"?n(l,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in m&&m.indent!==r.indent&&n(l,"BAD_INDENT",M0)),!_.anchor&&!_.tag&&!v){c=_.end,_.comment&&(a.comment?a.comment+=`
`+_.comment:a.comment=_.comment);continue}(_.newlineAfterProp||JO.containsNewline(m))&&n(m!=null?m:g[g.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else((f=_.found)==null?void 0:f.indent)!==r.indent&&n(l,"BAD_INDENT",M0);i.atKey=!0;let O=_.end,k=m?t(i,m,_,n):e(i,O,g,null,_,n);i.schema.compat&&P0.flowIndentCheck(r.indent,m,n),i.atKey=!1,ZO.mapIncludes(i,a.items,k)&&n(O,"DUPLICATE_KEY","Map keys must be unique");let E=R0.resolveProps(v!=null?v:[],{indicator:"map-value-ind",next:b,offset:k.range[2],onError:n,parentIndent:r.indent,startOnNewline:!m||m.type==="block-scalar"});if(l=E.end,E.found){S&&((b==null?void 0:b.type)==="block-map"&&!E.hasNewline&&n(l,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),i.options.strict&&_.start<E.found.offset-1024&&n(k.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));let R=b?t(i,b,E,n):e(i,l,v,null,E,n);i.schema.compat&&P0.flowIndentCheck(r.indent,b,n),l=R.range[2];let T=new B0.Pair(k,R);i.options.keepSourceTokens&&(T.srcToken=d),a.items.push(T)}else{S&&n(k.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),E.comment&&(k.comment?k.comment+=`
`+E.comment:k.comment=E.comment);let R=new B0.Pair(k);i.options.keepSourceTokens&&(R.srcToken=d),a.items.push(R)}}return c&&c<l&&n(c,"IMPOSSIBLE","Map comment with trailing content"),a.range=[r.offset,l,c!=null?c:l],a}F0.resolveBlockMap=QO});var j0=w(D0=>{"use strict";var XO=Ei(),eC=Fn(),tC=du();function iC({composeNode:t,composeEmptyNode:e},i,r,n,s){var u;let o=(u=s==null?void 0:s.nodeClass)!=null?u:XO.YAMLSeq,a=new o(i.schema);i.atRoot&&(i.atRoot=!1),i.atKey&&(i.atKey=!1);let l=r.offset,c=null;for(let{start:f,value:d}of r.items){let g=eC.resolveProps(f,{indicator:"seq-item-ind",next:d,offset:l,onError:n,parentIndent:r.indent,startOnNewline:!0});if(!g.found)if(g.anchor||g.tag||d)d&&d.type==="block-seq"?n(g.end,"BAD_INDENT","All sequence items must start at the same column"):n(l,"MISSING_CHAR","Sequence item without - indicator");else{c=g.end,g.comment&&(a.comment=g.comment);continue}let m=d?t(i,d,g,n):e(i,g.end,f,null,g,n);i.schema.compat&&tC.flowIndentCheck(r.indent,d,n),l=m.range[2],a.items.push(m)}return a.range=[r.offset,l,c!=null?c:l],a}D0.resolveBlockSeq=iC});var Vr=w(U0=>{"use strict";function rC(t,e,i,r){let n="";if(t){let s=!1,o="";for(let a of t){let{source:l,type:c}=a;switch(c){case"space":s=!0;break;case"comment":{i&&!s&&r(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let u=l.substring(1)||" ";n?n+=o+u:n=u,o="";break}case"newline":n&&(o+=l),s=!0;break;default:r(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}e+=l.length}}return{comment:n,offset:e}}U0.resolveEnd=rC});var G0=w(H0=>{"use strict";var nC=Se(),sC=wi(),$0=Si(),oC=Ei(),aC=Vr(),V0=Fn(),lC=Do(),cC=mu(),gu="Block collections are not allowed within flow collections",vu=t=>t&&(t.type==="block-map"||t.type==="block-seq");function uC({composeNode:t,composeEmptyNode:e},i,r,n,s){var b,_;let o=r.start.source==="{",a=o?"flow map":"flow sequence",l=(b=s==null?void 0:s.nodeClass)!=null?b:o?$0.YAMLMap:oC.YAMLSeq,c=new l(i.schema);c.flow=!0;let u=i.atRoot;u&&(i.atRoot=!1),i.atKey&&(i.atKey=!1);let f=r.offset+r.start.source.length;for(let S=0;S<r.items.length;++S){let O=r.items[S],{start:k,key:E,sep:R,value:T}=O,A=V0.resolveProps(k,{flow:a,indicator:"explicit-key-ind",next:E!=null?E:R==null?void 0:R[0],offset:f,onError:n,parentIndent:r.indent,startOnNewline:!1});if(!A.found){if(!A.anchor&&!A.tag&&!R&&!T){S===0&&A.comma?n(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):S<r.items.length-1&&n(A.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),A.comment&&(c.comment?c.comment+=`
`+A.comment:c.comment=A.comment),f=A.end;continue}!o&&i.options.strict&&lC.containsNewline(E)&&n(E,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(S===0)A.comma&&n(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(A.comma||n(A.start,"MISSING_CHAR",`Missing , between ${a} items`),A.comment){let C="";e:for(let L of k)switch(L.type){case"comma":case"space":break;case"comment":C=L.source.substring(1);break e;default:break e}if(C){let L=c.items[c.items.length-1];nC.isPair(L)&&(L=(_=L.value)!=null?_:L.key),L.comment?L.comment+=`
`+C:L.comment=C,A.comment=A.comment.substring(C.length+1)}}if(!o&&!R&&!A.found){let C=T?t(i,T,A,n):e(i,A.end,R,null,A,n);c.items.push(C),f=C.range[2],vu(T)&&n(C.range,"BLOCK_IN_FLOW",gu)}else{i.atKey=!0;let C=A.end,L=E?t(i,E,A,n):e(i,C,k,null,A,n);vu(E)&&n(L.range,"BLOCK_IN_FLOW",gu),i.atKey=!1;let P=V0.resolveProps(R!=null?R:[],{flow:a,indicator:"map-value-ind",next:T,offset:L.range[2],onError:n,parentIndent:r.indent,startOnNewline:!1});if(P.found){if(!o&&!A.found&&i.options.strict){if(R)for(let H of R){if(H===P.found)break;if(H.type==="newline"){n(H,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}A.start<P.found.offset-1024&&n(P.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else T&&("source"in T&&T.source&&T.source[0]===":"?n(T,"MISSING_CHAR",`Missing space after : in ${a}`):n(P.start,"MISSING_CHAR",`Missing , or : between ${a} items`));let U=T?t(i,T,P,n):P.found?e(i,P.end,R,null,P,n):null;U?vu(T)&&n(U.range,"BLOCK_IN_FLOW",gu):P.comment&&(L.comment?L.comment+=`
`+P.comment:L.comment=P.comment);let q=new sC.Pair(L,U);if(i.options.keepSourceTokens&&(q.srcToken=O),o){let H=c;cC.mapIncludes(i,H.items,L)&&n(C,"DUPLICATE_KEY","Map keys must be unique"),H.items.push(q)}else{let H=new $0.YAMLMap(i.schema);H.flow=!0,H.items.push(q);let j=(U!=null?U:L).range;H.range=[L.range[0],j[1],j[2]],c.items.push(H)}f=U?U.range[2]:P.end}}let d=o?"}":"]",[g,...m]=r.end,v=f;if(g&&g.source===d)v=g.offset+g.source.length;else{let S=a[0].toUpperCase()+a.substring(1),O=u?`${S} must end with a ${d}`:`${S} in block collection must be sufficiently indented and end with a ${d}`;n(f,u?"MISSING_CHAR":"BAD_INDENT",O),g&&g.source.length!==1&&m.unshift(g)}if(m.length>0){let S=aC.resolveEnd(m,v,i.options.strict,n);S.comment&&(c.comment?c.comment+=`
`+S.comment:c.comment=S.comment),c.range=[r.offset,v,S.offset]}else c.range=[r.offset,v,v];return c}H0.resolveFlowCollection=uC});var W0=w(Y0=>{"use strict";var fC=Se(),hC=je(),pC=Si(),dC=Ei(),mC=q0(),gC=j0(),vC=G0();function yu(t,e,i,r,n,s){let o=i.type==="block-map"?mC.resolveBlockMap(t,e,i,r,s):i.type==="block-seq"?gC.resolveBlockSeq(t,e,i,r,s):vC.resolveFlowCollection(t,e,i,r,s),a=o.constructor;return n==="!"||n===a.tagName?(o.tag=a.tagName,o):(n&&(o.tag=n),o)}function yC(t,e,i,r,n){var d,g;let s=r.tag,o=s?e.directives.tagName(s.source,m=>n(s,"TAG_RESOLVE_FAILED",m)):null;if(i.type==="block-seq"){let{anchor:m,newlineAfterProp:v}=r,b=m&&s?m.offset>s.offset?m:s:m!=null?m:s;b&&(!v||v.offset<b.offset)&&n(b,"MISSING_CHAR","Missing newline after block sequence props")}let a=i.type==="block-map"?"map":i.type==="block-seq"?"seq":i.start.source==="{"?"map":"seq";if(!s||!o||o==="!"||o===pC.YAMLMap.tagName&&a==="map"||o===dC.YAMLSeq.tagName&&a==="seq")return yu(t,e,i,n,o);let l=e.schema.tags.find(m=>m.tag===o&&m.collection===a);if(!l){let m=e.schema.knownTags[o];if(m&&m.collection===a)e.schema.tags.push(Object.assign({},m,{default:!1})),l=m;else return m!=null&&m.collection?n(s,"BAD_COLLECTION_TYPE",`${m.tag} used for ${a} collection, but expects ${m.collection}`,!0):n(s,"TAG_RESOLVE_FAILED",`Unresolved tag: ${o}`,!0),yu(t,e,i,n,o)}let c=yu(t,e,i,n,o,l),u=(g=(d=l.resolve)==null?void 0:d.call(l,c,m=>n(s,"TAG_RESOLVE_FAILED",m),e.options))!=null?g:c,f=fC.isNode(u)?u:new hC.Scalar(u);return f.range=c.range,f.tag=o,l!=null&&l.format&&(f.format=l.format),f}Y0.composeCollection=yC});var _u=w(z0=>{"use strict";var bu=je();function bC(t,e,i){let r=e.offset,n=_C(e,t.options.strict,i);if(!n)return{value:"",type:null,comment:"",range:[r,r,r]};let s=n.mode===">"?bu.Scalar.BLOCK_FOLDED:bu.Scalar.BLOCK_LITERAL,o=e.source?wC(e.source):[],a=o.length;for(let v=o.length-1;v>=0;--v){let b=o[v][1];if(b===""||b==="\r")a=v;else break}if(a===0){let v=n.chomp==="+"&&o.length>0?`
`.repeat(Math.max(1,o.length-1)):"",b=r+n.length;return e.source&&(b+=e.source.length),{value:v,type:s,comment:n.comment,range:[r,b,b]}}let l=e.indent+n.indent,c=e.offset+n.length,u=0;for(let v=0;v<a;++v){let[b,_]=o[v];if(_===""||_==="\r")n.indent===0&&b.length>l&&(l=b.length);else{if(b.length<l){let S="Block scalars with more-indented leading empty lines must use an explicit indentation indicator";i(c+b.length,"MISSING_CHAR",S)}n.indent===0&&(l=b.length),u=v,l===0&&!t.atRoot&&i(c,"BAD_INDENT","Block scalar values in collections must be indented");break}c+=b.length+_.length+1}for(let v=o.length-1;v>=a;--v)o[v][0].length>l&&(a=v+1);let f="",d="",g=!1;for(let v=0;v<u;++v)f+=o[v][0].slice(l)+`
`;for(let v=u;v<a;++v){let[b,_]=o[v];c+=b.length+_.length+1;let S=_[_.length-1]==="\r";if(S&&(_=_.slice(0,-1)),_&&b.length<l){let k=`Block scalar lines must not be less indented than their ${n.indent?"explicit indentation indicator":"first line"}`;i(c-_.length-(S?2:1),"BAD_INDENT",k),b=""}s===bu.Scalar.BLOCK_LITERAL?(f+=d+b.slice(l)+_,d=`
`):b.length>l||_[0]==="	"?(d===" "?d=`
`:!g&&d===`
`&&(d=`

`),f+=d+b.slice(l)+_,d=`
`,g=!0):_===""?d===`
`?f+=`
`:d=`
`:(f+=d+_,d=" ",g=!1)}switch(n.chomp){case"-":break;case"+":for(let v=a;v<o.length;++v)f+=`
`+o[v][0].slice(l);f[f.length-1]!==`
`&&(f+=`
`);break;default:f+=`
`}let m=r+n.length+e.source.length;return{value:f,type:s,comment:n.comment,range:[r,m,m]}}function _C({offset:t,props:e},i,r){if(e[0].type!=="block-scalar-header")return r(e[0],"IMPOSSIBLE","Block scalar header not found"),null;let{source:n}=e[0],s=n[0],o=0,a="",l=-1;for(let d=1;d<n.length;++d){let g=n[d];if(!a&&(g==="-"||g==="+"))a=g;else{let m=Number(g);!o&&m?o=m:l===-1&&(l=t+d)}}l!==-1&&r(l,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${n}`);let c=!1,u="",f=n.length;for(let d=1;d<e.length;++d){let g=e[d];switch(g.type){case"space":c=!0;case"newline":f+=g.source.length;break;case"comment":i&&!c&&r(g,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),f+=g.source.length,u=g.source.substring(1);break;case"error":r(g,"UNEXPECTED_TOKEN",g.message),f+=g.source.length;break;default:{let m=`Unexpected token in block scalar header: ${g.type}`;r(g,"UNEXPECTED_TOKEN",m);let v=g.source;v&&typeof v=="string"&&(f+=v.length)}}}return{mode:s,indent:o,chomp:a,comment:u,length:f}}function wC(t){let e=t.split(/\n( *)/),i=e[0],r=i.match(/^( *)/),s=[r!=null&&r[1]?[r[1],i.slice(r[1].length)]:["",i]];for(let o=1;o<e.length;o+=2)s.push([e[o],e[o+1]]);return s}z0.resolveBlockScalar=bC});var xu=w(J0=>{"use strict";var wu=je(),xC=Vr();function SC(t,e,i){let{offset:r,type:n,source:s,end:o}=t,a,l,c=(d,g,m)=>i(r+d,g,m);switch(n){case"scalar":a=wu.Scalar.PLAIN,l=EC(s,c);break;case"single-quoted-scalar":a=wu.Scalar.QUOTE_SINGLE,l=kC(s,c);break;case"double-quoted-scalar":a=wu.Scalar.QUOTE_DOUBLE,l=OC(s,c);break;default:return i(t,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${n}`),{value:"",type:null,comment:"",range:[r,r+s.length,r+s.length]}}let u=r+s.length,f=xC.resolveEnd(o,u,e,i);return{value:l,type:a,comment:f.comment,range:[r,u,f.offset]}}function EC(t,e){let i="";switch(t[0]){case"	":i="a tab character";break;case",":i="flow indicator character ,";break;case"%":i="directive indicator character %";break;case"|":case">":{i=`block scalar indicator ${t[0]}`;break}case"@":case"`":{i=`reserved character ${t[0]}`;break}}return i&&e(0,"BAD_SCALAR_START",`Plain value cannot start with ${i}`),K0(t)}function kC(t,e){return(t[t.length-1]!=="'"||t.length===1)&&e(t.length,"MISSING_CHAR","Missing closing 'quote"),K0(t.slice(1,-1)).replace(/''/g,"'")}function K0(t){var l;let e,i;try{e=new RegExp(`(.*?)(?<![ 	])[ 	]*\r?
`,"sy"),i=new RegExp(`[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?
`,"sy")}catch{e=/(.*?)[ \t]*\r?\n/sy,i=/[ \t]*(.*?)[ \t]*\r?\n/sy}let r=e.exec(t);if(!r)return t;let n=r[1],s=" ",o=e.lastIndex;for(i.lastIndex=o;r=i.exec(t);)r[1]===""?s===`
`?n+=s:s=`
`:(n+=s+r[1],s=" "),o=i.lastIndex;let a=/[ \t]*(.*)/sy;return a.lastIndex=o,r=a.exec(t),n+s+((l=r==null?void 0:r[1])!=null?l:"")}function OC(t,e){let i="";for(let r=1;r<t.length-1;++r){let n=t[r];if(!(n==="\r"&&t[r+1]===`
`))if(n===`
`){let{fold:s,offset:o}=CC(t,r);i+=s,r=o}else if(n==="\\"){let s=t[++r],o=TC[s];if(o)i+=o;else if(s===`
`)for(s=t[r+1];s===" "||s==="	";)s=t[++r+1];else if(s==="\r"&&t[r+1]===`
`)for(s=t[++r+1];s===" "||s==="	";)s=t[++r+1];else if(s==="x"||s==="u"||s==="U"){let a={x:2,u:4,U:8}[s];i+=AC(t,r+1,a,e),r+=a}else{let a=t.substr(r-1,2);e(r-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),i+=a}}else if(n===" "||n==="	"){let s=r,o=t[r+1];for(;o===" "||o==="	";)o=t[++r+1];o!==`
`&&!(o==="\r"&&t[r+2]===`
`)&&(i+=r>s?t.slice(s,r+1):n)}else i+=n}return(t[t.length-1]!=='"'||t.length===1)&&e(t.length,"MISSING_CHAR",'Missing closing "quote'),i}function CC(t,e){let i="",r=t[e+1];for(;(r===" "||r==="	"||r===`
`||r==="\r")&&!(r==="\r"&&t[e+2]!==`
`);)r===`
`&&(i+=`
`),e+=1,r=t[e+1];return i||(i=" "),{fold:i,offset:e}}var TC={0:"\0",a:"\x07",b:"\b",e:"\x1B",f:"\f",n:`
`,r:"\r",t:"	",v:"\v",N:"\x85",_:"\xA0",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function AC(t,e,i,r){let n=t.substr(e,i),o=n.length===i&&/^[0-9a-fA-F]+$/.test(n)?parseInt(n,16):NaN;if(isNaN(o)){let a=t.substr(e-2,i+2);return r(e-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),a}return String.fromCodePoint(o)}J0.resolveFlowScalar=SC});var X0=w(Q0=>{"use strict";var er=Se(),Z0=je(),IC=_u(),NC=xu();function LC(t,e,i,r){let{value:n,type:s,comment:o,range:a}=e.type==="block-scalar"?IC.resolveBlockScalar(t,e,r):NC.resolveFlowScalar(e,t.options.strict,r),l=i?t.directives.tagName(i.source,f=>r(i,"TAG_RESOLVE_FAILED",f)):null,c;t.options.stringKeys&&t.atKey?c=t.schema[er.SCALAR]:l?c=BC(t.schema,n,l,i,r):e.type==="scalar"?c=RC(t,n,e,r):c=t.schema[er.SCALAR];let u;try{let f=c.resolve(n,d=>r(i!=null?i:e,"TAG_RESOLVE_FAILED",d),t.options);u=er.isScalar(f)?f:new Z0.Scalar(f)}catch(f){let d=f instanceof Error?f.message:String(f);r(i!=null?i:e,"TAG_RESOLVE_FAILED",d),u=new Z0.Scalar(n)}return u.range=a,u.source=n,s&&(u.type=s),l&&(u.tag=l),c.format&&(u.format=c.format),o&&(u.comment=o),u}function BC(t,e,i,r,n){var a;if(i==="!")return t[er.SCALAR];let s=[];for(let l of t.tags)if(!l.collection&&l.tag===i)if(l.default&&l.test)s.push(l);else return l;for(let l of s)if((a=l.test)!=null&&a.test(e))return l;let o=t.knownTags[i];return o&&!o.collection?(t.tags.push(Object.assign({},o,{default:!1,test:void 0})),o):(n(r,"TAG_RESOLVE_FAILED",`Unresolved tag: ${i}`,i!=="tag:yaml.org,2002:str"),t[er.SCALAR])}function RC({atKey:t,directives:e,schema:i},r,n,s){var a;let o=i.tags.find(l=>{var c;return(l.default===!0||t&&l.default==="key")&&((c=l.test)==null?void 0:c.test(r))})||i[er.SCALAR];if(i.compat){let l=(a=i.compat.find(c=>{var u;return c.default&&((u=c.test)==null?void 0:u.test(r))}))!=null?a:i[er.SCALAR];if(o.tag!==l.tag){let c=e.tagString(o.tag),u=e.tagString(l.tag),f=`Value may be parsed as either ${c} or ${u}`;s(n,"TAG_RESOLVE_FAILED",f,!0)}}return o}Q0.composeScalar=LC});var tv=w(ev=>{"use strict";function PC(t,e,i){if(e){i===null&&(i=e.length);for(let r=i-1;r>=0;--r){let n=e[r];switch(n.type){case"space":case"comment":case"newline":t-=n.source.length;continue}for(n=e[++r];(n==null?void 0:n.type)==="space";)t+=n.source.length,n=e[++r];break}}return t}ev.emptyScalarPosition=PC});var nv=w(Eu=>{"use strict";var MC=vn(),FC=Se(),qC=W0(),iv=X0(),DC=Vr(),jC=tv(),UC={composeNode:rv,composeEmptyNode:Su};function rv(t,e,i,r){let n=t.atKey,{spaceBefore:s,comment:o,anchor:a,tag:l}=i,c,u=!0;switch(e.type){case"alias":c=$C(t,e,r),(a||l)&&r(e,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=iv.composeScalar(t,e,l,r),a&&(c.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=qC.composeCollection(UC,t,e,i,r),a&&(c.anchor=a.source.substring(1));break;default:{let f=e.type==="error"?e.message:`Unsupported token (type: ${e.type})`;r(e,"UNEXPECTED_TOKEN",f),c=Su(t,e.offset,void 0,null,i,r),u=!1}}if(a&&c.anchor===""&&r(a,"BAD_ALIAS","Anchor cannot be an empty string"),n&&t.options.stringKeys&&(!FC.isScalar(c)||typeof c.value!="string"||c.tag&&c.tag!=="tag:yaml.org,2002:str")){let f="With stringKeys, all keys must be strings";r(l!=null?l:e,"NON_STRING_KEY",f)}return s&&(c.spaceBefore=!0),o&&(e.type==="scalar"&&e.source===""?c.comment=o:c.commentBefore=o),t.options.keepSourceTokens&&u&&(c.srcToken=e),c}function Su(t,e,i,r,{spaceBefore:n,comment:s,anchor:o,tag:a,end:l},c){let u={type:"scalar",offset:jC.emptyScalarPosition(e,i,r),indent:-1,source:""},f=iv.composeScalar(t,u,a,c);return o&&(f.anchor=o.source.substring(1),f.anchor===""&&c(o,"BAD_ALIAS","Anchor cannot be an empty string")),n&&(f.spaceBefore=!0),s&&(f.comment=s,f.range[2]=l),f}function $C({options:t},{offset:e,source:i,end:r},n){let s=new MC.Alias(i.substring(1));s.source===""&&n(e,"BAD_ALIAS","Alias cannot be an empty string"),s.source.endsWith(":")&&n(e+i.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);let o=e+i.length,a=DC.resolveEnd(r,o,t.strict,n);return s.range=[e,o,a.offset],a.comment&&(s.comment=a.comment),s}Eu.composeEmptyNode=Su;Eu.composeNode=rv});var av=w(ov=>{"use strict";var VC=Bn(),sv=nv(),HC=Vr(),GC=Fn();function YC(t,e,{offset:i,start:r,value:n,end:s},o){let a=Object.assign({_directives:e},t),l=new VC.Document(void 0,a),c={atKey:!1,atRoot:!0,directives:l.directives,options:l.options,schema:l.schema},u=GC.resolveProps(r,{indicator:"doc-start",next:n!=null?n:s==null?void 0:s[0],offset:i,onError:o,parentIndent:0,startOnNewline:!0});u.found&&(l.directives.docStart=!0,n&&(n.type==="block-map"||n.type==="block-seq")&&!u.hasNewline&&o(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),l.contents=n?sv.composeNode(c,n,u,o):sv.composeEmptyNode(c,u.end,r,null,u,o);let f=l.contents.range[2],d=HC.resolveEnd(s,f,!1,o);return d.comment&&(l.comment=d.comment),l.range=[i,f,d.offset],l}ov.composeDoc=YC});var Ou=w(uv=>{"use strict";var WC=fc(),zC=Bn(),qn=Mn(),lv=Se(),KC=av(),JC=Vr();function Dn(t){if(typeof t=="number")return[t,t+1];if(Array.isArray(t))return t.length===2?t:[t[0],t[1]];let{offset:e,source:i}=t;return[e,e+(typeof i=="string"?i.length:1)]}function cv(t){var n;let e="",i=!1,r=!1;for(let s=0;s<t.length;++s){let o=t[s];switch(o[0]){case"#":e+=(e===""?"":r?`

`:`
`)+(o.substring(1)||" "),i=!0,r=!1;break;case"%":((n=t[s+1])==null?void 0:n[0])!=="#"&&(s+=1),i=!1;break;default:i||(r=!0),i=!1}}return{comment:e,afterEmptyLine:r}}var ku=class{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(i,r,n,s)=>{let o=Dn(i);s?this.warnings.push(new qn.YAMLWarning(o,r,n)):this.errors.push(new qn.YAMLParseError(o,r,n))},this.directives=new WC.Directives({version:e.version||"1.2"}),this.options=e}decorate(e,i){let{comment:r,afterEmptyLine:n}=cv(this.prelude);if(r){let s=e.contents;if(i)e.comment=e.comment?`${e.comment}
${r}`:r;else if(n||e.directives.docStart||!s)e.commentBefore=r;else if(lv.isCollection(s)&&!s.flow&&s.items.length>0){let o=s.items[0];lv.isPair(o)&&(o=o.key);let a=o.commentBefore;o.commentBefore=a?`${r}
${a}`:r}else{let o=s.commentBefore;s.commentBefore=o?`${r}
${o}`:r}}i?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:cv(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,i=!1,r=-1){for(let n of e)yield*this.next(n);yield*this.end(i,r)}*next(e){switch(process.env.LOG_STREAM&&console.dir(e,{depth:null}),e.type){case"directive":this.directives.add(e.source,(i,r,n)=>{let s=Dn(e);s[0]+=i,this.onError(s,"BAD_DIRECTIVE",r,n)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{let i=KC.composeDoc(this.options,this.directives,e,this.onError);this.atDirectives&&!i.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(i,!1),this.doc&&(yield this.doc),this.doc=i,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{let i=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,r=new qn.YAMLParseError(Dn(e),"UNEXPECTED_TOKEN",i);this.atDirectives||!this.doc?this.errors.push(r):this.doc.errors.push(r);break}case"doc-end":{if(!this.doc){let r="Unexpected doc-end without preceding document";this.errors.push(new qn.YAMLParseError(Dn(e),"UNEXPECTED_TOKEN",r));break}this.doc.directives.docEnd=!0;let i=JC.resolveEnd(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),i.comment){let r=this.doc.comment;this.doc.comment=r?`${r}
${i.comment}`:i.comment}this.doc.range[2]=i.offset;break}default:this.errors.push(new qn.YAMLParseError(Dn(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,i=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){let r=Object.assign({_directives:this.directives},this.options),n=new zC.Document(void 0,r);this.atDirectives&&this.onError(i,"MISSING_CHAR","Missing directives-end indicator line"),n.range=[0,i,i],this.decorate(n,!1),yield n}}};uv.Composer=ku});var pv=w(jo=>{"use strict";var ZC=_u(),QC=xu(),XC=Mn(),fv=xn();function eT(t,e=!0,i){if(t){let r=(n,s,o)=>{let a=typeof n=="number"?n:Array.isArray(n)?n[0]:n.offset;if(i)i(a,s,o);else throw new XC.YAMLParseError([a,a+1],s,o)};switch(t.type){case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return QC.resolveFlowScalar(t,e,r);case"block-scalar":return ZC.resolveBlockScalar({options:{strict:e}},t,r)}}return null}function tT(t,e){var c;let{implicitKey:i=!1,indent:r,inFlow:n=!1,offset:s=-1,type:o="PLAIN"}=e,a=fv.stringifyString({type:o,value:t},{implicitKey:i,indent:r>0?" ".repeat(r):"",inFlow:n,options:{blockQuote:!0,lineWidth:-1}}),l=(c=e.end)!=null?c:[{type:"newline",offset:-1,indent:r,source:`
`}];switch(a[0]){case"|":case">":{let u=a.indexOf(`
`),f=a.substring(0,u),d=a.substring(u+1)+`
`,g=[{type:"block-scalar-header",offset:s,indent:r,source:f}];return hv(g,l)||g.push({type:"newline",offset:-1,indent:r,source:`
`}),{type:"block-scalar",offset:s,indent:r,props:g,source:d}}case'"':return{type:"double-quoted-scalar",offset:s,indent:r,source:a,end:l};case"'":return{type:"single-quoted-scalar",offset:s,indent:r,source:a,end:l};default:return{type:"scalar",offset:s,indent:r,source:a,end:l}}}function iT(t,e,i={}){let{afterKey:r=!1,implicitKey:n=!1,inFlow:s=!1,type:o}=i,a="indent"in t?t.indent:null;if(r&&typeof a=="number"&&(a+=2),!o)switch(t.type){case"single-quoted-scalar":o="QUOTE_SINGLE";break;case"double-quoted-scalar":o="QUOTE_DOUBLE";break;case"block-scalar":{let c=t.props[0];if(c.type!=="block-scalar-header")throw new Error("Invalid block scalar header");o=c.source[0]===">"?"BLOCK_FOLDED":"BLOCK_LITERAL";break}default:o="PLAIN"}let l=fv.stringifyString({type:o,value:e},{implicitKey:n||a===null,indent:a!==null&&a>0?" ".repeat(a):"",inFlow:s,options:{blockQuote:!0,lineWidth:-1}});switch(l[0]){case"|":case">":rT(t,l);break;case'"':Cu(t,l,"double-quoted-scalar");break;case"'":Cu(t,l,"single-quoted-scalar");break;default:Cu(t,l,"scalar")}}function rT(t,e){let i=e.indexOf(`
`),r=e.substring(0,i),n=e.substring(i+1)+`
`;if(t.type==="block-scalar"){let s=t.props[0];if(s.type!=="block-scalar-header")throw new Error("Invalid block scalar header");s.source=r,t.source=n}else{let{offset:s}=t,o="indent"in t?t.indent:-1,a=[{type:"block-scalar-header",offset:s,indent:o,source:r}];hv(a,"end"in t?t.end:void 0)||a.push({type:"newline",offset:-1,indent:o,source:`
`});for(let l of Object.keys(t))l!=="type"&&l!=="offset"&&delete t[l];Object.assign(t,{type:"block-scalar",indent:o,props:a,source:n})}}function hv(t,e){if(e)for(let i of e)switch(i.type){case"space":case"comment":t.push(i);break;case"newline":return t.push(i),!0}return!1}function Cu(t,e,i){switch(t.type){case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":t.type=i,t.source=e;break;case"block-scalar":{let r=t.props.slice(1),n=e.length;t.props[0].type==="block-scalar-header"&&(n-=t.props[0].source.length);for(let s of r)s.offset+=n;delete t.props,Object.assign(t,{type:i,source:e,end:r});break}case"block-map":case"block-seq":{let n={type:"newline",offset:t.offset+e.length,indent:t.indent,source:`
`};delete t.items,Object.assign(t,{type:i,source:e,end:[n]});break}default:{let r="indent"in t?t.indent:-1,n="end"in t&&Array.isArray(t.end)?t.end.filter(s=>s.type==="space"||s.type==="comment"||s.type==="newline"):[];for(let s of Object.keys(t))s!=="type"&&s!=="offset"&&delete t[s];Object.assign(t,{type:i,indent:r,source:e,end:n})}}}jo.createScalarToken=tT;jo.resolveAsScalar=eT;jo.setScalarValue=iT});var mv=w(dv=>{"use strict";var nT=t=>"type"in t?$o(t):Uo(t);function $o(t){switch(t.type){case"block-scalar":{let e="";for(let i of t.props)e+=$o(i);return e+t.source}case"block-map":case"block-seq":{let e="";for(let i of t.items)e+=Uo(i);return e}case"flow-collection":{let e=t.start.source;for(let i of t.items)e+=Uo(i);for(let i of t.end)e+=i.source;return e}case"document":{let e=Uo(t);if(t.end)for(let i of t.end)e+=i.source;return e}default:{let e=t.source;if("end"in t&&t.end)for(let i of t.end)e+=i.source;return e}}}function Uo({start:t,key:e,sep:i,value:r}){let n="";for(let s of t)n+=s.source;if(e&&(n+=$o(e)),i)for(let s of i)n+=s.source;return r&&(n+=$o(r)),n}dv.stringify=nT});var bv=w(yv=>{"use strict";var Tu=Symbol("break visit"),sT=Symbol("skip children"),gv=Symbol("remove item");function tr(t,e){"type"in t&&t.type==="document"&&(t={start:t.start,value:t.value}),vv(Object.freeze([]),t,e)}tr.BREAK=Tu;tr.SKIP=sT;tr.REMOVE=gv;tr.itemAtPath=(t,e)=>{let i=t;for(let[r,n]of e){let s=i==null?void 0:i[r];if(s&&"items"in s)i=s.items[n];else return}return i};tr.parentCollection=(t,e)=>{let i=tr.itemAtPath(t,e.slice(0,-1)),r=e[e.length-1][0],n=i==null?void 0:i[r];if(n&&"items"in n)return n;throw new Error("Parent collection not found")};function vv(t,e,i){let r=i(e,t);if(typeof r=="symbol")return r;for(let n of["key","value"]){let s=e[n];if(s&&"items"in s){for(let o=0;o<s.items.length;++o){let a=vv(Object.freeze(t.concat([[n,o]])),s.items[o],i);if(typeof a=="number")o=a-1;else{if(a===Tu)return Tu;a===gv&&(s.items.splice(o,1),o-=1)}}typeof r=="function"&&n==="key"&&(r=r(e,t))}}return typeof r=="function"?r(e,t):r}yv.visit=tr});var Vo=w(gt=>{"use strict";var Au=pv(),oT=mv(),aT=bv(),Iu="\uFEFF",Nu="",Lu="",Bu="",lT=t=>!!t&&"items"in t,cT=t=>!!t&&(t.type==="scalar"||t.type==="single-quoted-scalar"||t.type==="double-quoted-scalar"||t.type==="block-scalar");function uT(t){switch(t){case Iu:return"<BOM>";case Nu:return"<DOC>";case Lu:return"<FLOW_END>";case Bu:return"<SCALAR>";default:return JSON.stringify(t)}}function fT(t){switch(t){case Iu:return"byte-order-mark";case Nu:return"doc-mode";case Lu:return"flow-error-end";case Bu:return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case`
`:case`\r
`:return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(t[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}gt.createScalarToken=Au.createScalarToken;gt.resolveAsScalar=Au.resolveAsScalar;gt.setScalarValue=Au.setScalarValue;gt.stringify=oT.stringify;gt.visit=aT.visit;gt.BOM=Iu;gt.DOCUMENT=Nu;gt.FLOW_END=Lu;gt.SCALAR=Bu;gt.isCollection=lT;gt.isScalar=cT;gt.prettyToken=uT;gt.tokenType=fT});var Mu=w(wv=>{"use strict";var jn=Vo();function jt(t){switch(t){case void 0:case" ":case`
`:case"\r":case"	":return!0;default:return!1}}var _v=new Set("0123456789ABCDEFabcdef"),hT=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),Ho=new Set(",[]{}"),pT=new Set(` ,[]{}
\r	`),Ru=t=>!t||pT.has(t),Pu=class{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,i=!1){var n;if(e){if(typeof e!="string")throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!i;let r=(n=this.next)!=null?n:"stream";for(;r&&(i||this.hasChars(1));)r=yield*this.parseNext(r)}atLineEnd(){let e=this.pos,i=this.buffer[e];for(;i===" "||i==="	";)i=this.buffer[++e];return!i||i==="#"||i===`
`?!0:i==="\r"?this.buffer[e+1]===`
`:!1}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let i=this.buffer[e];if(this.indentNext>0){let r=0;for(;i===" ";)i=this.buffer[++r+e];if(i==="\r"){let n=this.buffer[r+e+1];if(n===`
`||!n&&!this.atEnd)return e+r+1}return i===`
`||r>=this.indentNext||!i&&!this.atEnd?e+r:-1}if(i==="-"||i==="."){let r=this.buffer.substr(e,3);if((r==="---"||r==="...")&&jt(this.buffer[e+3]))return-1}return e}getLine(){let e=this.lineEndPos;return(typeof e!="number"||e!==-1&&e<this.pos)&&(e=this.buffer.indexOf(`
`,this.pos),this.lineEndPos=e),e===-1?this.atEnd?this.buffer.substring(this.pos):null:(this.buffer[e-1]==="\r"&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(e===null)return this.setNext("stream");if(e[0]===jn.BOM&&(yield*this.pushCount(1),e=e.substring(1)),e[0]==="%"){let i=e.length,r=e.indexOf("#");for(;r!==-1;){let s=e[r-1];if(s===" "||s==="	"){i=r-1;break}else r=e.indexOf("#",r+1)}for(;;){let s=e[i-1];if(s===" "||s==="	")i-=1;else break}let n=(yield*this.pushCount(i))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-n),this.pushNewline(),"stream"}if(this.atLineEnd()){let i=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-i),yield*this.pushNewline(),"stream"}return yield jn.DOCUMENT,yield*this.parseLineStart()}*parseLineStart(){let e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if(e==="-"||e==="."){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");let i=this.peek(3);if((i==="---"||i==="...")&&jt(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,i==="---"?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!jt(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){let[e,i]=this.peek(2);if(!i&&!this.atEnd)return this.setNext("block-start");if((e==="-"||e==="?"||e===":")&&jt(i)){let r=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=r,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);let e=this.getLine();if(e===null)return this.setNext("doc");let i=yield*this.pushIndicators();switch(e[i]){case"#":yield*this.pushCount(e.length-i);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(Ru),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return i+=yield*this.parseBlockScalarHeader(),i+=yield*this.pushSpaces(!0),yield*this.pushCount(e.length-i),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,i,r=-1;do e=yield*this.pushNewline(),e>0?(i=yield*this.pushSpaces(!1),this.indentValue=r=i):i=0,i+=yield*this.pushSpaces(!0);while(e+i>0);let n=this.getLine();if(n===null)return this.setNext("flow");if((r!==-1&&r<this.indentNext&&n[0]!=="#"||r===0&&(n.startsWith("---")||n.startsWith("..."))&&jt(n[3]))&&!(r===this.indentNext-1&&this.flowLevel===1&&(n[0]==="]"||n[0]==="}")))return this.flowLevel=0,yield jn.FLOW_END,yield*this.parseLineStart();let s=0;for(;n[s]===",";)s+=yield*this.pushCount(1),s+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(s+=yield*this.pushIndicators(),n[s]){case void 0:return"flow";case"#":return yield*this.pushCount(n.length-s),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(Ru),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{let o=this.charAt(1);if(this.flowKey||jt(o)||o===",")return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){let e=this.charAt(0),i=this.buffer.indexOf(e,this.pos+1);if(e==="'")for(;i!==-1&&this.buffer[i+1]==="'";)i=this.buffer.indexOf("'",i+2);else for(;i!==-1;){let s=0;for(;this.buffer[i-1-s]==="\\";)s+=1;if(s%2===0)break;i=this.buffer.indexOf('"',i+1)}let r=this.buffer.substring(0,i),n=r.indexOf(`
`,this.pos);if(n!==-1){for(;n!==-1;){let s=this.continueScalar(n+1);if(s===-1)break;n=r.indexOf(`
`,s)}n!==-1&&(i=n-(r[n-1]==="\r"?2:1))}if(i===-1){if(!this.atEnd)return this.setNext("quoted-scalar");i=this.buffer.length}return yield*this.pushToIndex(i+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){let i=this.buffer[++e];if(i==="+")this.blockScalarKeep=!0;else if(i>"0"&&i<="9")this.blockScalarIndent=Number(i)-1;else if(i!=="-")break}return yield*this.pushUntil(i=>jt(i)||i==="#")}*parseBlockScalar(){let e=this.pos-1,i=0,r;e:for(let s=this.pos;r=this.buffer[s];++s)switch(r){case" ":i+=1;break;case`
`:e=s,i=0;break;case"\r":{let o=this.buffer[s+1];if(!o&&!this.atEnd)return this.setNext("block-scalar");if(o===`
`)break}default:break e}if(!r&&!this.atEnd)return this.setNext("block-scalar");if(i>=this.indentNext){this.blockScalarIndent===-1?this.indentNext=i:this.indentNext=this.blockScalarIndent+(this.indentNext===0?1:this.indentNext);do{let s=this.continueScalar(e+1);if(s===-1)break;e=this.buffer.indexOf(`
`,s)}while(e!==-1);if(e===-1){if(!this.atEnd)return this.setNext("block-scalar");e=this.buffer.length}}let n=e+1;for(r=this.buffer[n];r===" ";)r=this.buffer[++n];if(r==="	"){for(;r==="	"||r===" "||r==="\r"||r===`
`;)r=this.buffer[++n];e=n-1}else if(!this.blockScalarKeep)do{let s=e-1,o=this.buffer[s];o==="\r"&&(o=this.buffer[--s]);let a=s;for(;o===" ";)o=this.buffer[--s];if(o===`
`&&s>=this.pos&&s+1+i>a)e=s;else break}while(!0);return yield jn.SCALAR,yield*this.pushToIndex(e+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){let e=this.flowLevel>0,i=this.pos-1,r=this.pos-1,n;for(;n=this.buffer[++r];)if(n===":"){let s=this.buffer[r+1];if(jt(s)||e&&Ho.has(s))break;i=r}else if(jt(n)){let s=this.buffer[r+1];if(n==="\r"&&(s===`
`?(r+=1,n=`
`,s=this.buffer[r+1]):i=r),s==="#"||e&&Ho.has(s))break;if(n===`
`){let o=this.continueScalar(r+1);if(o===-1)break;r=Math.max(r,o-2)}}else{if(e&&Ho.has(n))break;i=r}return!n&&!this.atEnd?this.setNext("plain-scalar"):(yield jn.SCALAR,yield*this.pushToIndex(i+1,!0),e?"flow":"doc")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,i){let r=this.buffer.slice(this.pos,e);return r?(yield r,this.pos+=r.length,r.length):(i&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(Ru))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{let e=this.flowLevel>0,i=this.charAt(1);if(jt(i)||e&&Ho.has(i))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if(this.charAt(1)==="<"){let e=this.pos+2,i=this.buffer[e];for(;!jt(i)&&i!==">";)i=this.buffer[++e];return yield*this.pushToIndex(i===">"?e+1:e,!1)}else{let e=this.pos+1,i=this.buffer[e];for(;i;)if(hT.has(i))i=this.buffer[++e];else if(i==="%"&&_v.has(this.buffer[e+1])&&_v.has(this.buffer[e+2]))i=this.buffer[e+=3];else break;return yield*this.pushToIndex(e,!1)}}*pushNewline(){let e=this.buffer[this.pos];return e===`
`?yield*this.pushCount(1):e==="\r"&&this.charAt(1)===`
`?yield*this.pushCount(2):0}*pushSpaces(e){let i=this.pos-1,r;do r=this.buffer[++i];while(r===" "||e&&r==="	");let n=i-this.pos;return n>0&&(yield this.buffer.substr(this.pos,n),this.pos=i),n}*pushUntil(e){let i=this.pos,r=this.buffer[i];for(;!e(r);)r=this.buffer[++i];return yield*this.pushToIndex(i,!1)}};wv.Lexer=Pu});var qu=w(xv=>{"use strict";var Fu=class{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let i=0,r=this.lineStarts.length;for(;i<r;){let s=i+r>>1;this.lineStarts[s]<e?i=s+1:r=s}if(this.lineStarts[i]===e)return{line:i+1,col:1};if(i===0)return{line:0,col:e};let n=this.lineStarts[i-1];return{line:i,col:e-n+1}}}};xv.LineCounter=Fu});var ju=w(Cv=>{"use strict";var Sv=Vo(),dT=Mu();function ir(t,e){for(let i=0;i<t.length;++i)if(t[i].type===e)return!0;return!1}function Ev(t){for(let e=0;e<t.length;++e)switch(t[e].type){case"space":case"comment":case"newline":break;default:return e}return-1}function Ov(t){switch(t==null?void 0:t.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Go(t){var e;switch(t.type){case"document":return t.start;case"block-map":{let i=t.items[t.items.length-1];return(e=i.sep)!=null?e:i.start}case"block-seq":return t.items[t.items.length-1].start;default:return[]}}function Hr(t){var i;if(t.length===0)return[];let e=t.length;e:for(;--e>=0;)switch(t[e].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;((i=t[++e])==null?void 0:i.type)==="space";);return t.splice(e,t.length)}function kv(t){if(t.start.type==="flow-seq-start")for(let e of t.items)e.sep&&!e.value&&!ir(e.start,"explicit-key-ind")&&!ir(e.sep,"map-value-ind")&&(e.key&&(e.value=e.key),delete e.key,Ov(e.value)?e.value.end?Array.prototype.push.apply(e.value.end,e.sep):e.value.end=e.sep:Array.prototype.push.apply(e.start,e.sep),delete e.sep)}var Du=class{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new dT.Lexer,this.onNewLine=e}*parse(e,i=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(let r of this.lexer.lex(e,i))yield*this.next(r);i||(yield*this.end())}*next(e){if(this.source=e,process.env.LOG_TOKENS&&console.log("|",Sv.prettyToken(e)),this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=e.length;return}let i=Sv.tokenType(e);if(i)if(i==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=i,yield*this.step(),i){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&e[0]===" "&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{let r=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:r,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){let e=this.peek(1);if(this.type==="doc-end"&&(!e||e.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}peek(e){return this.stack[this.stack.length-e]}*pop(e){let i=e!=null?e:this.stack.pop();if(i)if(this.stack.length===0)yield i;else{let r=this.peek(1);switch(i.type==="block-scalar"?i.indent="indent"in r?r.indent:0:i.type==="flow-collection"&&r.type==="document"&&(i.indent=0),i.type==="flow-collection"&&kv(i),r.type){case"document":r.value=i;break;case"block-scalar":r.props.push(i);break;case"block-map":{let n=r.items[r.items.length-1];if(n.value){r.items.push({start:[],key:i,sep:[]}),this.onKeyLine=!0;return}else if(n.sep)n.value=i;else{Object.assign(n,{key:i,sep:[]}),this.onKeyLine=!n.explicitKey;return}break}case"block-seq":{let n=r.items[r.items.length-1];n.value?r.items.push({start:[],value:i}):n.value=i;break}case"flow-collection":{let n=r.items[r.items.length-1];!n||n.value?r.items.push({start:[],key:i,sep:[]}):n.sep?n.value=i:Object.assign(n,{key:i,sep:[]});return}default:yield*this.pop(),yield*this.pop(i)}if((r.type==="document"||r.type==="block-map"||r.type==="block-seq")&&(i.type==="block-map"||i.type==="block-seq")){let n=i.items[i.items.length-1];n&&!n.sep&&!n.value&&n.start.length>0&&Ev(n.start)===-1&&(i.indent===0||n.start.every(s=>s.type!=="comment"||s.indent<i.indent))&&(r.type==="document"?r.end=n.start:r.items.push({start:n.start}),i.items.splice(-1,1))}}else{let r="Tried to pop an empty stack";yield{type:"error",offset:this.offset,source:"",message:r}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{let e={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&e.start.push(this.sourceToken),this.stack.push(e);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":{Ev(e.start)!==-1?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":e.start.push(this.sourceToken);return}let i=this.startBlockValue(e);i?this.stack.push(i):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if(this.type==="map-value-ind"){let i=Go(this.peek(2)),r=Hr(i),n;e.end?(n=e.end,n.push(this.sourceToken),delete e.end):n=[this.sourceToken];let s={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:r,key:e,sep:n}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=s}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":e.props.push(this.sourceToken);return;case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let i=this.source.indexOf(`
`)+1;for(;i!==0;)this.onNewLine(this.offset+i),i=this.source.indexOf(`
`,i)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){var r;let i=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,i.value){let n="end"in i.value?i.value.end:void 0,s=Array.isArray(n)?n[n.length-1]:void 0;(s==null?void 0:s.type)==="comment"?n==null||n.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else i.sep?i.sep.push(this.sourceToken):i.start.push(this.sourceToken);return;case"space":case"comment":if(i.value)e.items.push({start:[this.sourceToken]});else if(i.sep)i.sep.push(this.sourceToken);else{if(this.atIndentedComment(i.start,e.indent)){let n=e.items[e.items.length-2],s=(r=n==null?void 0:n.value)==null?void 0:r.end;if(Array.isArray(s)){Array.prototype.push.apply(s,i.start),s.push(this.sourceToken),e.items.pop();return}}i.start.push(this.sourceToken)}return}if(this.indent>=e.indent){let n=!this.onKeyLine&&this.indent===e.indent,s=n&&(i.sep||i.explicitKey)&&this.type!=="seq-item-ind",o=[];if(s&&i.sep&&!i.value){let a=[];for(let l=0;l<i.sep.length;++l){let c=i.sep[l];switch(c.type){case"newline":a.push(l);break;case"space":break;case"comment":c.indent>e.indent&&(a.length=0);break;default:a.length=0}}a.length>=2&&(o=i.sep.splice(a[1]))}switch(this.type){case"anchor":case"tag":s||i.value?(o.push(this.sourceToken),e.items.push({start:o}),this.onKeyLine=!0):i.sep?i.sep.push(this.sourceToken):i.start.push(this.sourceToken);return;case"explicit-key-ind":!i.sep&&!i.explicitKey?(i.start.push(this.sourceToken),i.explicitKey=!0):s||i.value?(o.push(this.sourceToken),e.items.push({start:o,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(i.explicitKey)if(i.sep)if(i.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(ir(i.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:null,sep:[this.sourceToken]}]});else if(Ov(i.key)&&!ir(i.sep,"newline")){let a=Hr(i.start),l=i.key,c=i.sep;c.push(this.sourceToken),delete i.key,delete i.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:a,key:l,sep:c}]})}else o.length>0?i.sep=i.sep.concat(o,this.sourceToken):i.sep.push(this.sourceToken);else if(ir(i.start,"newline"))Object.assign(i,{key:null,sep:[this.sourceToken]});else{let a=Hr(i.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:a,key:null,sep:[this.sourceToken]}]})}else i.sep?i.value||s?e.items.push({start:o,key:null,sep:[this.sourceToken]}):ir(i.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):i.sep.push(this.sourceToken):Object.assign(i,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let a=this.flowScalar(this.type);s||i.value?(e.items.push({start:o,key:a,sep:[]}),this.onKeyLine=!0):i.sep?this.stack.push(a):(Object.assign(i,{key:a,sep:[]}),this.onKeyLine=!0);return}default:{let a=this.startBlockValue(e);if(a){n&&a.type!=="block-seq"&&e.items.push({start:o}),this.stack.push(a);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(e){var r;let i=e.items[e.items.length-1];switch(this.type){case"newline":if(i.value){let n="end"in i.value?i.value.end:void 0,s=Array.isArray(n)?n[n.length-1]:void 0;(s==null?void 0:s.type)==="comment"?n==null||n.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else i.start.push(this.sourceToken);return;case"space":case"comment":if(i.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(i.start,e.indent)){let n=e.items[e.items.length-2],s=(r=n==null?void 0:n.value)==null?void 0:r.end;if(Array.isArray(s)){Array.prototype.push.apply(s,i.start),s.push(this.sourceToken),e.items.pop();return}}i.start.push(this.sourceToken)}return;case"anchor":case"tag":if(i.value||this.indent<=e.indent)break;i.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==e.indent)break;i.value||ir(i.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):i.start.push(this.sourceToken);return}if(this.indent>e.indent){let n=this.startBlockValue(e);if(n){this.stack.push(n);return}}yield*this.pop(),yield*this.step()}*flowCollection(e){let i=e.items[e.items.length-1];if(this.type==="flow-error-end"){let r;do yield*this.pop(),r=this.peek(1);while(r&&r.type==="flow-collection")}else if(e.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!i||i.sep?e.items.push({start:[this.sourceToken]}):i.start.push(this.sourceToken);return;case"map-value-ind":!i||i.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):i.sep?i.sep.push(this.sourceToken):Object.assign(i,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!i||i.value?e.items.push({start:[this.sourceToken]}):i.sep?i.sep.push(this.sourceToken):i.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let n=this.flowScalar(this.type);!i||i.value?e.items.push({start:[],key:n,sep:[]}):i.sep?this.stack.push(n):Object.assign(i,{key:n,sep:[]});return}case"flow-map-end":case"flow-seq-end":e.end.push(this.sourceToken);return}let r=this.startBlockValue(e);r?this.stack.push(r):(yield*this.pop(),yield*this.step())}else{let r=this.peek(2);if(r.type==="block-map"&&(this.type==="map-value-ind"&&r.indent===e.indent||this.type==="newline"&&!r.items[r.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&r.type!=="flow-collection"){let n=Go(r),s=Hr(n);kv(e);let o=e.end.splice(1,e.end.length);o.push(this.sourceToken);let a={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:s,key:e,sep:o}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=a}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let i=this.source.indexOf(`
`)+1;for(;i!==0;)this.onNewLine(this.offset+i),i=this.source.indexOf(`
`,i)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;let i=Go(e),r=Hr(i);return r.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;let i=Go(e),r=Hr(i);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,i){return this.type!=="comment"||this.indent<=i?!1:e.every(r=>r.type==="newline"||r.type==="space")}*documentEnd(e){this.type!=="doc-mode"&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}};Cv.Parser=Du});var Lv=w($n=>{"use strict";var Tv=Ou(),mT=Bn(),Un=Mn(),gT=Ec(),vT=Se(),yT=qu(),Av=ju();function Iv(t){let e=t.prettyErrors!==!1;return{lineCounter:t.lineCounter||e&&new yT.LineCounter||null,prettyErrors:e}}function bT(t,e={}){let{lineCounter:i,prettyErrors:r}=Iv(e),n=new Av.Parser(i==null?void 0:i.addNewLine),s=new Tv.Composer(e),o=Array.from(s.compose(n.parse(t)));if(r&&i)for(let a of o)a.errors.forEach(Un.prettifyError(t,i)),a.warnings.forEach(Un.prettifyError(t,i));return o.length>0?o:Object.assign([],{empty:!0},s.streamInfo())}function Nv(t,e={}){let{lineCounter:i,prettyErrors:r}=Iv(e),n=new Av.Parser(i==null?void 0:i.addNewLine),s=new Tv.Composer(e),o=null;for(let a of s.compose(n.parse(t),!0,t.length))if(!o)o=a;else if(o.options.logLevel!=="silent"){o.errors.push(new Un.YAMLParseError(a.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return r&&i&&(o.errors.forEach(Un.prettifyError(t,i)),o.warnings.forEach(Un.prettifyError(t,i))),o}function _T(t,e,i){let r;typeof e=="function"?r=e:i===void 0&&e&&typeof e=="object"&&(i=e);let n=Nv(t,i);if(!n)return null;if(n.warnings.forEach(s=>gT.warn(n.options.logLevel,s)),n.errors.length>0){if(n.options.logLevel!=="silent")throw n.errors[0];n.errors=[]}return n.toJS(Object.assign({reviver:r},i))}function wT(t,e,i){var n;let r=null;if(typeof e=="function"||Array.isArray(e)?r=e:i===void 0&&e&&(i=e),typeof i=="string"&&(i=i.length),typeof i=="number"){let s=Math.round(i);i=s<1?void 0:s>8?{indent:8}:{indent:s}}if(t===void 0){let{keepUndefined:s}=(n=i!=null?i:e)!=null?n:{};if(!s)return}return vT.isDocument(t)&&!r?t.toString(i):new mT.Document(t,r,i).toString(i)}$n.parse=_T;$n.parseAllDocuments=bT;$n.parseDocument=Nv;$n.stringify=wT});var Rv=w(Oe=>{"use strict";var xT=Ou(),ST=Bn(),ET=ou(),Uu=Mn(),kT=vn(),ki=Se(),OT=wi(),CT=je(),TT=Si(),AT=Ei(),IT=Vo(),NT=Mu(),LT=qu(),BT=ju(),Yo=Lv(),Bv=pn();Oe.Composer=xT.Composer;Oe.Document=ST.Document;Oe.Schema=ET.Schema;Oe.YAMLError=Uu.YAMLError;Oe.YAMLParseError=Uu.YAMLParseError;Oe.YAMLWarning=Uu.YAMLWarning;Oe.Alias=kT.Alias;Oe.isAlias=ki.isAlias;Oe.isCollection=ki.isCollection;Oe.isDocument=ki.isDocument;Oe.isMap=ki.isMap;Oe.isNode=ki.isNode;Oe.isPair=ki.isPair;Oe.isScalar=ki.isScalar;Oe.isSeq=ki.isSeq;Oe.Pair=OT.Pair;Oe.Scalar=CT.Scalar;Oe.YAMLMap=TT.YAMLMap;Oe.YAMLSeq=AT.YAMLSeq;Oe.CST=IT;Oe.Lexer=NT.Lexer;Oe.LineCounter=LT.LineCounter;Oe.Parser=BT.Parser;Oe.parse=Yo.parse;Oe.parseAllDocuments=Yo.parseAllDocuments;Oe.parseDocument=Yo.parseDocument;Oe.stringify=Yo.stringify;Oe.visit=Bv.visit;Oe.visitAsync=Bv.visitAsync});var qv=w((OL,Fv)=>{"use strict";var{Duplex:RT}=require("stream");function Pv(t){t.emit("close")}function PT(){!this.destroyed&&this._writableState.finished&&this.destroy()}function Mv(t){this.removeListener("error",Mv),this.destroy(),this.listenerCount("error")===0&&this.emit("error",t)}function MT(t,e){let i=!0,r=new RT({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return t.on("message",function(s,o){let a=!o&&r._readableState.objectMode?s.toString():s;r.push(a)||t.pause()}),t.once("error",function(s){r.destroyed||(i=!1,r.destroy(s))}),t.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(n,s){if(t.readyState===t.CLOSED){s(n),process.nextTick(Pv,r);return}let o=!1;t.once("error",function(l){o=!0,s(l)}),t.once("close",function(){o||s(n),process.nextTick(Pv,r)}),i&&t.terminate()},r._final=function(n){if(t.readyState===t.CONNECTING){t.once("open",function(){r._final(n)});return}t._socket!==null&&(t._socket._writableState.finished?(n(),r._readableState.endEmitted&&r.destroy()):(t._socket.once("finish",function(){n()}),t.close()))},r._read=function(){t.isPaused&&t.resume()},r._write=function(n,s,o){if(t.readyState===t.CONNECTING){t.once("open",function(){r._write(n,s,o)});return}t.send(n,o)},r.on("end",PT),r.on("error",Mv),r}Fv.exports=MT});var Oi=w((CL,Dv)=>{"use strict";Dv.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var Vn=w((TL,Wo)=>{"use strict";var{EMPTY_BUFFER:FT}=Oi(),$u=Buffer[Symbol.species];function qT(t,e){if(t.length===0)return FT;if(t.length===1)return t[0];let i=Buffer.allocUnsafe(e),r=0;for(let n=0;n<t.length;n++){let s=t[n];i.set(s,r),r+=s.length}return r<e?new $u(i.buffer,i.byteOffset,r):i}function jv(t,e,i,r,n){for(let s=0;s<n;s++)i[r+s]=t[s]^e[s&3]}function Uv(t,e){for(let i=0;i<t.length;i++)t[i]^=e[i&3]}function DT(t){return t.length===t.buffer.byteLength?t.buffer:t.buffer.slice(t.byteOffset,t.byteOffset+t.length)}function Vu(t){if(Vu.readOnly=!0,Buffer.isBuffer(t))return t;let e;return t instanceof ArrayBuffer?e=new $u(t):ArrayBuffer.isView(t)?e=new $u(t.buffer,t.byteOffset,t.byteLength):(e=Buffer.from(t),Vu.readOnly=!1),e}Wo.exports={concat:qT,mask:jv,toArrayBuffer:DT,toBuffer:Vu,unmask:Uv};if(!process.env.WS_NO_BUFFER_UTIL)try{let t=require("bufferutil");Wo.exports.mask=function(e,i,r,n,s){s<48?jv(e,i,r,n,s):t.mask(e,i,r,n,s)},Wo.exports.unmask=function(e,i){e.length<32?Uv(e,i):t.unmask(e,i)}}catch{}});var Hv=w((AL,Vv)=>{"use strict";var $v=Symbol("kDone"),Hu=Symbol("kRun"),Gu=class{constructor(e){this[$v]=()=>{this.pending--,this[Hu]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Hu]()}[Hu](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[$v])}}};Vv.exports=Gu});var Yn=w((IL,zv)=>{"use strict";var Hn=require("zlib"),Gv=Vn(),jT=Hv(),{kStatusCode:Yv}=Oi(),UT=Buffer[Symbol.species],$T=Buffer.from([0,0,255,255]),Jo=Symbol("permessage-deflate"),ai=Symbol("total-length"),Gn=Symbol("callback"),Ci=Symbol("buffers"),Ko=Symbol("error"),zo,Yu=class{constructor(e,i,r){if(this._maxPayload=r|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!i,this._deflate=null,this._inflate=null,this.params=null,!zo){let n=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;zo=new jT(n)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[Gn];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let i=this._options,r=e.find(n=>!(i.serverNoContextTakeover===!1&&n.server_no_context_takeover||n.server_max_window_bits&&(i.serverMaxWindowBits===!1||typeof i.serverMaxWindowBits=="number"&&i.serverMaxWindowBits>n.server_max_window_bits)||typeof i.clientMaxWindowBits=="number"&&!n.client_max_window_bits));if(!r)throw new Error("None of the extension offers can be accepted");return i.serverNoContextTakeover&&(r.server_no_context_takeover=!0),i.clientNoContextTakeover&&(r.client_no_context_takeover=!0),typeof i.serverMaxWindowBits=="number"&&(r.server_max_window_bits=i.serverMaxWindowBits),typeof i.clientMaxWindowBits=="number"?r.client_max_window_bits=i.clientMaxWindowBits:(r.client_max_window_bits===!0||i.clientMaxWindowBits===!1)&&delete r.client_max_window_bits,r}acceptAsClient(e){let i=e[0];if(this._options.clientNoContextTakeover===!1&&i.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!i.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(i.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&i.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return i}normalizeParams(e){return e.forEach(i=>{Object.keys(i).forEach(r=>{let n=i[r];if(n.length>1)throw new Error(`Parameter "${r}" must have only a single value`);if(n=n[0],r==="client_max_window_bits"){if(n!==!0){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else if(r==="server_max_window_bits"){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(r==="client_no_context_takeover"||r==="server_no_context_takeover"){if(n!==!0)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else throw new Error(`Unknown parameter "${r}"`);i[r]=n})}),e}decompress(e,i,r){zo.add(n=>{this._decompress(e,i,(s,o)=>{n(),r(s,o)})})}compress(e,i,r){zo.add(n=>{this._compress(e,i,(s,o)=>{n(),r(s,o)})})}_decompress(e,i,r){let n=this._isServer?"client":"server";if(!this._inflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Hn.Z_DEFAULT_WINDOWBITS:this.params[s];this._inflate=Hn.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[Jo]=this,this._inflate[ai]=0,this._inflate[Ci]=[],this._inflate.on("error",HT),this._inflate.on("data",Wv)}this._inflate[Gn]=r,this._inflate.write(e),i&&this._inflate.write($T),this._inflate.flush(()=>{let s=this._inflate[Ko];if(s){this._inflate.close(),this._inflate=null,r(s);return}let o=Gv.concat(this._inflate[Ci],this._inflate[ai]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[ai]=0,this._inflate[Ci]=[],i&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,o)})}_compress(e,i,r){let n=this._isServer?"server":"client";if(!this._deflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Hn.Z_DEFAULT_WINDOWBITS:this.params[s];this._deflate=Hn.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[ai]=0,this._deflate[Ci]=[],this._deflate.on("data",VT)}this._deflate[Gn]=r,this._deflate.write(e),this._deflate.flush(Hn.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let s=Gv.concat(this._deflate[Ci],this._deflate[ai]);i&&(s=new UT(s.buffer,s.byteOffset,s.length-4)),this._deflate[Gn]=null,this._deflate[ai]=0,this._deflate[Ci]=[],i&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,s)})}};zv.exports=Yu;function VT(t){this[Ci].push(t),this[ai]+=t.length}function Wv(t){if(this[ai]+=t.length,this[Jo]._maxPayload<1||this[ai]<=this[Jo]._maxPayload){this[Ci].push(t);return}this[Ko]=new RangeError("Max payload size exceeded"),this[Ko].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[Ko][Yv]=1009,this.removeListener("data",Wv),this.reset()}function HT(t){this[Jo]._inflate=null,t[Yv]=1007,this[Gn](t)}});var Wn=w((NL,Zo)=>{"use strict";var{isUtf8:Kv}=require("buffer"),GT=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function YT(t){return t>=1e3&&t<=1014&&t!==1004&&t!==1005&&t!==1006||t>=3e3&&t<=4999}function Wu(t){let e=t.length,i=0;for(;i<e;)if(!(t[i]&128))i++;else if((t[i]&224)===192){if(i+1===e||(t[i+1]&192)!==128||(t[i]&254)===192)return!1;i+=2}else if((t[i]&240)===224){if(i+2>=e||(t[i+1]&192)!==128||(t[i+2]&192)!==128||t[i]===224&&(t[i+1]&224)===128||t[i]===237&&(t[i+1]&224)===160)return!1;i+=3}else if((t[i]&248)===240){if(i+3>=e||(t[i+1]&192)!==128||(t[i+2]&192)!==128||(t[i+3]&192)!==128||t[i]===240&&(t[i+1]&240)===128||t[i]===244&&t[i+1]>143||t[i]>244)return!1;i+=4}else return!1;return!0}Zo.exports={isValidStatusCode:YT,isValidUTF8:Wu,tokenChars:GT};if(Kv)Zo.exports.isValidUTF8=function(t){return t.length<24?Wu(t):Kv(t)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=require("utf-8-validate");Zo.exports.isValidUTF8=function(e){return e.length<32?Wu(e):t(e)}}catch{}});var Qu=w((LL,iy)=>{"use strict";var{Writable:WT}=require("stream"),Jv=Yn(),{BINARY_TYPES:zT,EMPTY_BUFFER:Zv,kStatusCode:KT,kWebSocket:JT}=Oi(),{concat:zu,toArrayBuffer:ZT,unmask:QT}=Vn(),{isValidStatusCode:XT,isValidUTF8:Qv}=Wn(),Qo=Buffer[Symbol.species],It=0,Xv=1,ey=2,ty=3,Ku=4,Ju=5,Xo=6,Zu=class extends WT{constructor(e={}){super(),this._allowSynchronousEvents=e.allowSynchronousEvents!==void 0?e.allowSynchronousEvents:!0,this._binaryType=e.binaryType||zT[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[JT]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=It}_write(e,i,r){if(this._opcode===8&&this._state==It)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let r=this._buffers[0];return this._buffers[0]=new Qo(r.buffer,r.byteOffset+e,r.length-e),new Qo(r.buffer,r.byteOffset,e)}let i=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=i.length-e;e>=r.length?i.set(this._buffers.shift(),n):(i.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=new Qo(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return i}startLoop(e){this._loop=!0;do switch(this._state){case It:this.getInfo(e);break;case Xv:this.getPayloadLength16(e);break;case ey:this.getPayloadLength64(e);break;case ty:this.getMask();break;case Ku:this.getData(e);break;case Ju:case Xo:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let i=this.consume(2);if(i[0]&48){let n=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");e(n);return}let r=(i[0]&64)===64;if(r&&!this._extensions[Jv.extensionName]){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(this._fin=(i[0]&128)===128,this._opcode=i[0]&15,this._payloadLength=i[1]&127,this._opcode===0){if(r){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(!this._fragmented){let n=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let n=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}this._compressed=r}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let n=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");e(n);return}if(r){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let n=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");e(n);return}}else{let n=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(i[1]&128)===128,this._isServer){if(!this._masked){let n=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");e(n);return}}else if(this._masked){let n=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");e(n);return}this._payloadLength===126?this._state=Xv:this._payloadLength===127?this._state=ey:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let i=this.consume(8),r=i.readUInt32BE(0);if(r>Math.pow(2,53-32)-1){let n=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");e(n);return}this._payloadLength=r*Math.pow(2,32)+i.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){let i=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");e(i);return}this._masked?this._state=ty:this._state=Ku}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Ku}getData(e){let i=Zv;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}i=this.consume(this._payloadLength),this._masked&&this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3]&&QT(i,this._mask)}if(this._opcode>7){this.controlMessage(i,e);return}if(this._compressed){this._state=Ju,this.decompress(i,e);return}i.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(i)),this.dataMessage(e)}decompress(e,i){this._extensions[Jv.extensionName].decompress(e,this._fin,(n,s)=>{if(n)return i(n);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let o=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");i(o);return}this._fragments.push(s)}this.dataMessage(i),this._state===It&&this.startLoop(i)})}dataMessage(e){if(!this._fin){this._state=It;return}let i=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let n;this._binaryType==="nodebuffer"?n=zu(r,i):this._binaryType==="arraybuffer"?n=ZT(zu(r,i)):n=r,this._allowSynchronousEvents?(this.emit("message",n,!0),this._state=It):(this._state=Xo,setImmediate(()=>{this.emit("message",n,!0),this._state=It,this.startLoop(e)}))}else{let n=zu(r,i);if(!this._skipUTF8Validation&&!Qv(n)){let s=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");e(s);return}this._state===Ju||this._allowSynchronousEvents?(this.emit("message",n,!1),this._state=It):(this._state=Xo,setImmediate(()=>{this.emit("message",n,!1),this._state=It,this.startLoop(e)}))}}controlMessage(e,i){if(this._opcode===8){if(e.length===0)this._loop=!1,this.emit("conclude",1005,Zv),this.end();else{let r=e.readUInt16BE(0);if(!XT(r)){let s=this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");i(s);return}let n=new Qo(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!Qv(n)){let s=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");i(s);return}this._loop=!1,this.emit("conclude",r,n),this.end()}this._state=It;return}this._allowSynchronousEvents?(this.emit(this._opcode===9?"ping":"pong",e),this._state=It):(this._state=Xo,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",e),this._state=It,this.startLoop(i)}))}createError(e,i,r,n,s){this._loop=!1,this._errored=!0;let o=new e(r?`Invalid WebSocket frame: ${i}`:i);return Error.captureStackTrace(o,this.createError),o.code=s,o[KT]=n,o}};iy.exports=Zu});var ef=w((RL,sy)=>{"use strict";var{Duplex:BL}=require("stream"),{randomFillSync:eA}=require("crypto"),ry=Yn(),{EMPTY_BUFFER:tA}=Oi(),{isValidStatusCode:iA}=Wn(),{mask:ny,toBuffer:Gr}=Vn(),Ut=Symbol("kByteLength"),rA=Buffer.alloc(4),ea=8*1024,rr,Yr=ea,Xu=class t{constructor(e,i,r){this._extensions=i||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,i){let r,n=!1,s=2,o=!1;i.mask&&(r=i.maskBuffer||rA,i.generateMask?i.generateMask(r):(Yr===ea&&(rr===void 0&&(rr=Buffer.alloc(ea)),eA(rr,0,ea),Yr=0),r[0]=rr[Yr++],r[1]=rr[Yr++],r[2]=rr[Yr++],r[3]=rr[Yr++]),o=(r[0]|r[1]|r[2]|r[3])===0,s=6);let a;typeof e=="string"?(!i.mask||o)&&i[Ut]!==void 0?a=i[Ut]:(e=Buffer.from(e),a=e.length):(a=e.length,n=i.mask&&i.readOnly&&!o);let l=a;a>=65536?(s+=8,l=127):a>125&&(s+=2,l=126);let c=Buffer.allocUnsafe(n?a+s:s);return c[0]=i.fin?i.opcode|128:i.opcode,i.rsv1&&(c[0]|=64),c[1]=l,l===126?c.writeUInt16BE(a,2):l===127&&(c[2]=c[3]=0,c.writeUIntBE(a,4,6)),i.mask?(c[1]|=128,c[s-4]=r[0],c[s-3]=r[1],c[s-2]=r[2],c[s-1]=r[3],o?[c,e]:n?(ny(e,r,c,s,a),[c]):(ny(e,r,e,0,a),[c,e])):[c,e]}close(e,i,r,n){let s;if(e===void 0)s=tA;else{if(typeof e!="number"||!iA(e))throw new TypeError("First argument must be a valid error code number");if(i===void 0||!i.length)s=Buffer.allocUnsafe(2),s.writeUInt16BE(e,0);else{let a=Buffer.byteLength(i);if(a>123)throw new RangeError("The message must not be greater than 123 bytes");s=Buffer.allocUnsafe(2+a),s.writeUInt16BE(e,0),typeof i=="string"?s.write(i,2):s.set(i,2)}}let o={[Ut]:s.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,o,n]):this.sendFrame(t.frame(s,o),n)}ping(e,i,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=Gr(e),n=e.length,s=Gr.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[Ut]:n,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(t.frame(e,o),r)}pong(e,i,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=Gr(e),n=e.length,s=Gr.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[Ut]:n,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(t.frame(e,o),r)}send(e,i,r){let n=this._extensions[ry.extensionName],s=i.binary?2:1,o=i.compress,a,l;if(typeof e=="string"?(a=Buffer.byteLength(e),l=!1):(e=Gr(e),a=e.length,l=Gr.readOnly),this._firstFragment?(this._firstFragment=!1,o&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=a>=n._threshold),this._compress=o):(o=!1,s=0),i.fin&&(this._firstFragment=!0),n){let c={[Ut]:a,fin:i.fin,generateMask:this._generateMask,mask:i.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:l,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,c,r]):this.dispatch(e,this._compress,c,r)}else this.sendFrame(t.frame(e,{[Ut]:a,fin:i.fin,generateMask:this._generateMask,mask:i.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:l,rsv1:!1}),r)}dispatch(e,i,r,n){if(!i){this.sendFrame(t.frame(e,r),n);return}let s=this._extensions[ry.extensionName];this._bufferedBytes+=r[Ut],this._deflating=!0,s.compress(e,r.fin,(o,a)=>{if(this._socket.destroyed){let l=new Error("The socket was closed while data was being compressed");typeof n=="function"&&n(l);for(let c=0;c<this._queue.length;c++){let u=this._queue[c],f=u[u.length-1];typeof f=="function"&&f(l)}return}this._bufferedBytes-=r[Ut],this._deflating=!1,r.readOnly=!1,this.sendFrame(t.frame(a,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][Ut],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][Ut],this._queue.push(e)}sendFrame(e,i){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],i),this._socket.uncork()):this._socket.write(e[0],i)}};sy.exports=Xu});var dy=w((PL,py)=>{"use strict";var{kForOnEventAttribute:zn,kListener:tf}=Oi(),oy=Symbol("kCode"),ay=Symbol("kData"),ly=Symbol("kError"),cy=Symbol("kMessage"),uy=Symbol("kReason"),Wr=Symbol("kTarget"),fy=Symbol("kType"),hy=Symbol("kWasClean"),li=class{constructor(e){this[Wr]=null,this[fy]=e}get target(){return this[Wr]}get type(){return this[fy]}};Object.defineProperty(li.prototype,"target",{enumerable:!0});Object.defineProperty(li.prototype,"type",{enumerable:!0});var nr=class extends li{constructor(e,i={}){super(e),this[oy]=i.code===void 0?0:i.code,this[uy]=i.reason===void 0?"":i.reason,this[hy]=i.wasClean===void 0?!1:i.wasClean}get code(){return this[oy]}get reason(){return this[uy]}get wasClean(){return this[hy]}};Object.defineProperty(nr.prototype,"code",{enumerable:!0});Object.defineProperty(nr.prototype,"reason",{enumerable:!0});Object.defineProperty(nr.prototype,"wasClean",{enumerable:!0});var zr=class extends li{constructor(e,i={}){super(e),this[ly]=i.error===void 0?null:i.error,this[cy]=i.message===void 0?"":i.message}get error(){return this[ly]}get message(){return this[cy]}};Object.defineProperty(zr.prototype,"error",{enumerable:!0});Object.defineProperty(zr.prototype,"message",{enumerable:!0});var Kn=class extends li{constructor(e,i={}){super(e),this[ay]=i.data===void 0?null:i.data}get data(){return this[ay]}};Object.defineProperty(Kn.prototype,"data",{enumerable:!0});var nA={addEventListener(t,e,i={}){for(let n of this.listeners(t))if(!i[zn]&&n[tf]===e&&!n[zn])return;let r;if(t==="message")r=function(s,o){let a=new Kn("message",{data:o?s:s.toString()});a[Wr]=this,ta(e,this,a)};else if(t==="close")r=function(s,o){let a=new nr("close",{code:s,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});a[Wr]=this,ta(e,this,a)};else if(t==="error")r=function(s){let o=new zr("error",{error:s,message:s.message});o[Wr]=this,ta(e,this,o)};else if(t==="open")r=function(){let s=new li("open");s[Wr]=this,ta(e,this,s)};else return;r[zn]=!!i[zn],r[tf]=e,i.once?this.once(t,r):this.on(t,r)},removeEventListener(t,e){for(let i of this.listeners(t))if(i[tf]===e&&!i[zn]){this.removeListener(t,i);break}}};py.exports={CloseEvent:nr,ErrorEvent:zr,Event:li,EventTarget:nA,MessageEvent:Kn};function ta(t,e,i){typeof t=="object"&&t.handleEvent?t.handleEvent.call(t,i):t.call(e,i)}});var rf=w((ML,my)=>{"use strict";var{tokenChars:Jn}=Wn();function Zt(t,e,i){t[e]===void 0?t[e]=[i]:t[e].push(i)}function sA(t){let e=Object.create(null),i=Object.create(null),r=!1,n=!1,s=!1,o,a,l=-1,c=-1,u=-1,f=0;for(;f<t.length;f++)if(c=t.charCodeAt(f),o===void 0)if(u===-1&&Jn[c]===1)l===-1&&(l=f);else if(f!==0&&(c===32||c===9))u===-1&&l!==-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let g=t.slice(l,u);c===44?(Zt(e,g,i),i=Object.create(null)):o=g,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);else if(a===void 0)if(u===-1&&Jn[c]===1)l===-1&&(l=f);else if(c===32||c===9)u===-1&&l!==-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f),Zt(i,t.slice(l,u),!0),c===44&&(Zt(e,o,i),i=Object.create(null),o=void 0),l=u=-1}else if(c===61&&l!==-1&&u===-1)a=t.slice(l,f),l=u=-1;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(n){if(Jn[c]!==1)throw new SyntaxError(`Unexpected character at index ${f}`);l===-1?l=f:r||(r=!0),n=!1}else if(s)if(Jn[c]===1)l===-1&&(l=f);else if(c===34&&l!==-1)s=!1,u=f;else if(c===92)n=!0;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(c===34&&t.charCodeAt(f-1)===61)s=!0;else if(u===-1&&Jn[c]===1)l===-1&&(l=f);else if(l!==-1&&(c===32||c===9))u===-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let g=t.slice(l,u);r&&(g=g.replace(/\\/g,""),r=!1),Zt(i,a,g),c===44&&(Zt(e,o,i),i=Object.create(null),o=void 0),a=void 0,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);if(l===-1||s||c===32||c===9)throw new SyntaxError("Unexpected end of input");u===-1&&(u=f);let d=t.slice(l,u);return o===void 0?Zt(e,d,i):(a===void 0?Zt(i,d,!0):r?Zt(i,a,d.replace(/\\/g,"")):Zt(i,a,d),Zt(e,o,i)),e}function oA(t){return Object.keys(t).map(e=>{let i=t[e];return Array.isArray(i)||(i=[i]),i.map(r=>[e].concat(Object.keys(r).map(n=>{let s=r[n];return Array.isArray(s)||(s=[s]),s.map(o=>o===!0?n:`${n}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}my.exports={format:oA,parse:sA}});var lf=w((DL,Oy)=>{"use strict";var aA=require("events"),lA=require("https"),cA=require("http"),yy=require("net"),uA=require("tls"),{randomBytes:fA,createHash:hA}=require("crypto"),{Duplex:FL,Readable:qL}=require("stream"),{URL:nf}=require("url"),Ti=Yn(),pA=Qu(),dA=ef(),{BINARY_TYPES:gy,EMPTY_BUFFER:ia,GUID:mA,kForOnEventAttribute:sf,kListener:gA,kStatusCode:vA,kWebSocket:nt,NOOP:by}=Oi(),{EventTarget:{addEventListener:yA,removeEventListener:bA}}=dy(),{format:_A,parse:wA}=rf(),{toBuffer:xA}=Vn(),SA=30*1e3,_y=Symbol("kAborted"),of=[8,13],ci=["CONNECTING","OPEN","CLOSING","CLOSED"],EA=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,qe=class t extends aA{constructor(e,i,r){super(),this._binaryType=gy[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=ia,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=t.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,i===void 0?i=[]:Array.isArray(i)||(typeof i=="object"&&i!==null?(r=i,i=[]):i=[i]),wy(this,e,i,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){gy.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,i,r){let n=new pA({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new dA(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[nt]=this,e[nt]=this,n.on("conclude",CA),n.on("drain",TA),n.on("error",AA),n.on("message",IA),n.on("ping",NA),n.on("pong",LA),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),i.length>0&&e.unshift(i),e.on("close",Sy),e.on("data",na),e.on("end",Ey),e.on("error",ky),this._readyState=t.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[Ti.extensionName]&&this._extensions[Ti.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,i){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){let r="WebSocket was closed before the connection was established";St(this,this._req,r);return}if(this.readyState===t.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=t.CLOSING,this._sender.close(e,i,!this._isServer,r=>{r||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),SA)}}pause(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=i=void 0):typeof i=="function"&&(r=i,i=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){af(this,e,r);return}i===void 0&&(i=!this._isServer),this._sender.ping(e||ia,i,r)}pong(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=i=void 0):typeof i=="function"&&(r=i,i=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){af(this,e,r);return}i===void 0&&(i=!this._isServer),this._sender.pong(e||ia,i,r)}resume(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof i=="function"&&(r=i,i={}),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){af(this,e,r);return}let n={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...i};this._extensions[Ti.extensionName]||(n.compress=!1),this._sender.send(e||ia,n,r)}terminate(){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){let e="WebSocket was closed before the connection was established";St(this,this._req,e);return}this._socket&&(this._readyState=t.CLOSING,this._socket.destroy())}}};Object.defineProperty(qe,"CONNECTING",{enumerable:!0,value:ci.indexOf("CONNECTING")});Object.defineProperty(qe.prototype,"CONNECTING",{enumerable:!0,value:ci.indexOf("CONNECTING")});Object.defineProperty(qe,"OPEN",{enumerable:!0,value:ci.indexOf("OPEN")});Object.defineProperty(qe.prototype,"OPEN",{enumerable:!0,value:ci.indexOf("OPEN")});Object.defineProperty(qe,"CLOSING",{enumerable:!0,value:ci.indexOf("CLOSING")});Object.defineProperty(qe.prototype,"CLOSING",{enumerable:!0,value:ci.indexOf("CLOSING")});Object.defineProperty(qe,"CLOSED",{enumerable:!0,value:ci.indexOf("CLOSED")});Object.defineProperty(qe.prototype,"CLOSED",{enumerable:!0,value:ci.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(t=>{Object.defineProperty(qe.prototype,t,{enumerable:!0})});["open","error","close","message"].forEach(t=>{Object.defineProperty(qe.prototype,`on${t}`,{enumerable:!0,get(){for(let e of this.listeners(t))if(e[sf])return e[gA];return null},set(e){for(let i of this.listeners(t))if(i[sf]){this.removeListener(t,i);break}typeof e=="function"&&this.addEventListener(t,e,{[sf]:!0})}})});qe.prototype.addEventListener=yA;qe.prototype.removeEventListener=bA;Oy.exports=qe;function wy(t,e,i,r){let n={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:of[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...r,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=n.autoPong,!of.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${of.join(", ")})`);let s;if(e instanceof nf)s=e;else try{s=new nf(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}s.protocol==="http:"?s.protocol="ws:":s.protocol==="https:"&&(s.protocol="wss:"),t._url=s.href;let o=s.protocol==="wss:",a=s.protocol==="ws+unix:",l;if(s.protocol!=="ws:"&&!o&&!a?l=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`:a&&!s.pathname?l="The URL's pathname is empty":s.hash&&(l="The URL contains a fragment identifier"),l){let v=new SyntaxError(l);if(t._redirects===0)throw v;ra(t,v);return}let c=o?443:80,u=fA(16).toString("base64"),f=o?lA.request:cA.request,d=new Set,g;if(n.createConnection=n.createConnection||(o?OA:kA),n.defaultPort=n.defaultPort||c,n.port=s.port||c,n.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,n.headers={...n.headers,"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":u,Connection:"Upgrade",Upgrade:"websocket"},n.path=s.pathname+s.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(g=new Ti(n.perMessageDeflate!==!0?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=_A({[Ti.extensionName]:g.offer()})),i.length){for(let v of i){if(typeof v!="string"||!EA.test(v)||d.has(v))throw new SyntaxError("An invalid or duplicated subprotocol was specified");d.add(v)}n.headers["Sec-WebSocket-Protocol"]=i.join(",")}if(n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(s.username||s.password)&&(n.auth=`${s.username}:${s.password}`),a){let v=n.path.split(":");n.socketPath=v[0],n.path=v[1]}let m;if(n.followRedirects){if(t._redirects===0){t._originalIpc=a,t._originalSecure=o,t._originalHostOrSocketPath=a?n.socketPath:s.host;let v=r&&r.headers;if(r={...r,headers:{}},v)for(let[b,_]of Object.entries(v))r.headers[b.toLowerCase()]=_}else if(t.listenerCount("redirect")===0){let v=a?t._originalIpc?n.socketPath===t._originalHostOrSocketPath:!1:t._originalIpc?!1:s.host===t._originalHostOrSocketPath;(!v||t._originalSecure&&!o)&&(delete n.headers.authorization,delete n.headers.cookie,v||delete n.headers.host,n.auth=void 0)}n.auth&&!r.headers.authorization&&(r.headers.authorization="Basic "+Buffer.from(n.auth).toString("base64")),m=t._req=f(n),t._redirects&&t.emit("redirect",t.url,m)}else m=t._req=f(n);n.timeout&&m.on("timeout",()=>{St(t,m,"Opening handshake has timed out")}),m.on("error",v=>{m===null||m[_y]||(m=t._req=null,ra(t,v))}),m.on("response",v=>{let b=v.headers.location,_=v.statusCode;if(b&&n.followRedirects&&_>=300&&_<400){if(++t._redirects>n.maxRedirects){St(t,m,"Maximum redirects exceeded");return}m.abort();let S;try{S=new nf(b,e)}catch{let k=new SyntaxError(`Invalid URL: ${b}`);ra(t,k);return}wy(t,S,i,r)}else t.emit("unexpected-response",m,v)||St(t,m,`Unexpected server response: ${v.statusCode}`)}),m.on("upgrade",(v,b,_)=>{if(t.emit("upgrade",v),t.readyState!==qe.CONNECTING)return;m=t._req=null;let S=v.headers.upgrade;if(S===void 0||S.toLowerCase()!=="websocket"){St(t,b,"Invalid Upgrade header");return}let O=hA("sha1").update(u+mA).digest("base64");if(v.headers["sec-websocket-accept"]!==O){St(t,b,"Invalid Sec-WebSocket-Accept header");return}let k=v.headers["sec-websocket-protocol"],E;if(k!==void 0?d.size?d.has(k)||(E="Server sent an invalid subprotocol"):E="Server sent a subprotocol but none was requested":d.size&&(E="Server sent no subprotocol"),E){St(t,b,E);return}k&&(t._protocol=k);let R=v.headers["sec-websocket-extensions"];if(R!==void 0){if(!g){St(t,b,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let T;try{T=wA(R)}catch{St(t,b,"Invalid Sec-WebSocket-Extensions header");return}let A=Object.keys(T);if(A.length!==1||A[0]!==Ti.extensionName){St(t,b,"Server indicated an extension that was not requested");return}try{g.accept(T[Ti.extensionName])}catch{St(t,b,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[Ti.extensionName]=g}t.setSocket(b,_,{allowSynchronousEvents:n.allowSynchronousEvents,generateMask:n.generateMask,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation})}),n.finishRequest?n.finishRequest(m,t):m.end()}function ra(t,e){t._readyState=qe.CLOSING,t.emit("error",e),t.emitClose()}function kA(t){return t.path=t.socketPath,yy.connect(t)}function OA(t){return t.path=void 0,!t.servername&&t.servername!==""&&(t.servername=yy.isIP(t.host)?"":t.host),uA.connect(t)}function St(t,e,i){t._readyState=qe.CLOSING;let r=new Error(i);Error.captureStackTrace(r,St),e.setHeader?(e[_y]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(ra,t,r)):(e.destroy(r),e.once("error",t.emit.bind(t,"error")),e.once("close",t.emitClose.bind(t)))}function af(t,e,i){if(e){let r=xA(e).length;t._socket?t._sender._bufferedBytes+=r:t._bufferedAmount+=r}if(i){let r=new Error(`WebSocket is not open: readyState ${t.readyState} (${ci[t.readyState]})`);process.nextTick(i,r)}}function CA(t,e){let i=this[nt];i._closeFrameReceived=!0,i._closeMessage=e,i._closeCode=t,i._socket[nt]!==void 0&&(i._socket.removeListener("data",na),process.nextTick(xy,i._socket),t===1005?i.close():i.close(t,e))}function TA(){let t=this[nt];t.isPaused||t._socket.resume()}function AA(t){let e=this[nt];e._socket[nt]!==void 0&&(e._socket.removeListener("data",na),process.nextTick(xy,e._socket),e.close(t[vA])),e.emit("error",t)}function vy(){this[nt].emitClose()}function IA(t,e){this[nt].emit("message",t,e)}function NA(t){let e=this[nt];e._autoPong&&e.pong(t,!this._isServer,by),e.emit("ping",t)}function LA(t){this[nt].emit("pong",t)}function xy(t){t.resume()}function Sy(){let t=this[nt];this.removeListener("close",Sy),this.removeListener("data",na),this.removeListener("end",Ey),t._readyState=qe.CLOSING;let e;!this._readableState.endEmitted&&!t._closeFrameReceived&&!t._receiver._writableState.errorEmitted&&(e=t._socket.read())!==null&&t._receiver.write(e),t._receiver.end(),this[nt]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",vy),t._receiver.on("finish",vy))}function na(t){this[nt]._receiver.write(t)||this.pause()}function Ey(){let t=this[nt];t._readyState=qe.CLOSING,t._receiver.end(),this.end()}function ky(){let t=this[nt];this.removeListener("error",ky),this.on("error",by),t&&(t._readyState=qe.CLOSING,this.destroy())}});var Ty=w((jL,Cy)=>{"use strict";var{tokenChars:BA}=Wn();function RA(t){let e=new Set,i=-1,r=-1,n=0;for(n;n<t.length;n++){let o=t.charCodeAt(n);if(r===-1&&BA[o]===1)i===-1&&(i=n);else if(n!==0&&(o===32||o===9))r===-1&&i!==-1&&(r=n);else if(o===44){if(i===-1)throw new SyntaxError(`Unexpected character at index ${n}`);r===-1&&(r=n);let a=t.slice(i,r);if(e.has(a))throw new SyntaxError(`The "${a}" subprotocol is duplicated`);e.add(a),i=r=-1}else throw new SyntaxError(`Unexpected character at index ${n}`)}if(i===-1||r!==-1)throw new SyntaxError("Unexpected end of input");let s=t.slice(i,n);if(e.has(s))throw new SyntaxError(`The "${s}" subprotocol is duplicated`);return e.add(s),e}Cy.exports={parse:RA}});var Py=w(($L,Ry)=>{"use strict";var PA=require("events"),sa=require("http"),{Duplex:UL}=require("stream"),{createHash:MA}=require("crypto"),Ay=rf(),sr=Yn(),FA=Ty(),qA=lf(),{GUID:DA,kWebSocket:jA}=Oi(),UA=/^[+/0-9A-Za-z]{22}==$/,Iy=0,Ny=1,By=2,cf=class extends PA{constructor(e,i){if(super(),e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:qA,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=sa.createServer((r,n)=>{let s=sa.STATUS_CODES[426];n.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),n.end(s)}),this._server.listen(e.port,e.host,e.backlog,i)):e.server&&(this._server=e.server),this._server){let r=this.emit.bind(this,"connection");this._removeListeners=$A(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(n,s,o)=>{this.handleUpgrade(n,s,o,r)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Iy}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===By){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(Zn,this);return}if(e&&this.once("close",e),this._state!==Ny)if(this._state=Ny,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(Zn,this):process.nextTick(Zn,this);else{let i=this._server;this._removeListeners(),this._removeListeners=this._server=null,i.close(()=>{Zn(this)})}}shouldHandle(e){if(this.options.path){let i=e.url.indexOf("?");if((i!==-1?e.url.slice(0,i):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,i,r,n){i.on("error",Ly);let s=e.headers["sec-websocket-key"],o=e.headers.upgrade,a=+e.headers["sec-websocket-version"];if(e.method!=="GET"){or(this,e,i,405,"Invalid HTTP method");return}if(o===void 0||o.toLowerCase()!=="websocket"){or(this,e,i,400,"Invalid Upgrade header");return}if(s===void 0||!UA.test(s)){or(this,e,i,400,"Missing or invalid Sec-WebSocket-Key header");return}if(a!==8&&a!==13){or(this,e,i,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){Qn(i,400);return}let l=e.headers["sec-websocket-protocol"],c=new Set;if(l!==void 0)try{c=FA.parse(l)}catch{or(this,e,i,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&u!==void 0){let d=new sr(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let g=Ay.parse(u);g[sr.extensionName]&&(d.accept(g[sr.extensionName]),f[sr.extensionName]=d)}catch{or(this,e,i,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let d={origin:e.headers[`${a===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(d,(g,m,v,b)=>{if(!g)return Qn(i,m||401,v,b);this.completeUpgrade(f,s,c,e,i,r,n)});return}if(!this.options.verifyClient(d))return Qn(i,401)}this.completeUpgrade(f,s,c,e,i,r,n)}completeUpgrade(e,i,r,n,s,o,a){if(!s.readable||!s.writable)return s.destroy();if(s[jA])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Iy)return Qn(s,503);let c=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${MA("sha1").update(i+DA).digest("base64")}`],u=new this.options.WebSocket(null,void 0,this.options);if(r.size){let f=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;f&&(c.push(`Sec-WebSocket-Protocol: ${f}`),u._protocol=f)}if(e[sr.extensionName]){let f=e[sr.extensionName].params,d=Ay.format({[sr.extensionName]:[f]});c.push(`Sec-WebSocket-Extensions: ${d}`),u._extensions=e}this.emit("headers",c,n),s.write(c.concat(`\r
`).join(`\r
`)),s.removeListener("error",Ly),u.setSocket(s,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(u),u.on("close",()=>{this.clients.delete(u),this._shouldEmitClose&&!this.clients.size&&process.nextTick(Zn,this)})),a(u,n)}};Ry.exports=cf;function $A(t,e){for(let i of Object.keys(e))t.on(i,e[i]);return function(){for(let r of Object.keys(e))t.removeListener(r,e[r])}}function Zn(t){t._state=By,t.emit("close")}function Ly(){this.destroy()}function Qn(t,e,i,r){i=i||sa.STATUS_CODES[e],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(i),...r},t.once("finish",t.destroy),t.end(`HTTP/1.1 ${e} ${sa.STATUS_CODES[e]}\r
`+Object.keys(r).map(n=>`${n}: ${r[n]}`).join(`\r
`)+`\r
\r
`+i)}function or(t,e,i,r,n){if(t.listenerCount("wsClientError")){let s=new Error(n);Error.captureStackTrace(s,or),t.emit("wsClientError",s,i,e)}else Qn(i,r,n)}});var Dy=w((HL,qy)=>{var Ai=require("constants"),HA=process.cwd,oa=null,GA=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return oa||(oa=HA.call(process)),oa};try{process.cwd()}catch{}typeof process.chdir=="function"&&(pf=process.chdir,process.chdir=function(t){oa=null,pf.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,pf));var pf;qy.exports=YA;function YA(t){Ai.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(t),t.lutimes||i(t),t.chown=s(t.chown),t.fchown=s(t.fchown),t.lchown=s(t.lchown),t.chmod=r(t.chmod),t.fchmod=r(t.fchmod),t.lchmod=r(t.lchmod),t.chownSync=o(t.chownSync),t.fchownSync=o(t.fchownSync),t.lchownSync=o(t.lchownSync),t.chmodSync=n(t.chmodSync),t.fchmodSync=n(t.fchmodSync),t.lchmodSync=n(t.lchmodSync),t.stat=a(t.stat),t.fstat=a(t.fstat),t.lstat=a(t.lstat),t.statSync=l(t.statSync),t.fstatSync=l(t.fstatSync),t.lstatSync=l(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(u,f,d){d&&process.nextTick(d)},t.lchmodSync=function(){}),t.chown&&!t.lchown&&(t.lchown=function(u,f,d,g){g&&process.nextTick(g)},t.lchownSync=function(){}),GA==="win32"&&(t.rename=typeof t.rename!="function"?t.rename:function(u){function f(d,g,m){var v=Date.now(),b=0;u(d,g,function _(S){if(S&&(S.code==="EACCES"||S.code==="EPERM")&&Date.now()-v<6e4){setTimeout(function(){t.stat(g,function(O,k){O&&O.code==="ENOENT"?u(d,g,_):m(S)})},b),b<100&&(b+=10);return}m&&m(S)})}return Object.setPrototypeOf&&Object.setPrototypeOf(f,u),f}(t.rename)),t.read=typeof t.read!="function"?t.read:function(u){function f(d,g,m,v,b,_){var S;if(_&&typeof _=="function"){var O=0;S=function(k,E,R){if(k&&k.code==="EAGAIN"&&O<10)return O++,u.call(t,d,g,m,v,b,S);_.apply(this,arguments)}}return u.call(t,d,g,m,v,b,S)}return Object.setPrototypeOf&&Object.setPrototypeOf(f,u),f}(t.read),t.readSync=typeof t.readSync!="function"?t.readSync:function(u){return function(f,d,g,m,v){for(var b=0;;)try{return u.call(t,f,d,g,m,v)}catch(_){if(_.code==="EAGAIN"&&b<10){b++;continue}throw _}}}(t.readSync);function e(u){u.lchmod=function(f,d,g){u.open(f,Ai.O_WRONLY|Ai.O_SYMLINK,d,function(m,v){if(m){g&&g(m);return}u.fchmod(v,d,function(b){u.close(v,function(_){g&&g(b||_)})})})},u.lchmodSync=function(f,d){var g=u.openSync(f,Ai.O_WRONLY|Ai.O_SYMLINK,d),m=!0,v;try{v=u.fchmodSync(g,d),m=!1}finally{if(m)try{u.closeSync(g)}catch{}else u.closeSync(g)}return v}}function i(u){Ai.hasOwnProperty("O_SYMLINK")&&u.futimes?(u.lutimes=function(f,d,g,m){u.open(f,Ai.O_SYMLINK,function(v,b){if(v){m&&m(v);return}u.futimes(b,d,g,function(_){u.close(b,function(S){m&&m(_||S)})})})},u.lutimesSync=function(f,d,g){var m=u.openSync(f,Ai.O_SYMLINK),v,b=!0;try{v=u.futimesSync(m,d,g),b=!1}finally{if(b)try{u.closeSync(m)}catch{}else u.closeSync(m)}return v}):u.futimes&&(u.lutimes=function(f,d,g,m){m&&process.nextTick(m)},u.lutimesSync=function(){})}function r(u){return u&&function(f,d,g){return u.call(t,f,d,function(m){c(m)&&(m=null),g&&g.apply(this,arguments)})}}function n(u){return u&&function(f,d){try{return u.call(t,f,d)}catch(g){if(!c(g))throw g}}}function s(u){return u&&function(f,d,g,m){return u.call(t,f,d,g,function(v){c(v)&&(v=null),m&&m.apply(this,arguments)})}}function o(u){return u&&function(f,d,g){try{return u.call(t,f,d,g)}catch(m){if(!c(m))throw m}}}function a(u){return u&&function(f,d,g){typeof d=="function"&&(g=d,d=null);function m(v,b){b&&(b.uid<0&&(b.uid+=4294967296),b.gid<0&&(b.gid+=4294967296)),g&&g.apply(this,arguments)}return d?u.call(t,f,d,m):u.call(t,f,m)}}function l(u){return u&&function(f,d){var g=d?u.call(t,f,d):u.call(t,f);return g&&(g.uid<0&&(g.uid+=4294967296),g.gid<0&&(g.gid+=4294967296)),g}}function c(u){if(!u||u.code==="ENOSYS")return!0;var f=!process.getuid||process.getuid()!==0;return!!(f&&(u.code==="EINVAL"||u.code==="EPERM"))}}});var $y=w((GL,Uy)=>{var jy=require("stream").Stream;Uy.exports=WA;function WA(t){return{ReadStream:e,WriteStream:i};function e(r,n){if(!(this instanceof e))return new e(r,n);jy.call(this);var s=this;this.path=r,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,n=n||{};for(var o=Object.keys(n),a=0,l=o.length;a<l;a++){var c=o[a];this[c]=n[c]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){s._read()});return}t.open(this.path,this.flags,this.mode,function(u,f){if(u){s.emit("error",u),s.readable=!1;return}s.fd=f,s.emit("open",f),s._read()})}function i(r,n){if(!(this instanceof i))return new i(r,n);jy.call(this),this.path=r,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,n=n||{};for(var s=Object.keys(n),o=0,a=s.length;o<a;o++){var l=s[o];this[l]=n[l]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}});var Hy=w((YL,Vy)=>{"use strict";Vy.exports=KA;var zA=Object.getPrototypeOf||function(t){return t.__proto__};function KA(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var e={__proto__:zA(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(t,i))}),e}});var zy=w((WL,gf)=>{var Me=require("fs"),JA=Dy(),ZA=$y(),QA=Hy(),aa=require("util"),et,ca;typeof Symbol=="function"&&typeof Symbol.for=="function"?(et=Symbol.for("graceful-fs.queue"),ca=Symbol.for("graceful-fs.previous")):(et="___graceful-fs.queue",ca="___graceful-fs.previous");function XA(){}function Wy(t,e){Object.defineProperty(t,et,{get:function(){return e}})}var ar=XA;aa.debuglog?ar=aa.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(ar=function(){var t=aa.format.apply(aa,arguments);t="GFS4: "+t.split(/\n/).join(`
GFS4: `),console.error(t)});Me[et]||(Gy=global[et]||[],Wy(Me,Gy),Me.close=function(t){function e(i,r){return t.call(Me,i,function(n){n||Yy(),typeof r=="function"&&r.apply(this,arguments)})}return Object.defineProperty(e,ca,{value:t}),e}(Me.close),Me.closeSync=function(t){function e(i){t.apply(Me,arguments),Yy()}return Object.defineProperty(e,ca,{value:t}),e}(Me.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){ar(Me[et]),require("assert").equal(Me[et].length,0)}));var Gy;global[et]||Wy(global,Me[et]);gf.exports=df(QA(Me));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!Me.__patched&&(gf.exports=df(Me),Me.__patched=!0);function df(t){JA(t),t.gracefulify=df,t.createReadStream=E,t.createWriteStream=R;var e=t.readFile;t.readFile=i;function i(C,L,P){return typeof L=="function"&&(P=L,L=null),U(C,L,P);function U(q,H,j,V){return e(q,H,function(W){W&&(W.code==="EMFILE"||W.code==="ENFILE")?Kr([U,[q,H,j],W,V||Date.now(),Date.now()]):typeof j=="function"&&j.apply(this,arguments)})}}var r=t.writeFile;t.writeFile=n;function n(C,L,P,U){return typeof P=="function"&&(U=P,P=null),q(C,L,P,U);function q(H,j,V,W,Q){return r(H,j,V,function(Y){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?Kr([q,[H,j,V,W],Y,Q||Date.now(),Date.now()]):typeof W=="function"&&W.apply(this,arguments)})}}var s=t.appendFile;s&&(t.appendFile=o);function o(C,L,P,U){return typeof P=="function"&&(U=P,P=null),q(C,L,P,U);function q(H,j,V,W,Q){return s(H,j,V,function(Y){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?Kr([q,[H,j,V,W],Y,Q||Date.now(),Date.now()]):typeof W=="function"&&W.apply(this,arguments)})}}var a=t.copyFile;a&&(t.copyFile=l);function l(C,L,P,U){return typeof P=="function"&&(U=P,P=0),q(C,L,P,U);function q(H,j,V,W,Q){return a(H,j,V,function(Y){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?Kr([q,[H,j,V,W],Y,Q||Date.now(),Date.now()]):typeof W=="function"&&W.apply(this,arguments)})}}var c=t.readdir;t.readdir=f;var u=/^v[0-5]\./;function f(C,L,P){typeof L=="function"&&(P=L,L=null);var U=u.test(process.version)?function(j,V,W,Q){return c(j,q(j,V,W,Q))}:function(j,V,W,Q){return c(j,V,q(j,V,W,Q))};return U(C,L,P);function q(H,j,V,W){return function(Q,Y){Q&&(Q.code==="EMFILE"||Q.code==="ENFILE")?Kr([U,[H,j,V],Q,W||Date.now(),Date.now()]):(Y&&Y.sort&&Y.sort(),typeof V=="function"&&V.call(this,Q,Y))}}}if(process.version.substr(0,4)==="v0.8"){var d=ZA(t);_=d.ReadStream,O=d.WriteStream}var g=t.ReadStream;g&&(_.prototype=Object.create(g.prototype),_.prototype.open=S);var m=t.WriteStream;m&&(O.prototype=Object.create(m.prototype),O.prototype.open=k),Object.defineProperty(t,"ReadStream",{get:function(){return _},set:function(C){_=C},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return O},set:function(C){O=C},enumerable:!0,configurable:!0});var v=_;Object.defineProperty(t,"FileReadStream",{get:function(){return v},set:function(C){v=C},enumerable:!0,configurable:!0});var b=O;Object.defineProperty(t,"FileWriteStream",{get:function(){return b},set:function(C){b=C},enumerable:!0,configurable:!0});function _(C,L){return this instanceof _?(g.apply(this,arguments),this):_.apply(Object.create(_.prototype),arguments)}function S(){var C=this;A(C.path,C.flags,C.mode,function(L,P){L?(C.autoClose&&C.destroy(),C.emit("error",L)):(C.fd=P,C.emit("open",P),C.read())})}function O(C,L){return this instanceof O?(m.apply(this,arguments),this):O.apply(Object.create(O.prototype),arguments)}function k(){var C=this;A(C.path,C.flags,C.mode,function(L,P){L?(C.destroy(),C.emit("error",L)):(C.fd=P,C.emit("open",P))})}function E(C,L){return new t.ReadStream(C,L)}function R(C,L){return new t.WriteStream(C,L)}var T=t.open;t.open=A;function A(C,L,P,U){return typeof P=="function"&&(U=P,P=null),q(C,L,P,U);function q(H,j,V,W,Q){return T(H,j,V,function(Y,de){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?Kr([q,[H,j,V,W],Y,Q||Date.now(),Date.now()]):typeof W=="function"&&W.apply(this,arguments)})}}return t}function Kr(t){ar("ENQUEUE",t[0].name,t[1]),Me[et].push(t),mf()}var la;function Yy(){for(var t=Date.now(),e=0;e<Me[et].length;++e)Me[et][e].length>2&&(Me[et][e][3]=t,Me[et][e][4]=t);mf()}function mf(){if(clearTimeout(la),la=void 0,Me[et].length!==0){var t=Me[et].shift(),e=t[0],i=t[1],r=t[2],n=t[3],s=t[4];if(n===void 0)ar("RETRY",e.name,i),e.apply(null,i);else if(Date.now()-n>=6e4){ar("TIMEOUT",e.name,i);var o=i.pop();typeof o=="function"&&o.call(null,r)}else{var a=Date.now()-s,l=Math.max(s-n,1),c=Math.min(l*1.2,100);a>=c?(ar("RETRY",e.name,i),e.apply(null,i.concat([n]))):Me[et].push(t)}la===void 0&&(la=setTimeout(mf,0))}}});var Jy=w((zL,Ky)=>{function Nt(t,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(t)),this._timeouts=t,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Ky.exports=Nt;Nt.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};Nt.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timeouts=[],this._cachedTimeouts=null};Nt.prototype.retry=function(t){if(this._timeout&&clearTimeout(this._timeout),!t)return!1;var e=new Date().getTime();if(t&&e-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(t);var i=this._timeouts.shift();if(i===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),i=this._timeouts.shift();else return!1;var r=this,n=setTimeout(function(){r._attempts++,r._operationTimeoutCb&&(r._timeout=setTimeout(function(){r._operationTimeoutCb(r._attempts)},r._operationTimeout),r._options.unref&&r._timeout.unref()),r._fn(r._attempts)},i);return this._options.unref&&n.unref(),!0};Nt.prototype.attempt=function(t,e){this._fn=t,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var i=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){i._operationTimeoutCb()},i._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};Nt.prototype.try=function(t){console.log("Using RetryOperation.try() is deprecated"),this.attempt(t)};Nt.prototype.start=function(t){console.log("Using RetryOperation.start() is deprecated"),this.attempt(t)};Nt.prototype.start=Nt.prototype.try;Nt.prototype.errors=function(){return this._errors};Nt.prototype.attempts=function(){return this._attempts};Nt.prototype.mainError=function(){if(this._errors.length===0)return null;for(var t={},e=null,i=0,r=0;r<this._errors.length;r++){var n=this._errors[r],s=n.message,o=(t[s]||0)+1;t[s]=o,o>=i&&(e=n,i=o)}return e}});var Zy=w(lr=>{var eI=Jy();lr.operation=function(t){var e=lr.timeouts(t);return new eI(e,{forever:t&&t.forever,unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})};lr.timeouts=function(t){if(t instanceof Array)return[].concat(t);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var i in t)e[i]=t[i];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var r=[],n=0;n<e.retries;n++)r.push(this.createTimeout(n,e));return t&&t.forever&&!r.length&&r.push(this.createTimeout(n,e)),r.sort(function(s,o){return s-o}),r};lr.createTimeout=function(t,e){var i=e.randomize?Math.random()+1:1,r=Math.round(i*e.minTimeout*Math.pow(e.factor,t));return r=Math.min(r,e.maxTimeout),r};lr.wrap=function(t,e,i){if(e instanceof Array&&(i=e,e=null),!i){i=[];for(var r in t)typeof t[r]=="function"&&i.push(r)}for(var n=0;n<i.length;n++){var s=i[n],o=t[s];t[s]=function(l){var c=lr.operation(e),u=Array.prototype.slice.call(arguments,1),f=u.pop();u.push(function(d){c.retry(d)||(d&&(arguments[0]=c.mainError()),f.apply(this,arguments))}),c.attempt(function(){l.apply(t,u)})}.bind(t,o),t[s].options=e}}});var Xy=w((JL,Qy)=>{Qy.exports=Zy()});var eb=w((ZL,ua)=>{ua.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&ua.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&ua.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var sb=w((QL,Qr)=>{var Pe=global.process,cr=function(t){return t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function"};cr(Pe)?(tb=require("assert"),Jr=eb(),ib=/^win/i.test(Pe.platform),Xn=require("events"),typeof Xn!="function"&&(Xn=Xn.EventEmitter),Pe.__signal_exit_emitter__?Je=Pe.__signal_exit_emitter__:(Je=Pe.__signal_exit_emitter__=new Xn,Je.count=0,Je.emitted={}),Je.infinite||(Je.setMaxListeners(1/0),Je.infinite=!0),Qr.exports=function(t,e){if(!cr(global.process))return function(){};tb.equal(typeof t,"function","a callback must be provided for exit handler"),Zr===!1&&vf();var i="exit";e&&e.alwaysLast&&(i="afterexit");var r=function(){Je.removeListener(i,t),Je.listeners("exit").length===0&&Je.listeners("afterexit").length===0&&fa()};return Je.on(i,t),r},fa=function(){!Zr||!cr(global.process)||(Zr=!1,Jr.forEach(function(e){try{Pe.removeListener(e,ha[e])}catch{}}),Pe.emit=pa,Pe.reallyExit=yf,Je.count-=1)},Qr.exports.unload=fa,ur=function(e,i,r){Je.emitted[e]||(Je.emitted[e]=!0,Je.emit(e,i,r))},ha={},Jr.forEach(function(t){ha[t]=function(){if(cr(global.process)){var i=Pe.listeners(t);i.length===Je.count&&(fa(),ur("exit",null,t),ur("afterexit",null,t),ib&&t==="SIGHUP"&&(t="SIGINT"),Pe.kill(Pe.pid,t))}}}),Qr.exports.signals=function(){return Jr},Zr=!1,vf=function(){Zr||!cr(global.process)||(Zr=!0,Je.count+=1,Jr=Jr.filter(function(e){try{return Pe.on(e,ha[e]),!0}catch{return!1}}),Pe.emit=nb,Pe.reallyExit=rb)},Qr.exports.load=vf,yf=Pe.reallyExit,rb=function(e){cr(global.process)&&(Pe.exitCode=e||0,ur("exit",Pe.exitCode,null),ur("afterexit",Pe.exitCode,null),yf.call(Pe,Pe.exitCode))},pa=Pe.emit,nb=function(e,i){if(e==="exit"&&cr(global.process)){i!==void 0&&(Pe.exitCode=i);var r=pa.apply(this,arguments);return ur("exit",Pe.exitCode,null),ur("afterexit",Pe.exitCode,null),r}else return pa.apply(this,arguments)}):Qr.exports=function(){return function(){}};var tb,Jr,ib,Xn,Je,fa,ur,ha,Zr,vf,yf,rb,pa,nb});var pb=w((XL,hb)=>{"use strict";var tI=require("path"),cb=zy(),iI=Xy(),rI=sb(),Ii={},ob=Symbol();function nI(t,e,i){let r=e[ob];if(r)return e.stat(t,(s,o)=>{if(s)return i(s);i(null,o.mtime,r)});let n=new Date(Math.ceil(Date.now()/1e3)*1e3+5);e.utimes(t,n,n,s=>{if(s)return i(s);e.stat(t,(o,a)=>{if(o)return i(o);let l=a.mtime.getTime()%1e3===0?"s":"ms";Object.defineProperty(e,ob,{value:l}),i(null,a.mtime,l)})})}function sI(t){let e=Date.now();return t==="s"&&(e=Math.ceil(e/1e3)*1e3),new Date(e)}function ma(t,e){return e.lockfilePath||`${t}.lock`}function ub(t,e,i){if(!e.realpath)return i(null,tI.resolve(t));e.fs.realpath(t,i)}function _f(t,e,i){let r=ma(t,e);e.fs.mkdir(r,n=>{if(!n)return nI(r,e.fs,(s,o,a)=>{if(s)return e.fs.rmdir(r,()=>{}),i(s);i(null,o,a)});if(n.code!=="EEXIST")return i(n);if(e.stale<=0)return i(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));e.fs.stat(r,(s,o)=>{if(s)return s.code==="ENOENT"?_f(t,{...e,stale:0},i):i(s);if(!oI(o,e))return i(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));fb(t,e,a=>{if(a)return i(a);_f(t,{...e,stale:0},i)})})})}function oI(t,e){return t.mtime.getTime()<Date.now()-e.stale}function fb(t,e,i){e.fs.rmdir(ma(t,e),r=>{if(r&&r.code!=="ENOENT")return i(r);i()})}function da(t,e){let i=Ii[t];i.updateTimeout||(i.updateDelay=i.updateDelay||e.update,i.updateTimeout=setTimeout(()=>{i.updateTimeout=null,e.fs.stat(i.lockfilePath,(r,n)=>{let s=i.lastUpdate+e.stale<Date.now();if(r)return r.code==="ENOENT"||s?bf(t,i,Object.assign(r,{code:"ECOMPROMISED"})):(i.updateDelay=1e3,da(t,e));if(!(i.mtime.getTime()===n.mtime.getTime()))return bf(t,i,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let a=sI(i.mtimePrecision);e.fs.utimes(i.lockfilePath,a,a,l=>{let c=i.lastUpdate+e.stale<Date.now();if(!i.released){if(l)return l.code==="ENOENT"||c?bf(t,i,Object.assign(l,{code:"ECOMPROMISED"})):(i.updateDelay=1e3,da(t,e));i.mtime=a,i.lastUpdate=Date.now(),i.updateDelay=null,da(t,e)}})})},i.updateDelay),i.updateTimeout.unref&&i.updateTimeout.unref())}function bf(t,e,i){e.released=!0,e.updateTimeout&&clearTimeout(e.updateTimeout),Ii[t]===e&&delete Ii[t],e.options.onCompromised(i)}function aI(t,e,i){e={stale:1e4,update:null,realpath:!0,retries:0,fs:cb,onCompromised:r=>{throw r},...e},e.retries=e.retries||0,e.retries=typeof e.retries=="number"?{retries:e.retries}:e.retries,e.stale=Math.max(e.stale||0,2e3),e.update=e.update==null?e.stale/2:e.update||0,e.update=Math.max(Math.min(e.update,e.stale/2),1e3),ub(t,e,(r,n)=>{if(r)return i(r);let s=iI.operation(e.retries);s.attempt(()=>{_f(n,e,(o,a,l)=>{if(s.retry(o))return;if(o)return i(s.mainError());let c=Ii[n]={lockfilePath:ma(n,e),mtime:a,mtimePrecision:l,options:e,lastUpdate:Date.now()};da(n,e),i(null,u=>{if(c.released)return u&&u(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));lI(n,{...e,realpath:!1},u)})})})})}function lI(t,e,i){e={fs:cb,realpath:!0,...e},ub(t,e,(r,n)=>{if(r)return i(r);let s=Ii[n];if(!s)return i(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));s.updateTimeout&&clearTimeout(s.updateTimeout),s.released=!0,delete Ii[n],fb(n,e,i)})}function ab(t){return(...e)=>new Promise((i,r)=>{e.push((n,s)=>{n?r(n):i(s)}),t(...e)})}var lb=!1;function cI(){lb||(lb=!0,rI(()=>{for(let t in Ii){let e=Ii[t].options;try{e.fs.rmdirSync(ma(t,e))}catch{}}}))}hb.exports.lock=async(t,e)=>{cI();let i=await ab(aI)(t,e);return ab(i)}});var CI={};Ef(CI,{HttpsProxyAgent:()=>kb.HttpsProxyAgent,PNG:()=>Ob.PNG,SocksProxyAgent:()=>Tb.SocksProxyAgent,StackUtils:()=>wI,colors:()=>uI,debug:()=>fI,diff:()=>hI,dotenv:()=>pI,getProxyForUrl:()=>Eb.getProxyForUrl,jpegjs:()=>dI,lockfile:()=>gI,mime:()=>vI,minimatch:()=>yI,open:()=>bI,program:()=>Cb.program,progress:()=>_I,ws:()=>SI,wsReceiver:()=>kI,wsSender:()=>OI,wsServer:()=>EI,yaml:()=>xI});module.exports=Kb(CI);var db=De(Xf()),mb=De(br());var Ba={};Ef(Ba,{Diff:()=>Ot,applyPatch:()=>Nh,applyPatches:()=>ew,canonicalize:()=>Ss,convertChangesToDMP:()=>cw,convertChangesToXML:()=>uw,createPatch:()=>tw,createTwoFilesPatch:()=>Lh,diffArrays:()=>J_,diffChars:()=>L_,diffCss:()=>U_,diffJson:()=>K_,diffLines:()=>Ca,diffSentences:()=>j_,diffTrimmedLines:()=>D_,diffWords:()=>F_,diffWordsWithSpace:()=>Oh,formatPatch:()=>Os,merge:()=>sw,parsePatch:()=>Cs,reversePatch:()=>Bh,structuredPatch:()=>ks});function Ot(){}Ot.prototype={diff:function(e,i){var r,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=n.callback;typeof n=="function"&&(s=n,n={});var o=this;function a(k){return k=o.postProcess(k,n),s?(setTimeout(function(){s(k)},0),!0):k}e=this.castInput(e,n),i=this.castInput(i,n),e=this.removeEmpty(this.tokenize(e,n)),i=this.removeEmpty(this.tokenize(i,n));var l=i.length,c=e.length,u=1,f=l+c;n.maxEditLength!=null&&(f=Math.min(f,n.maxEditLength));var d=(r=n.timeout)!==null&&r!==void 0?r:1/0,g=Date.now()+d,m=[{oldPos:-1,lastComponent:void 0}],v=this.extractCommon(m[0],i,e,0,n);if(m[0].oldPos+1>=c&&v+1>=l)return a(uh(o,m[0].lastComponent,i,e,o.useLongestToken));var b=-1/0,_=1/0;function S(){for(var k=Math.max(b,-u);k<=Math.min(_,u);k+=2){var E=void 0,R=m[k-1],T=m[k+1];R&&(m[k-1]=void 0);var A=!1;if(T){var C=T.oldPos-k;A=T&&0<=C&&C<l}var L=R&&R.oldPos+1<c;if(!A&&!L){m[k]=void 0;continue}if(!L||A&&R.oldPos<T.oldPos?E=o.addToPath(T,!0,!1,0,n):E=o.addToPath(R,!1,!0,1,n),v=o.extractCommon(E,i,e,k,n),E.oldPos+1>=c&&v+1>=l)return a(uh(o,E.lastComponent,i,e,o.useLongestToken));m[k]=E,E.oldPos+1>=c&&(_=Math.min(_,k-1)),v+1>=l&&(b=Math.max(b,k+1))}u++}if(s)(function k(){setTimeout(function(){if(u>f||Date.now()>g)return s();S()||k()},0)})();else for(;u<=f&&Date.now()<=g;){var O=S();if(O)return O}},addToPath:function(e,i,r,n,s){var o=e.lastComponent;return o&&!s.oneChangePerToken&&o.added===i&&o.removed===r?{oldPos:e.oldPos+n,lastComponent:{count:o.count+1,added:i,removed:r,previousComponent:o.previousComponent}}:{oldPos:e.oldPos+n,lastComponent:{count:1,added:i,removed:r,previousComponent:o}}},extractCommon:function(e,i,r,n,s){for(var o=i.length,a=r.length,l=e.oldPos,c=l-n,u=0;c+1<o&&l+1<a&&this.equals(r[l+1],i[c+1],s);)c++,l++,u++,s.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return u&&!s.oneChangePerToken&&(e.lastComponent={count:u,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=l,c},equals:function(e,i,r){return r.comparator?r.comparator(e,i):e===i||r.ignoreCase&&e.toLowerCase()===i.toLowerCase()},removeEmpty:function(e){for(var i=[],r=0;r<e.length;r++)e[r]&&i.push(e[r]);return i},castInput:function(e){return e},tokenize:function(e){return Array.from(e)},join:function(e){return e.join("")},postProcess:function(e){return e}};function uh(t,e,i,r,n){for(var s=[],o;e;)s.push(e),o=e.previousComponent,delete e.previousComponent,e=o;s.reverse();for(var a=0,l=s.length,c=0,u=0;a<l;a++){var f=s[a];if(f.removed)f.value=t.join(r.slice(u,u+f.count)),u+=f.count;else{if(!f.added&&n){var d=i.slice(c,c+f.count);d=d.map(function(g,m){var v=r[u+m];return v.length>g.length?v:g}),f.value=t.join(d)}else f.value=t.join(i.slice(c,c+f.count));c+=f.count,f.added||(u+=f.count)}}return s}var N_=new Ot;function L_(t,e,i){return N_.diff(t,e,i)}function fh(t,e){var i;for(i=0;i<t.length&&i<e.length;i++)if(t[i]!=e[i])return t.slice(0,i);return t.slice(0,i)}function hh(t,e){var i;if(!t||!e||t[t.length-1]!=e[e.length-1])return"";for(i=0;i<t.length&&i<e.length;i++)if(t[t.length-(i+1)]!=e[e.length-(i+1)])return t.slice(-i);return t.slice(-i)}function ka(t,e,i){if(t.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return i+t.slice(e.length)}function Oa(t,e,i){if(!e)return t+i;if(t.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return t.slice(0,-e.length)+i}function en(t,e){return ka(t,e,"")}function _s(t,e){return Oa(t,e,"")}function ph(t,e){return e.slice(0,B_(t,e))}function B_(t,e){var i=0;t.length>e.length&&(i=t.length-e.length);var r=e.length;t.length<e.length&&(r=t.length);var n=Array(r),s=0;n[0]=0;for(var o=1;o<r;o++){for(e[o]==e[s]?n[o]=n[s]:n[o]=s;s>0&&e[o]!=e[s];)s=n[s];e[o]==e[s]&&s++}s=0;for(var a=i;a<t.length;a++){for(;s>0&&t[a]!=e[s];)s=n[s];t[a]==e[s]&&s++}return s}function R_(t){return t.includes(`\r
`)&&!t.startsWith(`
`)&&!t.match(/[^\r]\n/)}function P_(t){return!t.includes(`\r
`)&&t.includes(`
`)}var xs="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",M_=new RegExp("[".concat(xs,"]+|\\s+|[^").concat(xs,"]"),"ug"),tn=new Ot;tn.equals=function(t,e,i){return i.ignoreCase&&(t=t.toLowerCase(),e=e.toLowerCase()),t.trim()===e.trim()};tn.tokenize=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i;if(e.intlSegmenter){if(e.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');i=Array.from(e.intlSegmenter.segment(t),function(s){return s.segment})}else i=t.match(M_)||[];var r=[],n=null;return i.forEach(function(s){/\s/.test(s)?n==null?r.push(s):r.push(r.pop()+s):/\s/.test(n)?r[r.length-1]==n?r.push(r.pop()+s):r.push(n+s):r.push(s),n=s}),r};tn.join=function(t){return t.map(function(e,i){return i==0?e:e.replace(/^\s+/,"")}).join("")};tn.postProcess=function(t,e){if(!t||e.oneChangePerToken)return t;var i=null,r=null,n=null;return t.forEach(function(s){s.added?r=s:s.removed?n=s:((r||n)&&dh(i,n,r,s),i=s,r=null,n=null)}),(r||n)&&dh(i,n,r,null),t};function F_(t,e,i){return(i==null?void 0:i.ignoreWhitespace)!=null&&!i.ignoreWhitespace?Oh(t,e,i):tn.diff(t,e,i)}function dh(t,e,i,r){if(e&&i){var n=e.value.match(/^\s*/)[0],s=e.value.match(/\s*$/)[0],o=i.value.match(/^\s*/)[0],a=i.value.match(/\s*$/)[0];if(t){var l=fh(n,o);t.value=Oa(t.value,o,l),e.value=en(e.value,l),i.value=en(i.value,l)}if(r){var c=hh(s,a);r.value=ka(r.value,a,c),e.value=_s(e.value,c),i.value=_s(i.value,c)}}else if(i)t&&(i.value=i.value.replace(/^\s*/,"")),r&&(r.value=r.value.replace(/^\s*/,""));else if(t&&r){var u=r.value.match(/^\s*/)[0],f=e.value.match(/^\s*/)[0],d=e.value.match(/\s*$/)[0],g=fh(u,f);e.value=en(e.value,g);var m=hh(en(u,g),d);e.value=_s(e.value,m),r.value=ka(r.value,u,m),t.value=Oa(t.value,u,u.slice(0,u.length-m.length))}else if(r){var v=r.value.match(/^\s*/)[0],b=e.value.match(/\s*$/)[0],_=ph(b,v);e.value=_s(e.value,_)}else if(t){var S=t.value.match(/\s*$/)[0],O=e.value.match(/^\s*/)[0],k=ph(S,O);e.value=en(e.value,k)}}var kh=new Ot;kh.tokenize=function(t){var e=new RegExp("(\\r?\\n)|[".concat(xs,"]+|[^\\S\\n\\r]+|[^").concat(xs,"]"),"ug");return t.match(e)||[]};function Oh(t,e,i){return kh.diff(t,e,i)}function q_(t,e){if(typeof t=="function")e.callback=t;else if(t)for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e}var rn=new Ot;rn.tokenize=function(t,e){e.stripTrailingCr&&(t=t.replace(/\r\n/g,`
`));var i=[],r=t.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var n=0;n<r.length;n++){var s=r[n];n%2&&!e.newlineIsToken?i[i.length-1]+=s:i.push(s)}return i};rn.equals=function(t,e,i){return i.ignoreWhitespace?((!i.newlineIsToken||!t.includes(`
`))&&(t=t.trim()),(!i.newlineIsToken||!e.includes(`
`))&&(e=e.trim())):i.ignoreNewlineAtEof&&!i.newlineIsToken&&(t.endsWith(`
`)&&(t=t.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),Ot.prototype.equals.call(this,t,e,i)};function Ca(t,e,i){return rn.diff(t,e,i)}function D_(t,e,i){var r=q_(i,{ignoreWhitespace:!0});return rn.diff(t,e,r)}var Ch=new Ot;Ch.tokenize=function(t){return t.split(/(\S.+?[.!?])(?=\s+|$)/)};function j_(t,e,i){return Ch.diff(t,e,i)}var Th=new Ot;Th.tokenize=function(t){return t.split(/([{}:;,]|\s+)/)};function U_(t,e,i){return Th.diff(t,e,i)}function mh(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),i.push.apply(i,r)}return i}function pt(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?mh(Object(i),!0).forEach(function(r){H_(t,r,i[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):mh(Object(i)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(i,r))})}return t}function $_(t,e){if(typeof t!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var r=i.call(t,e||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function V_(t){var e=$_(t,"string");return typeof e=="symbol"?e:e+""}function Ta(t){"@babel/helpers - typeof";return Ta=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ta(t)}function H_(t,e,i){return e=V_(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function ti(t){return G_(t)||Y_(t)||W_(t)||z_()}function G_(t){if(Array.isArray(t))return Aa(t)}function Y_(t){if(typeof Symbol!="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function W_(t,e){if(t){if(typeof t=="string")return Aa(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if(i==="Object"&&t.constructor&&(i=t.constructor.name),i==="Map"||i==="Set")return Array.from(t);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Aa(t,e)}}function Aa(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,r=new Array(e);i<e;i++)r[i]=t[i];return r}function z_(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var _r=new Ot;_r.useLongestToken=!0;_r.tokenize=rn.tokenize;_r.castInput=function(t,e){var i=e.undefinedReplacement,r=e.stringifyReplacer,n=r===void 0?function(s,o){return typeof o=="undefined"?i:o}:r;return typeof t=="string"?t:JSON.stringify(Ss(t,null,null,n),n,"  ")};_r.equals=function(t,e,i){return Ot.prototype.equals.call(_r,t.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),i)};function K_(t,e,i){return _r.diff(t,e,i)}function Ss(t,e,i,r,n){e=e||[],i=i||[],r&&(t=r(n,t));var s;for(s=0;s<e.length;s+=1)if(e[s]===t)return i[s];var o;if(Object.prototype.toString.call(t)==="[object Array]"){for(e.push(t),o=new Array(t.length),i.push(o),s=0;s<t.length;s+=1)o[s]=Ss(t[s],e,i,r,n);return e.pop(),i.pop(),o}if(t&&t.toJSON&&(t=t.toJSON()),Ta(t)==="object"&&t!==null){e.push(t),o={},i.push(o);var a=[],l;for(l in t)Object.prototype.hasOwnProperty.call(t,l)&&a.push(l);for(a.sort(),s=0;s<a.length;s+=1)l=a[s],o[l]=Ss(t[l],e,i,r,l);e.pop(),i.pop()}else o=t;return o}var Es=new Ot;Es.tokenize=function(t){return t.slice()};Es.join=Es.removeEmpty=function(t){return t};function J_(t,e,i){return Es.diff(t,e,i)}function Ah(t){return Array.isArray(t)?t.map(Ah):pt(pt({},t),{},{hunks:t.hunks.map(function(e){return pt(pt({},e),{},{lines:e.lines.map(function(i,r){var n;return i.startsWith("\\")||i.endsWith("\r")||(n=e.lines[r+1])!==null&&n!==void 0&&n.startsWith("\\")?i:i+"\r"})})})})}function Ih(t){return Array.isArray(t)?t.map(Ih):pt(pt({},t),{},{hunks:t.hunks.map(function(e){return pt(pt({},e),{},{lines:e.lines.map(function(i){return i.endsWith("\r")?i.substring(0,i.length-1):i})})})})}function Z_(t){return Array.isArray(t)||(t=[t]),!t.some(function(e){return e.hunks.some(function(i){return i.lines.some(function(r){return!r.startsWith("\\")&&r.endsWith("\r")})})})}function Q_(t){return Array.isArray(t)||(t=[t]),t.some(function(e){return e.hunks.some(function(i){return i.lines.some(function(r){return r.endsWith("\r")})})})&&t.every(function(e){return e.hunks.every(function(i){return i.lines.every(function(r,n){var s;return r.startsWith("\\")||r.endsWith("\r")||((s=i.lines[n+1])===null||s===void 0?void 0:s.startsWith("\\"))})})})}function Cs(t){var e=t.split(/\n/),i=[],r=0;function n(){var a={};for(i.push(a);r<e.length;){var l=e[r];if(/^(\-\-\-|\+\+\+|@@)\s/.test(l))break;var c=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(l);c&&(a.index=c[1]),r++}for(s(a),s(a),a.hunks=[];r<e.length;){var u=e[r];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(u))break;if(/^@@/.test(u))a.hunks.push(o());else{if(u)throw new Error("Unknown line "+(r+1)+" "+JSON.stringify(u));r++}}}function s(a){var l=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[r]);if(l){var c=l[1]==="---"?"old":"new",u=l[2].split("	",2),f=u[0].replace(/\\\\/g,"\\");/^".*"$/.test(f)&&(f=f.substr(1,f.length-2)),a[c+"FileName"]=f,a[c+"Header"]=(u[1]||"").trim(),r++}}function o(){var a=r,l=e[r++],c=l.split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),u={oldStart:+c[1],oldLines:typeof c[2]=="undefined"?1:+c[2],newStart:+c[3],newLines:typeof c[4]=="undefined"?1:+c[4],lines:[]};u.oldLines===0&&(u.oldStart+=1),u.newLines===0&&(u.newStart+=1);for(var f=0,d=0;r<e.length&&(d<u.oldLines||f<u.newLines||(g=e[r])!==null&&g!==void 0&&g.startsWith("\\"));r++){var g,m=e[r].length==0&&r!=e.length-1?" ":e[r][0];if(m==="+"||m==="-"||m===" "||m==="\\")u.lines.push(e[r]),m==="+"?f++:m==="-"?d++:m===" "&&(f++,d++);else throw new Error("Hunk at line ".concat(a+1," contained invalid line ").concat(e[r]))}if(!f&&u.newLines===1&&(u.newLines=0),!d&&u.oldLines===1&&(u.oldLines=0),f!==u.newLines)throw new Error("Added line count did not match for hunk at line "+(a+1));if(d!==u.oldLines)throw new Error("Removed line count did not match for hunk at line "+(a+1));return u}for(;r<e.length;)n();return i}function X_(t,e,i){var r=!0,n=!1,s=!1,o=1;return function a(){if(r&&!s){if(n?o++:r=!1,t+o<=i)return t+o;s=!0}if(!n)return s||(r=!0),e<=t-o?t-o++:(n=!0,a())}}function Nh(t,e){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(typeof e=="string"&&(e=Cs(e)),Array.isArray(e)){if(e.length>1)throw new Error("applyPatch only works with a single input.");e=e[0]}(i.autoConvertLineEndings||i.autoConvertLineEndings==null)&&(R_(t)&&Z_(e)?e=Ah(e):P_(t)&&Q_(e)&&(e=Ih(e)));var r=t.split(`
`),n=e.hunks,s=i.compareLine||function(P,U,q,H){return U===H},o=i.fuzzFactor||0,a=0;if(o<0||!Number.isInteger(o))throw new Error("fuzzFactor must be a non-negative integer");if(!n.length)return t;for(var l="",c=!1,u=!1,f=0;f<n[n.length-1].lines.length;f++){var d=n[n.length-1].lines[f];d[0]=="\\"&&(l[0]=="+"?c=!0:l[0]=="-"&&(u=!0)),l=d}if(c){if(u){if(!o&&r[r.length-1]=="")return!1}else if(r[r.length-1]=="")r.pop();else if(!o)return!1}else if(u){if(r[r.length-1]!="")r.push("");else if(!o)return!1}function g(P,U,q){for(var H=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,j=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,V=arguments.length>5&&arguments[5]!==void 0?arguments[5]:[],W=arguments.length>6&&arguments[6]!==void 0?arguments[6]:0,Q=0,Y=!1;H<P.length;H++){var de=P[H],ae=de.length>0?de[0]:" ",ne=de.length>0?de.substr(1):de;if(ae==="-")if(s(U+1,r[U],ae,ne))U++,Q=0;else return!q||r[U]==null?null:(V[W]=r[U],g(P,U+1,q-1,H,!1,V,W+1));if(ae==="+"){if(!j)return null;V[W]=ne,W++,Q=0,Y=!0}if(ae===" ")if(Q++,V[W]=r[U],s(U+1,r[U],ae,ne))W++,j=!0,Y=!1,U++;else return Y||!q?null:r[U]&&(g(P,U+1,q-1,H+1,!1,V,W+1)||g(P,U+1,q-1,H,!1,V,W+1))||g(P,U,q-1,H+1,!1,V,W)}return W-=Q,U-=Q,V.length=W,{patchedLines:V,oldLineLastI:U-1}}for(var m=[],v=0,b=0;b<n.length;b++){for(var _=n[b],S=void 0,O=r.length-_.oldLines+o,k=void 0,E=0;E<=o;E++){k=_.oldStart+v-1;for(var R=X_(k,a,O);k!==void 0&&(S=g(_.lines,k,E),!S);k=R());if(S)break}if(!S)return!1;for(var T=a;T<k;T++)m.push(r[T]);for(var A=0;A<S.patchedLines.length;A++){var C=S.patchedLines[A];m.push(C)}a=S.oldLineLastI+1,v=k+1-_.oldStart}for(var L=a;L<r.length;L++)m.push(r[L]);return m.join(`
`)}function ew(t,e){typeof t=="string"&&(t=Cs(t));var i=0;function r(){var n=t[i++];if(!n)return e.complete();e.loadFile(n,function(s,o){if(s)return e.complete(s);var a=Nh(o,n,e);e.patched(n,a,function(l){if(l)return e.complete(l);r()})})}r()}function ks(t,e,i,r,n,s,o){if(o||(o={}),typeof o=="function"&&(o={callback:o}),typeof o.context=="undefined"&&(o.context=4),o.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(o.callback){var a=o,l=a.callback;Ca(i,r,pt(pt({},o),{},{callback:function(f){var d=c(f);l(d)}}))}else return c(Ca(i,r,o));function c(u){if(!u)return;u.push({value:"",lines:[]});function f(A){return A.map(function(C){return" "+C})}for(var d=[],g=0,m=0,v=[],b=1,_=1,S=function(){var C=u[O],L=C.lines||iw(C.value);if(C.lines=L,C.added||C.removed){var P;if(!g){var U=u[O-1];g=b,m=_,U&&(v=o.context>0?f(U.lines.slice(-o.context)):[],g-=v.length,m-=v.length)}(P=v).push.apply(P,ti(L.map(function(W){return(C.added?"+":"-")+W}))),C.added?_+=L.length:b+=L.length}else{if(g)if(L.length<=o.context*2&&O<u.length-2){var q;(q=v).push.apply(q,ti(f(L)))}else{var H,j=Math.min(L.length,o.context);(H=v).push.apply(H,ti(f(L.slice(0,j))));var V={oldStart:g,oldLines:b-g+j,newStart:m,newLines:_-m+j,lines:v};d.push(V),g=0,m=0,v=[]}b+=L.length,_+=L.length}},O=0;O<u.length;O++)S();for(var k=0,E=d;k<E.length;k++)for(var R=E[k],T=0;T<R.lines.length;T++)R.lines[T].endsWith(`
`)?R.lines[T]=R.lines[T].slice(0,-1):(R.lines.splice(T+1,0,"\\ No newline at end of file"),T++);return{oldFileName:t,newFileName:e,oldHeader:n,newHeader:s,hunks:d}}}function Os(t){if(Array.isArray(t))return t.map(Os).join(`
`);var e=[];t.oldFileName==t.newFileName&&e.push("Index: "+t.oldFileName),e.push("==================================================================="),e.push("--- "+t.oldFileName+(typeof t.oldHeader=="undefined"?"":"	"+t.oldHeader)),e.push("+++ "+t.newFileName+(typeof t.newHeader=="undefined"?"":"	"+t.newHeader));for(var i=0;i<t.hunks.length;i++){var r=t.hunks[i];r.oldLines===0&&(r.oldStart-=1),r.newLines===0&&(r.newStart-=1),e.push("@@ -"+r.oldStart+","+r.oldLines+" +"+r.newStart+","+r.newLines+" @@"),e.push.apply(e,r.lines)}return e.join(`
`)+`
`}function Lh(t,e,i,r,n,s,o){var a;if(typeof o=="function"&&(o={callback:o}),(a=o)!==null&&a!==void 0&&a.callback){var c=o,u=c.callback;ks(t,e,i,r,n,s,pt(pt({},o),{},{callback:function(d){d?u(Os(d)):u()}}))}else{var l=ks(t,e,i,r,n,s,o);return l?Os(l):void 0}}function tw(t,e,i,r,n,s){return Lh(t,t,e,i,r,n,s)}function iw(t){var e=t.endsWith(`
`),i=t.split(`
`).map(function(r){return r+`
`});return e?i.pop():i.push(i.pop().slice(0,-1)),i}function rw(t,e){return t.length!==e.length?!1:Ia(t,e)}function Ia(t,e){if(e.length>t.length)return!1;for(var i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}function nw(t){var e=Na(t.lines),i=e.oldLines,r=e.newLines;i!==void 0?t.oldLines=i:delete t.oldLines,r!==void 0?t.newLines=r:delete t.newLines}function sw(t,e,i){t=gh(t,i),e=gh(e,i);var r={};(t.index||e.index)&&(r.index=t.index||e.index),(t.newFileName||e.newFileName)&&(vh(t)?vh(e)?(r.oldFileName=ws(r,t.oldFileName,e.oldFileName),r.newFileName=ws(r,t.newFileName,e.newFileName),r.oldHeader=ws(r,t.oldHeader,e.oldHeader),r.newHeader=ws(r,t.newHeader,e.newHeader)):(r.oldFileName=t.oldFileName,r.newFileName=t.newFileName,r.oldHeader=t.oldHeader,r.newHeader=t.newHeader):(r.oldFileName=e.oldFileName||t.oldFileName,r.newFileName=e.newFileName||t.newFileName,r.oldHeader=e.oldHeader||t.oldHeader,r.newHeader=e.newHeader||t.newHeader)),r.hunks=[];for(var n=0,s=0,o=0,a=0;n<t.hunks.length||s<e.hunks.length;){var l=t.hunks[n]||{oldStart:1/0},c=e.hunks[s]||{oldStart:1/0};if(yh(l,c))r.hunks.push(bh(l,o)),n++,a+=l.newLines-l.oldLines;else if(yh(c,l))r.hunks.push(bh(c,a)),s++,o+=c.newLines-c.oldLines;else{var u={oldStart:Math.min(l.oldStart,c.oldStart),oldLines:0,newStart:Math.min(l.newStart+o,c.oldStart+a),newLines:0,lines:[]};ow(u,l.oldStart,l.lines,c.oldStart,c.lines),s++,n++,r.hunks.push(u)}}return r}function gh(t,e){if(typeof t=="string"){if(/^@@/m.test(t)||/^Index:/m.test(t))return Cs(t)[0];if(!e)throw new Error("Must provide a base reference or pass in a patch");return ks(void 0,void 0,e,t)}return t}function vh(t){return t.newFileName&&t.newFileName!==t.oldFileName}function ws(t,e,i){return e===i?e:(t.conflict=!0,{mine:e,theirs:i})}function yh(t,e){return t.oldStart<e.oldStart&&t.oldStart+t.oldLines<e.oldStart}function bh(t,e){return{oldStart:t.oldStart,oldLines:t.oldLines,newStart:t.newStart+e,newLines:t.newLines,lines:t.lines}}function ow(t,e,i,r,n){var s={offset:e,lines:i,index:0},o={offset:r,lines:n,index:0};for(wh(t,s,o),wh(t,o,s);s.index<s.lines.length&&o.index<o.lines.length;){var a=s.lines[s.index],l=o.lines[o.index];if((a[0]==="-"||a[0]==="+")&&(l[0]==="-"||l[0]==="+"))aw(t,s,o);else if(a[0]==="+"&&l[0]===" "){var c;(c=t.lines).push.apply(c,ti(Ui(s)))}else if(l[0]==="+"&&a[0]===" "){var u;(u=t.lines).push.apply(u,ti(Ui(o)))}else a[0]==="-"&&l[0]===" "?_h(t,s,o):l[0]==="-"&&a[0]===" "?_h(t,o,s,!0):a===l?(t.lines.push(a),s.index++,o.index++):La(t,Ui(s),Ui(o))}xh(t,s),xh(t,o),nw(t)}function aw(t,e,i){var r=Ui(e),n=Ui(i);if(Sh(r)&&Sh(n)){if(Ia(r,n)&&Eh(i,r,r.length-n.length)){var s;(s=t.lines).push.apply(s,ti(r));return}else if(Ia(n,r)&&Eh(e,n,n.length-r.length)){var o;(o=t.lines).push.apply(o,ti(n));return}}else if(rw(r,n)){var a;(a=t.lines).push.apply(a,ti(r));return}La(t,r,n)}function _h(t,e,i,r){var n=Ui(e),s=lw(i,n);if(s.merged){var o;(o=t.lines).push.apply(o,ti(s.merged))}else La(t,r?s:n,r?n:s)}function La(t,e,i){t.conflict=!0,t.lines.push({conflict:!0,mine:e,theirs:i})}function wh(t,e,i){for(;e.offset<i.offset&&e.index<e.lines.length;){var r=e.lines[e.index++];t.lines.push(r),e.offset++}}function xh(t,e){for(;e.index<e.lines.length;){var i=e.lines[e.index++];t.lines.push(i)}}function Ui(t){for(var e=[],i=t.lines[t.index][0];t.index<t.lines.length;){var r=t.lines[t.index];if(i==="-"&&r[0]==="+"&&(i="+"),i===r[0])e.push(r),t.index++;else break}return e}function lw(t,e){for(var i=[],r=[],n=0,s=!1,o=!1;n<e.length&&t.index<t.lines.length;){var a=t.lines[t.index],l=e[n];if(l[0]==="+")break;if(s=s||a[0]!==" ",r.push(l),n++,a[0]==="+")for(o=!0;a[0]==="+";)i.push(a),a=t.lines[++t.index];l.substr(1)===a.substr(1)?(i.push(a),t.index++):o=!0}if((e[n]||"")[0]==="+"&&s&&(o=!0),o)return i;for(;n<e.length;)r.push(e[n++]);return{merged:r,changes:i}}function Sh(t){return t.reduce(function(e,i){return e&&i[0]==="-"},!0)}function Eh(t,e,i){for(var r=0;r<i;r++){var n=e[e.length-i+r].substr(1);if(t.lines[t.index+r]!==" "+n)return!1}return t.index+=i,!0}function Na(t){var e=0,i=0;return t.forEach(function(r){if(typeof r!="string"){var n=Na(r.mine),s=Na(r.theirs);e!==void 0&&(n.oldLines===s.oldLines?e+=n.oldLines:e=void 0),i!==void 0&&(n.newLines===s.newLines?i+=n.newLines:i=void 0)}else i!==void 0&&(r[0]==="+"||r[0]===" ")&&i++,e!==void 0&&(r[0]==="-"||r[0]===" ")&&e++}),{oldLines:e,newLines:i}}function Bh(t){return Array.isArray(t)?t.map(Bh).reverse():pt(pt({},t),{},{oldFileName:t.newFileName,oldHeader:t.newHeader,newFileName:t.oldFileName,newHeader:t.oldHeader,hunks:t.hunks.map(function(e){return{oldLines:e.newLines,oldStart:e.newStart,newLines:e.oldLines,newStart:e.oldStart,lines:e.lines.map(function(i){return i.startsWith("-")?"+".concat(i.slice(1)):i.startsWith("+")?"-".concat(i.slice(1)):i})}})})}function cw(t){for(var e=[],i,r,n=0;n<t.length;n++)i=t[n],i.added?r=1:i.removed?r=-1:r=0,e.push([r,i.value]);return e}function uw(t){for(var e=[],i=0;i<t.length;i++){var r=t[i];r.added?e.push("<ins>"):r.removed&&e.push("<del>"),e.push(fw(r.value)),r.added?e.push("</ins>"):r.removed&&e.push("</del>")}return e.join("")}function fw(t){var e=t;return e=e.replace(/&/g,"&amp;"),e=e.replace(/</g,"&lt;"),e=e.replace(/>/g,"&gt;"),e=e.replace(/"/g,"&quot;"),e}var gb=De(qh()),Eb=De(jh()),kb=De(Kh()),vb=De(ip()),yb=De(up()),bb=De(Np()),_b=De(Gp()),Ob=De(jd()),Cb=De(Xd()),wb=De(nm()),Tb=De(jm()),xb=De(Ym()),Sb=De(Rv());var VA=De(qv(),1),uf=De(Qu(),1),ff=De(ef(),1),My=De(lf(),1),hf=De(Py(),1);var Fy=My.default;var uI=db.default,fI=mb.default,hI=Ba,pI=gb.default,dI=vb.default,mI=pb(),gI=mI,vI=yb.default,yI=bb.default,bI=_b.default,_I=wb.default,wI=xb.default,xI=Sb.default,SI=Fy,EI=hf.default,kI=uf.default,OI=ff.default;0&&(module.exports={HttpsProxyAgent,PNG,SocksProxyAgent,StackUtils,colors,debug,diff,dotenv,getProxyForUrl,jpegjs,lockfile,mime,minimatch,open,program,progress,ws,wsReceiver,wsSender,wsServer,yaml});
/*! Bundled license information:

progress/lib/node-progress.js:
  (*!
   * node-progress
   * Copyright(c) 2011 TJ Holowaychuk <<EMAIL>>
   * MIT Licensed
   *)
*/
