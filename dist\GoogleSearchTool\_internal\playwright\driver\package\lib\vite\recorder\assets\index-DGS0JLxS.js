const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/codeMirrorModule-CNAqJrkA.js","assets/codeMirrorModule-C3UTv-Ge.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var c1=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function xp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Kc={exports:{}},Fi={},Wc={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cs=Symbol.for("react.element"),Tp=Symbol.for("react.portal"),Cp=Symbol.for("react.fragment"),_p=Symbol.for("react.strict_mode"),Lp=Symbol.for("react.profiler"),Op=Symbol.for("react.provider"),$p=Symbol.for("react.context"),Ip=Symbol.for("react.forward_ref"),Ap=Symbol.for("react.suspense"),Pp=Symbol.for("react.memo"),Mp=Symbol.for("react.lazy"),du=Symbol.iterator;function jp(e){return e===null||typeof e!="object"?null:(e=du&&e[du]||e["@@iterator"],typeof e=="function"?e:null)}var Hc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},qc=Object.assign,Qc={};function ir(e,t,n){this.props=e,this.context=t,this.refs=Qc,this.updater=n||Hc}ir.prototype.isReactComponent={};ir.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ir.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Yc(){}Yc.prototype=ir.prototype;function na(e,t,n){this.props=e,this.context=t,this.refs=Qc,this.updater=n||Hc}var ra=na.prototype=new Yc;ra.constructor=na;qc(ra,ir.prototype);ra.isPureReactComponent=!0;var hu=Array.isArray,Jc=Object.prototype.hasOwnProperty,sa={current:null},Gc={key:!0,ref:!0,__self:!0,__source:!0};function Xc(e,t,n){var r,s={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)Jc.call(t,r)&&!Gc.hasOwnProperty(r)&&(s[r]=t[r]);var o=arguments.length-2;if(o===1)s.children=n;else if(1<o){for(var a=Array(o),u=0;u<o;u++)a[u]=arguments[u+2];s.children=a}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)s[r]===void 0&&(s[r]=o[r]);return{$$typeof:cs,type:e,key:i,ref:l,props:s,_owner:sa.current}}function Rp(e,t){return{$$typeof:cs,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ia(e){return typeof e=="object"&&e!==null&&e.$$typeof===cs}function Dp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var pu=/\/+/g;function wl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Dp(""+e.key):t.toString(36)}function Vs(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case cs:case Tp:l=!0}}if(l)return l=e,s=s(l),e=r===""?"."+wl(l,0):r,hu(s)?(n="",e!=null&&(n=e.replace(pu,"$&/")+"/"),Vs(s,t,n,"",function(u){return u})):s!=null&&(ia(s)&&(s=Rp(s,n+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(pu,"$&/")+"/")+e)),t.push(s)),1;if(l=0,r=r===""?".":r+":",hu(e))for(var o=0;o<e.length;o++){i=e[o];var a=r+wl(i,o);l+=Vs(i,t,n,a,s)}else if(a=jp(e),typeof a=="function")for(e=a.call(e),o=0;!(i=e.next()).done;)i=i.value,a=r+wl(i,o++),l+=Vs(i,t,n,a,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function ks(e,t,n){if(e==null)return e;var r=[],s=0;return Vs(e,r,"","",function(i){return t.call(n,i,s++)}),r}function bp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Ks={transition:null},Bp={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Ks,ReactCurrentOwner:sa};D.Children={map:ks,forEach:function(e,t,n){ks(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ks(e,function(){t++}),t},toArray:function(e){return ks(e,function(t){return t})||[]},only:function(e){if(!ia(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};D.Component=ir;D.Fragment=Cp;D.Profiler=Lp;D.PureComponent=na;D.StrictMode=_p;D.Suspense=Ap;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Bp;D.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=qc({},e.props),s=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=sa.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(a in t)Jc.call(t,a)&&!Gc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&o!==void 0?o[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){o=Array(a);for(var u=0;u<a;u++)o[u]=arguments[u+2];r.children=o}return{$$typeof:cs,type:e.type,key:s,ref:i,props:r,_owner:l}};D.createContext=function(e){return e={$$typeof:$p,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Op,_context:e},e.Consumer=e};D.createElement=Xc;D.createFactory=function(e){var t=Xc.bind(null,e);return t.type=e,t};D.createRef=function(){return{current:null}};D.forwardRef=function(e){return{$$typeof:Ip,render:e}};D.isValidElement=ia;D.lazy=function(e){return{$$typeof:Mp,_payload:{_status:-1,_result:e},_init:bp}};D.memo=function(e,t){return{$$typeof:Pp,type:e,compare:t===void 0?null:t}};D.startTransition=function(e){var t=Ks.transition;Ks.transition={};try{e()}finally{Ks.transition=t}};D.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};D.useCallback=function(e,t){return xe.current.useCallback(e,t)};D.useContext=function(e){return xe.current.useContext(e)};D.useDebugValue=function(){};D.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};D.useEffect=function(e,t){return xe.current.useEffect(e,t)};D.useId=function(){return xe.current.useId()};D.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};D.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};D.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};D.useMemo=function(e,t){return xe.current.useMemo(e,t)};D.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};D.useRef=function(e){return xe.current.useRef(e)};D.useState=function(e){return xe.current.useState(e)};D.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};D.useTransition=function(){return xe.current.useTransition()};D.version="18.2.0";Wc.exports=D;var B=Wc.exports;const zn=xp(B);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fp=B,zp=Symbol.for("react.element"),Up=Symbol.for("react.fragment"),Vp=Object.prototype.hasOwnProperty,Kp=Fp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Wp={key:!0,ref:!0,__self:!0,__source:!0};function Zc(e,t,n){var r,s={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)Vp.call(t,r)&&!Wp.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:zp,type:e,key:i,ref:l,props:s,_owner:Kp.current}}Fi.Fragment=Up;Fi.jsx=Zc;Fi.jsxs=Zc;Kc.exports=Fi;var L=Kc.exports;function ef(){const e=zn.useRef(null),[t,n]=zn.useState(new DOMRect(0,0,10,10));return zn.useLayoutEffect(()=>{const r=e.current;if(!r)return;const s=r.getBoundingClientRect();n(new DOMRect(0,0,s.width,s.height));const i=new ResizeObserver(l=>{const o=l[l.length-1];o&&o.contentRect&&n(o.contentRect)});return i.observe(r),()=>i.disconnect()},[e]),[t,e]}function Hp(e){if(e<0||!isFinite(e))return"-";if(e===0)return"0";if(e<1e3)return e.toFixed(0)+"ms";const t=e/1e3;if(t<60)return t.toFixed(1)+"s";const n=t/60;if(n<60)return n.toFixed(1)+"m";const r=n/60;return r<24?r.toFixed(1)+"h":(r/24).toFixed(1)+"d"}function mu(e){const t=document.createElement("textarea");t.style.position="absolute",t.style.zIndex="-1000",t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),t.remove()}function eo(e,t){e&&(t=Xt.getObject(e,t));const[n,r]=zn.useState(t),s=zn.useCallback(i=>{e?Xt.setObject(e,i):r(i)},[e,r]);return zn.useEffect(()=>{if(e){const i=()=>r(Xt.getObject(e,t));return Xt.onChangeEmitter.addEventListener(e,i),()=>Xt.onChangeEmitter.removeEventListener(e,i)}},[t,e]),[n,s]}class qp{constructor(){this.onChangeEmitter=new EventTarget}getString(t,n){return localStorage[t]||n}setString(t,n){var r;localStorage[t]=n,this.onChangeEmitter.dispatchEvent(new Event(t)),(r=window.saveSettings)==null||r.call(window)}getObject(t,n){if(!localStorage[t])return n;try{return JSON.parse(localStorage[t])}catch{return n}}setObject(t,n){var r;localStorage[t]=JSON.stringify(n),this.onChangeEmitter.dispatchEvent(new Event(t)),(r=window.saveSettings)==null||r.call(window)}}const Xt=new qp;function on(...e){return e.filter(Boolean).join(" ")}const gu="\\u0000-\\u0020\\u007f-\\u009f",Qp=new RegExp("(?:[a-zA-Z][a-zA-Z0-9+.-]{2,}:\\/\\/|www\\.)[^\\s"+gu+'"]{2,}[^\\s'+gu+`"')}\\],:;.!?]`,"ug");function Yp(){if(document.playwrightThemeInitialized)return;document.playwrightThemeInitialized=!0,document.defaultView.addEventListener("focus",n=>{n.target.document.nodeType===Node.DOCUMENT_NODE&&document.body.classList.remove("inactive")},!1),document.defaultView.addEventListener("blur",n=>{document.body.classList.add("inactive")},!1);const e=Xt.getString("theme","light-mode"),t=window.matchMedia("(prefers-color-scheme: dark)");(e==="dark-mode"||t.matches)&&document.body.classList.add("dark-mode")}const Jp=new Set;function Gp(){const e=Xp(),t=e==="dark-mode"?"light-mode":"dark-mode";e&&document.body.classList.remove(e),document.body.classList.add(t),Xt.setString("theme",t);for(const n of Jp)n(t)}function Xp(){return document.body.classList.contains("dark-mode")?"dark-mode":"light-mode"}var tf={exports:{}},Fe={},nf={exports:{}},rf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,j){var R=I.length;I.push(j);e:for(;0<R;){var Z=R-1>>>1,oe=I[Z];if(0<s(oe,j))I[Z]=j,I[R]=oe,R=Z;else break e}}function n(I){return I.length===0?null:I[0]}function r(I){if(I.length===0)return null;var j=I[0],R=I.pop();if(R!==j){I[0]=R;e:for(var Z=0,oe=I.length,ws=oe>>>1;Z<ws;){var Qt=2*(Z+1)-1,vl=I[Qt],Yt=Qt+1,Ss=I[Yt];if(0>s(vl,R))Yt<oe&&0>s(Ss,vl)?(I[Z]=Ss,I[Yt]=R,Z=Yt):(I[Z]=vl,I[Qt]=R,Z=Qt);else if(Yt<oe&&0>s(Ss,R))I[Z]=Ss,I[Yt]=R,Z=Yt;else break e}}return j}function s(I,j){var R=I.sortIndex-j.sortIndex;return R!==0?R:I.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,o=l.now();e.unstable_now=function(){return l.now()-o}}var a=[],u=[],m=1,f=null,p=3,g=!1,w=!1,h=!1,v=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(I){for(var j=n(u);j!==null;){if(j.callback===null)r(u);else if(j.startTime<=I)r(u),j.sortIndex=j.expirationTime,t(a,j);else break;j=n(u)}}function S(I){if(h=!1,y(I),!w)if(n(a)!==null)w=!0,yr(x);else{var j=n(u);j!==null&&vr(S,j.startTime-I)}}function x(I,j){w=!1,h&&(h=!1,d(N),N=-1),g=!0;var R=p;try{for(y(j),f=n(a);f!==null&&(!(f.expirationTime>j)||I&&!M());){var Z=f.callback;if(typeof Z=="function"){f.callback=null,p=f.priorityLevel;var oe=Z(f.expirationTime<=j);j=e.unstable_now(),typeof oe=="function"?f.callback=oe:f===n(a)&&r(a),y(j)}else r(a);f=n(a)}if(f!==null)var ws=!0;else{var Qt=n(u);Qt!==null&&vr(S,Qt.startTime-j),ws=!1}return ws}finally{f=null,p=R,g=!1}}var k=!1,T=null,N=-1,E=5,O=-1;function M(){return!(e.unstable_now()-O<E)}function C(){if(T!==null){var I=e.unstable_now();O=I;var j=!0;try{j=T(!0,I)}finally{j?A():(k=!1,T=null)}}else k=!1}var A;if(typeof c=="function")A=function(){c(C)};else if(typeof MessageChannel<"u"){var pe=new MessageChannel,gr=pe.port2;pe.port1.onmessage=C,A=function(){gr.postMessage(null)}}else A=function(){v(C,0)};function yr(I){T=I,k||(k=!0,A())}function vr(I,j){N=v(function(){I(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){w||g||(w=!0,yr(x))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(I){switch(p){case 1:case 2:case 3:var j=3;break;default:j=p}var R=p;p=j;try{return I()}finally{p=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,j){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var R=p;p=I;try{return j()}finally{p=R}},e.unstable_scheduleCallback=function(I,j,R){var Z=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?Z+R:Z):R=Z,I){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=**********;break;case 4:oe=1e4;break;default:oe=5e3}return oe=R+oe,I={id:m++,callback:j,priorityLevel:I,startTime:R,expirationTime:oe,sortIndex:-1},R>Z?(I.sortIndex=R,t(u,I),n(a)===null&&I===n(u)&&(h?(d(N),N=-1):h=!0,vr(S,R-Z))):(I.sortIndex=oe,t(a,I),w||g||(w=!0,yr(x))),I},e.unstable_shouldYield=M,e.unstable_wrapCallback=function(I){var j=p;return function(){var R=p;p=j;try{return I.apply(this,arguments)}finally{p=R}}}})(rf);nf.exports=rf;var Zp=nf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sf=B,Be=Zp;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var lf=new Set,Kr={};function gn(e,t){Jn(e,t),Jn(e+"Capture",t)}function Jn(e,t){for(Kr[e]=t,e=0;e<t.length;e++)lf.add(t[e])}var St=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),to=Object.prototype.hasOwnProperty,em=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,yu={},vu={};function tm(e){return to.call(vu,e)?!0:to.call(yu,e)?!1:em.test(e)?vu[e]=!0:(yu[e]=!0,!1)}function nm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function rm(e,t,n,r){if(t===null||typeof t>"u"||nm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Te(e,t,n,r,s,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var he={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){he[e]=new Te(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];he[t]=new Te(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){he[e]=new Te(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){he[e]=new Te(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){he[e]=new Te(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){he[e]=new Te(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){he[e]=new Te(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){he[e]=new Te(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){he[e]=new Te(e,5,!1,e.toLowerCase(),null,!1,!1)});var la=/[\-:]([a-z])/g;function oa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(la,oa);he[t]=new Te(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(la,oa);he[t]=new Te(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(la,oa);he[t]=new Te(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){he[e]=new Te(e,1,!1,e.toLowerCase(),null,!1,!1)});he.xlinkHref=new Te("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){he[e]=new Te(e,1,!1,e.toLowerCase(),null,!0,!0)});function aa(e,t,n,r){var s=he.hasOwnProperty(t)?he[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(rm(t,n,s,r)&&(n=null),r||s===null?tm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=sf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Es=Symbol.for("react.element"),xn=Symbol.for("react.portal"),Tn=Symbol.for("react.fragment"),ua=Symbol.for("react.strict_mode"),no=Symbol.for("react.profiler"),of=Symbol.for("react.provider"),af=Symbol.for("react.context"),ca=Symbol.for("react.forward_ref"),ro=Symbol.for("react.suspense"),so=Symbol.for("react.suspense_list"),fa=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),uf=Symbol.for("react.offscreen"),wu=Symbol.iterator;function wr(e){return e===null||typeof e!="object"?null:(e=wu&&e[wu]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,Sl;function Or(e){if(Sl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Sl=t&&t[1]||""}return`
`+Sl+e}var kl=!1;function El(e,t){if(!e||kl)return"";kl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),l=s.length-1,o=i.length-1;1<=l&&0<=o&&s[l]!==i[o];)o--;for(;1<=l&&0<=o;l--,o--)if(s[l]!==i[o]){if(l!==1||o!==1)do if(l--,o--,0>o||s[l]!==i[o]){var a=`
`+s[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=o);break}}}finally{kl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Or(e):""}function sm(e){switch(e.tag){case 5:return Or(e.type);case 16:return Or("Lazy");case 13:return Or("Suspense");case 19:return Or("SuspenseList");case 0:case 2:case 15:return e=El(e.type,!1),e;case 11:return e=El(e.type.render,!1),e;case 1:return e=El(e.type,!0),e;default:return""}}function io(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Tn:return"Fragment";case xn:return"Portal";case no:return"Profiler";case ua:return"StrictMode";case ro:return"Suspense";case so:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case af:return(e.displayName||"Context")+".Consumer";case of:return(e._context.displayName||"Context")+".Provider";case ca:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case fa:return t=e.displayName||null,t!==null?t:io(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return io(e(t))}catch{}}return null}function im(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return io(t);case 8:return t===ua?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function lm(e){var t=cf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ns(e){e._valueTracker||(e._valueTracker=lm(e))}function ff(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=cf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ci(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function lo(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Su(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function df(e,t){t=t.checked,t!=null&&aa(e,"checked",t,!1)}function oo(e,t){df(e,t);var n=zt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ao(e,t.type,n):t.hasOwnProperty("defaultValue")&&ao(e,t.type,zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ku(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ao(e,t,n){(t!=="number"||ci(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var $r=Array.isArray;function Un(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function uo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Eu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if($r(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zt(n)}}function hf(e,t){var n=zt(t.value),r=zt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Nu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function pf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function co(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?pf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var xs,mf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(xs=xs||document.createElement("div"),xs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=xs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Wr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},om=["Webkit","ms","Moz","O"];Object.keys(Mr).forEach(function(e){om.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Mr[t]=Mr[e]})});function gf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Mr.hasOwnProperty(e)&&Mr[e]?(""+t).trim():t+"px"}function yf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=gf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var am=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function fo(e,t){if(t){if(am[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function ho(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var po=null;function da(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var mo=null,Vn=null,Kn=null;function xu(e){if(e=hs(e)){if(typeof mo!="function")throw Error(_(280));var t=e.stateNode;t&&(t=Wi(t),mo(e.stateNode,e.type,t))}}function vf(e){Vn?Kn?Kn.push(e):Kn=[e]:Vn=e}function wf(){if(Vn){var e=Vn,t=Kn;if(Kn=Vn=null,xu(e),t)for(e=0;e<t.length;e++)xu(t[e])}}function Sf(e,t){return e(t)}function kf(){}var Nl=!1;function Ef(e,t,n){if(Nl)return e(t,n);Nl=!0;try{return Sf(e,t,n)}finally{Nl=!1,(Vn!==null||Kn!==null)&&(kf(),wf())}}function Hr(e,t){var n=e.stateNode;if(n===null)return null;var r=Wi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var go=!1;if(St)try{var Sr={};Object.defineProperty(Sr,"passive",{get:function(){go=!0}}),window.addEventListener("test",Sr,Sr),window.removeEventListener("test",Sr,Sr)}catch{go=!1}function um(e,t,n,r,s,i,l,o,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(m){this.onError(m)}}var jr=!1,fi=null,di=!1,yo=null,cm={onError:function(e){jr=!0,fi=e}};function fm(e,t,n,r,s,i,l,o,a){jr=!1,fi=null,um.apply(cm,arguments)}function dm(e,t,n,r,s,i,l,o,a){if(fm.apply(this,arguments),jr){if(jr){var u=fi;jr=!1,fi=null}else throw Error(_(198));di||(di=!0,yo=u)}}function yn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Nf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Tu(e){if(yn(e)!==e)throw Error(_(188))}function hm(e){var t=e.alternate;if(!t){if(t=yn(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return Tu(s),e;if(i===r)return Tu(s),t;i=i.sibling}throw Error(_(188))}if(n.return!==r.return)n=s,r=i;else{for(var l=!1,o=s.child;o;){if(o===n){l=!0,n=s,r=i;break}if(o===r){l=!0,r=s,n=i;break}o=o.sibling}if(!l){for(o=i.child;o;){if(o===n){l=!0,n=i,r=s;break}if(o===r){l=!0,r=i,n=s;break}o=o.sibling}if(!l)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function xf(e){return e=hm(e),e!==null?Tf(e):null}function Tf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Tf(e);if(t!==null)return t;e=e.sibling}return null}var Cf=Be.unstable_scheduleCallback,Cu=Be.unstable_cancelCallback,pm=Be.unstable_shouldYield,mm=Be.unstable_requestPaint,ee=Be.unstable_now,gm=Be.unstable_getCurrentPriorityLevel,ha=Be.unstable_ImmediatePriority,_f=Be.unstable_UserBlockingPriority,hi=Be.unstable_NormalPriority,ym=Be.unstable_LowPriority,Lf=Be.unstable_IdlePriority,zi=null,at=null;function vm(e){if(at&&typeof at.onCommitFiberRoot=="function")try{at.onCommitFiberRoot(zi,e,void 0,(e.current.flags&128)===128)}catch{}}var tt=Math.clz32?Math.clz32:km,wm=Math.log,Sm=Math.LN2;function km(e){return e>>>=0,e===0?32:31-(wm(e)/Sm|0)|0}var Ts=64,Cs=4194304;function Ir(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var o=l&~s;o!==0?r=Ir(o):(i&=l,i!==0&&(r=Ir(i)))}else l=n&~s,l!==0?r=Ir(l):i!==0&&(r=Ir(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-tt(t),s=1<<n,r|=e[n],t&=~s;return r}function Em(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-tt(i),o=1<<l,a=s[l];a===-1?(!(o&n)||o&r)&&(s[l]=Em(o,t)):a<=t&&(e.expiredLanes|=o),i&=~o}}function vo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Of(){var e=Ts;return Ts<<=1,!(Ts&4194240)&&(Ts=64),e}function xl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function fs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-tt(t),e[t]=n}function xm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-tt(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function pa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-tt(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var F=0;function $f(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var If,ma,Af,Pf,Mf,wo=!1,_s=[],At=null,Pt=null,Mt=null,qr=new Map,Qr=new Map,Lt=[],Tm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _u(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":qr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qr.delete(t.pointerId)}}function kr(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=hs(t),t!==null&&ma(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Cm(e,t,n,r,s){switch(t){case"focusin":return At=kr(At,e,t,n,r,s),!0;case"dragenter":return Pt=kr(Pt,e,t,n,r,s),!0;case"mouseover":return Mt=kr(Mt,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return qr.set(i,kr(qr.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Qr.set(i,kr(Qr.get(i)||null,e,t,n,r,s)),!0}return!1}function jf(e){var t=en(e.target);if(t!==null){var n=yn(t);if(n!==null){if(t=n.tag,t===13){if(t=Nf(n),t!==null){e.blockedOn=t,Mf(e.priority,function(){Af(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ws(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=So(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);po=r,n.target.dispatchEvent(r),po=null}else return t=hs(n),t!==null&&ma(t),e.blockedOn=n,!1;t.shift()}return!0}function Lu(e,t,n){Ws(e)&&n.delete(t)}function _m(){wo=!1,At!==null&&Ws(At)&&(At=null),Pt!==null&&Ws(Pt)&&(Pt=null),Mt!==null&&Ws(Mt)&&(Mt=null),qr.forEach(Lu),Qr.forEach(Lu)}function Er(e,t){e.blockedOn===t&&(e.blockedOn=null,wo||(wo=!0,Be.unstable_scheduleCallback(Be.unstable_NormalPriority,_m)))}function Yr(e){function t(s){return Er(s,e)}if(0<_s.length){Er(_s[0],e);for(var n=1;n<_s.length;n++){var r=_s[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&Er(At,e),Pt!==null&&Er(Pt,e),Mt!==null&&Er(Mt,e),qr.forEach(t),Qr.forEach(t),n=0;n<Lt.length;n++)r=Lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&(n=Lt[0],n.blockedOn===null);)jf(n),n.blockedOn===null&&Lt.shift()}var Wn=xt.ReactCurrentBatchConfig,mi=!0;function Lm(e,t,n,r){var s=F,i=Wn.transition;Wn.transition=null;try{F=1,ga(e,t,n,r)}finally{F=s,Wn.transition=i}}function Om(e,t,n,r){var s=F,i=Wn.transition;Wn.transition=null;try{F=4,ga(e,t,n,r)}finally{F=s,Wn.transition=i}}function ga(e,t,n,r){if(mi){var s=So(e,t,n,r);if(s===null)Ml(e,t,r,gi,n),_u(e,r);else if(Cm(s,e,t,n,r))r.stopPropagation();else if(_u(e,r),t&4&&-1<Tm.indexOf(e)){for(;s!==null;){var i=hs(s);if(i!==null&&If(i),i=So(e,t,n,r),i===null&&Ml(e,t,r,gi,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else Ml(e,t,r,null,n)}}var gi=null;function So(e,t,n,r){if(gi=null,e=da(r),e=en(e),e!==null)if(t=yn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Nf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return gi=e,null}function Rf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(gm()){case ha:return 1;case _f:return 4;case hi:case ym:return 16;case Lf:return 536870912;default:return 16}default:return 16}}var $t=null,ya=null,Hs=null;function Df(){if(Hs)return Hs;var e,t=ya,n=t.length,r,s="value"in $t?$t.value:$t.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===s[i-r];r++);return Hs=s.slice(e,1<r?1-r:void 0)}function qs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ls(){return!0}function Ou(){return!1}function ze(e){function t(n,r,s,i,l){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(i):i[o]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ls:Ou,this.isPropagationStopped=Ou,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ls)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ls)},persist:function(){},isPersistent:Ls}),t}var lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},va=ze(lr),ds=G({},lr,{view:0,detail:0}),$m=ze(ds),Tl,Cl,Nr,Ui=G({},ds,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Nr&&(Nr&&e.type==="mousemove"?(Tl=e.screenX-Nr.screenX,Cl=e.screenY-Nr.screenY):Cl=Tl=0,Nr=e),Tl)},movementY:function(e){return"movementY"in e?e.movementY:Cl}}),$u=ze(Ui),Im=G({},Ui,{dataTransfer:0}),Am=ze(Im),Pm=G({},ds,{relatedTarget:0}),_l=ze(Pm),Mm=G({},lr,{animationName:0,elapsedTime:0,pseudoElement:0}),jm=ze(Mm),Rm=G({},lr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Dm=ze(Rm),bm=G({},lr,{data:0}),Iu=ze(bm),Bm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},zm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Um(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=zm[e])?!!t[e]:!1}function wa(){return Um}var Vm=G({},ds,{key:function(e){if(e.key){var t=Bm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wa,charCode:function(e){return e.type==="keypress"?qs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Km=ze(Vm),Wm=G({},Ui,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Au=ze(Wm),Hm=G({},ds,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wa}),qm=ze(Hm),Qm=G({},lr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ym=ze(Qm),Jm=G({},Ui,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Gm=ze(Jm),Xm=[9,13,27,32],Sa=St&&"CompositionEvent"in window,Rr=null;St&&"documentMode"in document&&(Rr=document.documentMode);var Zm=St&&"TextEvent"in window&&!Rr,bf=St&&(!Sa||Rr&&8<Rr&&11>=Rr),Pu=" ",Mu=!1;function Bf(e,t){switch(e){case"keyup":return Xm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ff(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Cn=!1;function eg(e,t){switch(e){case"compositionend":return Ff(t);case"keypress":return t.which!==32?null:(Mu=!0,Pu);case"textInput":return e=t.data,e===Pu&&Mu?null:e;default:return null}}function tg(e,t){if(Cn)return e==="compositionend"||!Sa&&Bf(e,t)?(e=Df(),Hs=ya=$t=null,Cn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return bf&&t.locale!=="ko"?null:t.data;default:return null}}var ng={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ju(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ng[e.type]:t==="textarea"}function zf(e,t,n,r){vf(r),t=yi(t,"onChange"),0<t.length&&(n=new va("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Dr=null,Jr=null;function rg(e){Xf(e,0)}function Vi(e){var t=On(e);if(ff(t))return e}function sg(e,t){if(e==="change")return t}var Uf=!1;if(St){var Ll;if(St){var Ol="oninput"in document;if(!Ol){var Ru=document.createElement("div");Ru.setAttribute("oninput","return;"),Ol=typeof Ru.oninput=="function"}Ll=Ol}else Ll=!1;Uf=Ll&&(!document.documentMode||9<document.documentMode)}function Du(){Dr&&(Dr.detachEvent("onpropertychange",Vf),Jr=Dr=null)}function Vf(e){if(e.propertyName==="value"&&Vi(Jr)){var t=[];zf(t,Jr,e,da(e)),Ef(rg,t)}}function ig(e,t,n){e==="focusin"?(Du(),Dr=t,Jr=n,Dr.attachEvent("onpropertychange",Vf)):e==="focusout"&&Du()}function lg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Vi(Jr)}function og(e,t){if(e==="click")return Vi(t)}function ag(e,t){if(e==="input"||e==="change")return Vi(t)}function ug(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var rt=typeof Object.is=="function"?Object.is:ug;function Gr(e,t){if(rt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!to.call(t,s)||!rt(e[s],t[s]))return!1}return!0}function bu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bu(e,t){var n=bu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=bu(n)}}function Kf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Kf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Wf(){for(var e=window,t=ci();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ci(e.document)}return t}function ka(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function cg(e){var t=Wf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Kf(n.ownerDocument.documentElement,n)){if(r!==null&&ka(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Bu(n,i);var l=Bu(n,r);s&&l&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var fg=St&&"documentMode"in document&&11>=document.documentMode,_n=null,ko=null,br=null,Eo=!1;function Fu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Eo||_n==null||_n!==ci(r)||(r=_n,"selectionStart"in r&&ka(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),br&&Gr(br,r)||(br=r,r=yi(ko,"onSelect"),0<r.length&&(t=new va("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=_n)))}function Os(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ln={animationend:Os("Animation","AnimationEnd"),animationiteration:Os("Animation","AnimationIteration"),animationstart:Os("Animation","AnimationStart"),transitionend:Os("Transition","TransitionEnd")},$l={},Hf={};St&&(Hf=document.createElement("div").style,"AnimationEvent"in window||(delete Ln.animationend.animation,delete Ln.animationiteration.animation,delete Ln.animationstart.animation),"TransitionEvent"in window||delete Ln.transitionend.transition);function Ki(e){if($l[e])return $l[e];if(!Ln[e])return e;var t=Ln[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Hf)return $l[e]=t[n];return e}var qf=Ki("animationend"),Qf=Ki("animationiteration"),Yf=Ki("animationstart"),Jf=Ki("transitionend"),Gf=new Map,zu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wt(e,t){Gf.set(e,t),gn(t,[e])}for(var Il=0;Il<zu.length;Il++){var Al=zu[Il],dg=Al.toLowerCase(),hg=Al[0].toUpperCase()+Al.slice(1);Wt(dg,"on"+hg)}Wt(qf,"onAnimationEnd");Wt(Qf,"onAnimationIteration");Wt(Yf,"onAnimationStart");Wt("dblclick","onDoubleClick");Wt("focusin","onFocus");Wt("focusout","onBlur");Wt(Jf,"onTransitionEnd");Jn("onMouseEnter",["mouseout","mouseover"]);Jn("onMouseLeave",["mouseout","mouseover"]);Jn("onPointerEnter",["pointerout","pointerover"]);Jn("onPointerLeave",["pointerout","pointerover"]);gn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));gn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));gn("onBeforeInput",["compositionend","keypress","textInput","paste"]);gn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));gn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));gn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ar="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),pg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ar));function Uu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,dm(r,t,void 0,e),e.currentTarget=null}function Xf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var o=r[l],a=o.instance,u=o.currentTarget;if(o=o.listener,a!==i&&s.isPropagationStopped())break e;Uu(s,o,u),i=a}else for(l=0;l<r.length;l++){if(o=r[l],a=o.instance,u=o.currentTarget,o=o.listener,a!==i&&s.isPropagationStopped())break e;Uu(s,o,u),i=a}}}if(di)throw e=yo,di=!1,yo=null,e}function V(e,t){var n=t[_o];n===void 0&&(n=t[_o]=new Set);var r=e+"__bubble";n.has(r)||(Zf(t,e,2,!1),n.add(r))}function Pl(e,t,n){var r=0;t&&(r|=4),Zf(n,e,r,t)}var $s="_reactListening"+Math.random().toString(36).slice(2);function Xr(e){if(!e[$s]){e[$s]=!0,lf.forEach(function(n){n!=="selectionchange"&&(pg.has(n)||Pl(n,!1,e),Pl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[$s]||(t[$s]=!0,Pl("selectionchange",!1,t))}}function Zf(e,t,n,r){switch(Rf(t)){case 1:var s=Lm;break;case 4:s=Om;break;default:s=ga}n=s.bind(null,t,n,e),s=void 0,!go||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Ml(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var o=r.stateNode.containerInfo;if(o===s||o.nodeType===8&&o.parentNode===s)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===s||a.nodeType===8&&a.parentNode===s))return;l=l.return}for(;o!==null;){if(l=en(o),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}o=o.parentNode}}r=r.return}Ef(function(){var u=i,m=da(n),f=[];e:{var p=Gf.get(e);if(p!==void 0){var g=va,w=e;switch(e){case"keypress":if(qs(n)===0)break e;case"keydown":case"keyup":g=Km;break;case"focusin":w="focus",g=_l;break;case"focusout":w="blur",g=_l;break;case"beforeblur":case"afterblur":g=_l;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=$u;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Am;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=qm;break;case qf:case Qf:case Yf:g=jm;break;case Jf:g=Ym;break;case"scroll":g=$m;break;case"wheel":g=Gm;break;case"copy":case"cut":case"paste":g=Dm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Au}var h=(t&4)!==0,v=!h&&e==="scroll",d=h?p!==null?p+"Capture":null:p;h=[];for(var c=u,y;c!==null;){y=c;var S=y.stateNode;if(y.tag===5&&S!==null&&(y=S,d!==null&&(S=Hr(c,d),S!=null&&h.push(Zr(c,S,y)))),v)break;c=c.return}0<h.length&&(p=new g(p,w,null,n,m),f.push({event:p,listeners:h}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==po&&(w=n.relatedTarget||n.fromElement)&&(en(w)||w[kt]))break e;if((g||p)&&(p=m.window===m?m:(p=m.ownerDocument)?p.defaultView||p.parentWindow:window,g?(w=n.relatedTarget||n.toElement,g=u,w=w?en(w):null,w!==null&&(v=yn(w),w!==v||w.tag!==5&&w.tag!==6)&&(w=null)):(g=null,w=u),g!==w)){if(h=$u,S="onMouseLeave",d="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(h=Au,S="onPointerLeave",d="onPointerEnter",c="pointer"),v=g==null?p:On(g),y=w==null?p:On(w),p=new h(S,c+"leave",g,n,m),p.target=v,p.relatedTarget=y,S=null,en(m)===u&&(h=new h(d,c+"enter",w,n,m),h.target=y,h.relatedTarget=v,S=h),v=S,g&&w)t:{for(h=g,d=w,c=0,y=h;y;y=Sn(y))c++;for(y=0,S=d;S;S=Sn(S))y++;for(;0<c-y;)h=Sn(h),c--;for(;0<y-c;)d=Sn(d),y--;for(;c--;){if(h===d||d!==null&&h===d.alternate)break t;h=Sn(h),d=Sn(d)}h=null}else h=null;g!==null&&Vu(f,p,g,h,!1),w!==null&&v!==null&&Vu(f,v,w,h,!0)}}e:{if(p=u?On(u):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var x=sg;else if(ju(p))if(Uf)x=ag;else{x=lg;var k=ig}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(x=og);if(x&&(x=x(e,u))){zf(f,x,n,m);break e}k&&k(e,p,u),e==="focusout"&&(k=p._wrapperState)&&k.controlled&&p.type==="number"&&ao(p,"number",p.value)}switch(k=u?On(u):window,e){case"focusin":(ju(k)||k.contentEditable==="true")&&(_n=k,ko=u,br=null);break;case"focusout":br=ko=_n=null;break;case"mousedown":Eo=!0;break;case"contextmenu":case"mouseup":case"dragend":Eo=!1,Fu(f,n,m);break;case"selectionchange":if(fg)break;case"keydown":case"keyup":Fu(f,n,m)}var T;if(Sa)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else Cn?Bf(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(bf&&n.locale!=="ko"&&(Cn||N!=="onCompositionStart"?N==="onCompositionEnd"&&Cn&&(T=Df()):($t=m,ya="value"in $t?$t.value:$t.textContent,Cn=!0)),k=yi(u,N),0<k.length&&(N=new Iu(N,e,null,n,m),f.push({event:N,listeners:k}),T?N.data=T:(T=Ff(n),T!==null&&(N.data=T)))),(T=Zm?eg(e,n):tg(e,n))&&(u=yi(u,"onBeforeInput"),0<u.length&&(m=new Iu("onBeforeInput","beforeinput",null,n,m),f.push({event:m,listeners:u}),m.data=T))}Xf(f,t)})}function Zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function yi(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Hr(e,n),i!=null&&r.unshift(Zr(e,i,s)),i=Hr(e,t),i!=null&&r.push(Zr(e,i,s))),e=e.return}return r}function Sn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Vu(e,t,n,r,s){for(var i=t._reactName,l=[];n!==null&&n!==r;){var o=n,a=o.alternate,u=o.stateNode;if(a!==null&&a===r)break;o.tag===5&&u!==null&&(o=u,s?(a=Hr(n,i),a!=null&&l.unshift(Zr(n,a,o))):s||(a=Hr(n,i),a!=null&&l.push(Zr(n,a,o)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var mg=/\r\n?/g,gg=/\u0000|\uFFFD/g;function Ku(e){return(typeof e=="string"?e:""+e).replace(mg,`
`).replace(gg,"")}function Is(e,t,n){if(t=Ku(t),Ku(e)!==t&&n)throw Error(_(425))}function vi(){}var No=null,xo=null;function To(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Co=typeof setTimeout=="function"?setTimeout:void 0,yg=typeof clearTimeout=="function"?clearTimeout:void 0,Wu=typeof Promise=="function"?Promise:void 0,vg=typeof queueMicrotask=="function"?queueMicrotask:typeof Wu<"u"?function(e){return Wu.resolve(null).then(e).catch(wg)}:Co;function wg(e){setTimeout(function(){throw e})}function jl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Yr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Yr(t)}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Hu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var or=Math.random().toString(36).slice(2),ot="__reactFiber$"+or,es="__reactProps$"+or,kt="__reactContainer$"+or,_o="__reactEvents$"+or,Sg="__reactListeners$"+or,kg="__reactHandles$"+or;function en(e){var t=e[ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[kt]||n[ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Hu(e);e!==null;){if(n=e[ot])return n;e=Hu(e)}return t}e=n,n=e.parentNode}return null}function hs(e){return e=e[ot]||e[kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function On(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function Wi(e){return e[es]||null}var Lo=[],$n=-1;function Ht(e){return{current:e}}function K(e){0>$n||(e.current=Lo[$n],Lo[$n]=null,$n--)}function U(e,t){$n++,Lo[$n]=e.current,e.current=t}var Ut={},Se=Ht(Ut),Ie=Ht(!1),cn=Ut;function Gn(e,t){var n=e.type.contextTypes;if(!n)return Ut;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ae(e){return e=e.childContextTypes,e!=null}function wi(){K(Ie),K(Se)}function qu(e,t,n){if(Se.current!==Ut)throw Error(_(168));U(Se,t),U(Ie,n)}function ed(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(_(108,im(e)||"Unknown",s));return G({},n,r)}function Si(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ut,cn=Se.current,U(Se,e),U(Ie,Ie.current),!0}function Qu(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=ed(e,t,cn),r.__reactInternalMemoizedMergedChildContext=e,K(Ie),K(Se),U(Se,e)):K(Ie),U(Ie,n)}var pt=null,Hi=!1,Rl=!1;function td(e){pt===null?pt=[e]:pt.push(e)}function Eg(e){Hi=!0,td(e)}function qt(){if(!Rl&&pt!==null){Rl=!0;var e=0,t=F;try{var n=pt;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}pt=null,Hi=!1}catch(s){throw pt!==null&&(pt=pt.slice(e+1)),Cf(ha,qt),s}finally{F=t,Rl=!1}}return null}var In=[],An=0,ki=null,Ei=0,Ue=[],Ve=0,fn=null,mt=1,gt="";function Jt(e,t){In[An++]=Ei,In[An++]=ki,ki=e,Ei=t}function nd(e,t,n){Ue[Ve++]=mt,Ue[Ve++]=gt,Ue[Ve++]=fn,fn=e;var r=mt;e=gt;var s=32-tt(r)-1;r&=~(1<<s),n+=1;var i=32-tt(t)+s;if(30<i){var l=s-s%5;i=(r&(1<<l)-1).toString(32),r>>=l,s-=l,mt=1<<32-tt(t)+s|n<<s|r,gt=i+e}else mt=1<<i|n<<s|r,gt=e}function Ea(e){e.return!==null&&(Jt(e,1),nd(e,1,0))}function Na(e){for(;e===ki;)ki=In[--An],In[An]=null,Ei=In[--An],In[An]=null;for(;e===fn;)fn=Ue[--Ve],Ue[Ve]=null,gt=Ue[--Ve],Ue[Ve]=null,mt=Ue[--Ve],Ue[Ve]=null}var be=null,De=null,W=!1,et=null;function rd(e,t){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Yu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,be=e,De=jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,be=e,De=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=fn!==null?{id:mt,overflow:gt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,be=e,De=null,!0):!1;default:return!1}}function Oo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $o(e){if(W){var t=De;if(t){var n=t;if(!Yu(e,t)){if(Oo(e))throw Error(_(418));t=jt(n.nextSibling);var r=be;t&&Yu(e,t)?rd(r,n):(e.flags=e.flags&-4097|2,W=!1,be=e)}}else{if(Oo(e))throw Error(_(418));e.flags=e.flags&-4097|2,W=!1,be=e}}}function Ju(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;be=e}function As(e){if(e!==be)return!1;if(!W)return Ju(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!To(e.type,e.memoizedProps)),t&&(t=De)){if(Oo(e))throw sd(),Error(_(418));for(;t;)rd(e,t),t=jt(t.nextSibling)}if(Ju(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){De=jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}De=null}}else De=be?jt(e.stateNode.nextSibling):null;return!0}function sd(){for(var e=De;e;)e=jt(e.nextSibling)}function Xn(){De=be=null,W=!1}function xa(e){et===null?et=[e]:et.push(e)}var Ng=xt.ReactCurrentBatchConfig;function Xe(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Ni=Ht(null),xi=null,Pn=null,Ta=null;function Ca(){Ta=Pn=xi=null}function _a(e){var t=Ni.current;K(Ni),e._currentValue=t}function Io(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Hn(e,t){xi=e,Ta=Pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&($e=!0),e.firstContext=null)}function qe(e){var t=e._currentValue;if(Ta!==e)if(e={context:e,memoizedValue:t,next:null},Pn===null){if(xi===null)throw Error(_(308));Pn=e,xi.dependencies={lanes:0,firstContext:e}}else Pn=Pn.next=e;return t}var tn=null;function La(e){tn===null?tn=[e]:tn.push(e)}function id(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,La(t)):(n.next=s.next,s.next=n),t.interleaved=n,Et(e,r)}function Et(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var _t=!1;function Oa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ld(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function vt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Rt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,b&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,Et(e,n)}return s=r.interleaved,s===null?(t.next=t,La(r)):(t.next=s.next,s.next=t),r.interleaved=t,Et(e,n)}function Qs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,pa(e,n)}}function Gu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ti(e,t,n,r){var s=e.updateQueue;_t=!1;var i=s.firstBaseUpdate,l=s.lastBaseUpdate,o=s.shared.pending;if(o!==null){s.shared.pending=null;var a=o,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var m=e.alternate;m!==null&&(m=m.updateQueue,o=m.lastBaseUpdate,o!==l&&(o===null?m.firstBaseUpdate=u:o.next=u,m.lastBaseUpdate=a))}if(i!==null){var f=s.baseState;l=0,m=u=a=null,o=i;do{var p=o.lane,g=o.eventTime;if((r&p)===p){m!==null&&(m=m.next={eventTime:g,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var w=e,h=o;switch(p=t,g=n,h.tag){case 1:if(w=h.payload,typeof w=="function"){f=w.call(g,f,p);break e}f=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=h.payload,p=typeof w=="function"?w.call(g,f,p):w,p==null)break e;f=G({},f,p);break e;case 2:_t=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,p=s.effects,p===null?s.effects=[o]:p.push(o))}else g={eventTime:g,lane:p,tag:o.tag,payload:o.payload,callback:o.callback,next:null},m===null?(u=m=g,a=f):m=m.next=g,l|=p;if(o=o.next,o===null){if(o=s.shared.pending,o===null)break;p=o,o=p.next,p.next=null,s.lastBaseUpdate=p,s.shared.pending=null}}while(!0);if(m===null&&(a=f),s.baseState=a,s.firstBaseUpdate=u,s.lastBaseUpdate=m,t=s.shared.interleaved,t!==null){s=t;do l|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);hn|=l,e.lanes=l,e.memoizedState=f}}function Xu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(_(191,s));s.call(r)}}}var od=new sf.Component().refs;function Ao(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var qi={isMounted:function(e){return(e=e._reactInternals)?yn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ne(),s=bt(e),i=vt(r,s);i.payload=t,n!=null&&(i.callback=n),t=Rt(e,i,s),t!==null&&(nt(t,e,s,r),Qs(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ne(),s=bt(e),i=vt(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Rt(e,i,s),t!==null&&(nt(t,e,s,r),Qs(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ne(),r=bt(e),s=vt(n,r);s.tag=2,t!=null&&(s.callback=t),t=Rt(e,s,r),t!==null&&(nt(t,e,r,n),Qs(t,e,r))}};function Zu(e,t,n,r,s,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!Gr(n,r)||!Gr(s,i):!0}function ad(e,t,n){var r=!1,s=Ut,i=t.contextType;return typeof i=="object"&&i!==null?i=qe(i):(s=Ae(t)?cn:Se.current,r=t.contextTypes,i=(r=r!=null)?Gn(e,s):Ut),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=qi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function ec(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&qi.enqueueReplaceState(t,t.state,null)}function Po(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs=od,Oa(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=qe(i):(i=Ae(t)?cn:Se.current,s.context=Gn(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ao(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&qi.enqueueReplaceState(s,s.state,null),Ti(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function xr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var o=s.refs;o===od&&(o=s.refs={}),l===null?delete o[i]:o[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function Ps(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function tc(e){var t=e._init;return t(e._payload)}function ud(e){function t(d,c){if(e){var y=d.deletions;y===null?(d.deletions=[c],d.flags|=16):y.push(c)}}function n(d,c){if(!e)return null;for(;c!==null;)t(d,c),c=c.sibling;return null}function r(d,c){for(d=new Map;c!==null;)c.key!==null?d.set(c.key,c):d.set(c.index,c),c=c.sibling;return d}function s(d,c){return d=Bt(d,c),d.index=0,d.sibling=null,d}function i(d,c,y){return d.index=y,e?(y=d.alternate,y!==null?(y=y.index,y<c?(d.flags|=2,c):y):(d.flags|=2,c)):(d.flags|=1048576,c)}function l(d){return e&&d.alternate===null&&(d.flags|=2),d}function o(d,c,y,S){return c===null||c.tag!==6?(c=Vl(y,d.mode,S),c.return=d,c):(c=s(c,y),c.return=d,c)}function a(d,c,y,S){var x=y.type;return x===Tn?m(d,c,y.props.children,S,y.key):c!==null&&(c.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Ct&&tc(x)===c.type)?(S=s(c,y.props),S.ref=xr(d,c,y),S.return=d,S):(S=ei(y.type,y.key,y.props,null,d.mode,S),S.ref=xr(d,c,y),S.return=d,S)}function u(d,c,y,S){return c===null||c.tag!==4||c.stateNode.containerInfo!==y.containerInfo||c.stateNode.implementation!==y.implementation?(c=Kl(y,d.mode,S),c.return=d,c):(c=s(c,y.children||[]),c.return=d,c)}function m(d,c,y,S,x){return c===null||c.tag!==7?(c=un(y,d.mode,S,x),c.return=d,c):(c=s(c,y),c.return=d,c)}function f(d,c,y){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Vl(""+c,d.mode,y),c.return=d,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Es:return y=ei(c.type,c.key,c.props,null,d.mode,y),y.ref=xr(d,null,c),y.return=d,y;case xn:return c=Kl(c,d.mode,y),c.return=d,c;case Ct:var S=c._init;return f(d,S(c._payload),y)}if($r(c)||wr(c))return c=un(c,d.mode,y,null),c.return=d,c;Ps(d,c)}return null}function p(d,c,y,S){var x=c!==null?c.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return x!==null?null:o(d,c,""+y,S);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Es:return y.key===x?a(d,c,y,S):null;case xn:return y.key===x?u(d,c,y,S):null;case Ct:return x=y._init,p(d,c,x(y._payload),S)}if($r(y)||wr(y))return x!==null?null:m(d,c,y,S,null);Ps(d,y)}return null}function g(d,c,y,S,x){if(typeof S=="string"&&S!==""||typeof S=="number")return d=d.get(y)||null,o(c,d,""+S,x);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Es:return d=d.get(S.key===null?y:S.key)||null,a(c,d,S,x);case xn:return d=d.get(S.key===null?y:S.key)||null,u(c,d,S,x);case Ct:var k=S._init;return g(d,c,y,k(S._payload),x)}if($r(S)||wr(S))return d=d.get(y)||null,m(c,d,S,x,null);Ps(c,S)}return null}function w(d,c,y,S){for(var x=null,k=null,T=c,N=c=0,E=null;T!==null&&N<y.length;N++){T.index>N?(E=T,T=null):E=T.sibling;var O=p(d,T,y[N],S);if(O===null){T===null&&(T=E);break}e&&T&&O.alternate===null&&t(d,T),c=i(O,c,N),k===null?x=O:k.sibling=O,k=O,T=E}if(N===y.length)return n(d,T),W&&Jt(d,N),x;if(T===null){for(;N<y.length;N++)T=f(d,y[N],S),T!==null&&(c=i(T,c,N),k===null?x=T:k.sibling=T,k=T);return W&&Jt(d,N),x}for(T=r(d,T);N<y.length;N++)E=g(T,d,N,y[N],S),E!==null&&(e&&E.alternate!==null&&T.delete(E.key===null?N:E.key),c=i(E,c,N),k===null?x=E:k.sibling=E,k=E);return e&&T.forEach(function(M){return t(d,M)}),W&&Jt(d,N),x}function h(d,c,y,S){var x=wr(y);if(typeof x!="function")throw Error(_(150));if(y=x.call(y),y==null)throw Error(_(151));for(var k=x=null,T=c,N=c=0,E=null,O=y.next();T!==null&&!O.done;N++,O=y.next()){T.index>N?(E=T,T=null):E=T.sibling;var M=p(d,T,O.value,S);if(M===null){T===null&&(T=E);break}e&&T&&M.alternate===null&&t(d,T),c=i(M,c,N),k===null?x=M:k.sibling=M,k=M,T=E}if(O.done)return n(d,T),W&&Jt(d,N),x;if(T===null){for(;!O.done;N++,O=y.next())O=f(d,O.value,S),O!==null&&(c=i(O,c,N),k===null?x=O:k.sibling=O,k=O);return W&&Jt(d,N),x}for(T=r(d,T);!O.done;N++,O=y.next())O=g(T,d,N,O.value,S),O!==null&&(e&&O.alternate!==null&&T.delete(O.key===null?N:O.key),c=i(O,c,N),k===null?x=O:k.sibling=O,k=O);return e&&T.forEach(function(C){return t(d,C)}),W&&Jt(d,N),x}function v(d,c,y,S){if(typeof y=="object"&&y!==null&&y.type===Tn&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Es:e:{for(var x=y.key,k=c;k!==null;){if(k.key===x){if(x=y.type,x===Tn){if(k.tag===7){n(d,k.sibling),c=s(k,y.props.children),c.return=d,d=c;break e}}else if(k.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Ct&&tc(x)===k.type){n(d,k.sibling),c=s(k,y.props),c.ref=xr(d,k,y),c.return=d,d=c;break e}n(d,k);break}else t(d,k);k=k.sibling}y.type===Tn?(c=un(y.props.children,d.mode,S,y.key),c.return=d,d=c):(S=ei(y.type,y.key,y.props,null,d.mode,S),S.ref=xr(d,c,y),S.return=d,d=S)}return l(d);case xn:e:{for(k=y.key;c!==null;){if(c.key===k)if(c.tag===4&&c.stateNode.containerInfo===y.containerInfo&&c.stateNode.implementation===y.implementation){n(d,c.sibling),c=s(c,y.children||[]),c.return=d,d=c;break e}else{n(d,c);break}else t(d,c);c=c.sibling}c=Kl(y,d.mode,S),c.return=d,d=c}return l(d);case Ct:return k=y._init,v(d,c,k(y._payload),S)}if($r(y))return w(d,c,y,S);if(wr(y))return h(d,c,y,S);Ps(d,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,c!==null&&c.tag===6?(n(d,c.sibling),c=s(c,y),c.return=d,d=c):(n(d,c),c=Vl(y,d.mode,S),c.return=d,d=c),l(d)):n(d,c)}return v}var Zn=ud(!0),cd=ud(!1),ps={},ut=Ht(ps),ts=Ht(ps),ns=Ht(ps);function nn(e){if(e===ps)throw Error(_(174));return e}function $a(e,t){switch(U(ns,t),U(ts,e),U(ut,ps),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:co(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=co(t,e)}K(ut),U(ut,t)}function er(){K(ut),K(ts),K(ns)}function fd(e){nn(ns.current);var t=nn(ut.current),n=co(t,e.type);t!==n&&(U(ts,e),U(ut,n))}function Ia(e){ts.current===e&&(K(ut),K(ts))}var q=Ht(0);function Ci(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Dl=[];function Aa(){for(var e=0;e<Dl.length;e++)Dl[e]._workInProgressVersionPrimary=null;Dl.length=0}var Ys=xt.ReactCurrentDispatcher,bl=xt.ReactCurrentBatchConfig,dn=0,Y=null,ie=null,ae=null,_i=!1,Br=!1,rs=0,xg=0;function me(){throw Error(_(321))}function Pa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!rt(e[n],t[n]))return!1;return!0}function Ma(e,t,n,r,s,i){if(dn=i,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ys.current=e===null||e.memoizedState===null?Lg:Og,e=n(r,s),Br){i=0;do{if(Br=!1,rs=0,25<=i)throw Error(_(301));i+=1,ae=ie=null,t.updateQueue=null,Ys.current=$g,e=n(r,s)}while(Br)}if(Ys.current=Li,t=ie!==null&&ie.next!==null,dn=0,ae=ie=Y=null,_i=!1,t)throw Error(_(300));return e}function ja(){var e=rs!==0;return rs=0,e}function lt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?Y.memoizedState=ae=e:ae=ae.next=e,ae}function Qe(){if(ie===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=ae===null?Y.memoizedState:ae.next;if(t!==null)ae=t,ie=e;else{if(e===null)throw Error(_(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},ae===null?Y.memoizedState=ae=e:ae=ae.next=e}return ae}function ss(e,t){return typeof t=="function"?t(e):t}function Bl(e){var t=Qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=ie,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var l=s.next;s.next=i.next,i.next=l}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var o=l=null,a=null,u=i;do{var m=u.lane;if((dn&m)===m)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:m,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(o=a=f,l=r):a=a.next=f,Y.lanes|=m,hn|=m}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=o,rt(r,t.memoizedState)||($e=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,Y.lanes|=i,hn|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Fl(e){var t=Qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var l=s=s.next;do i=e(i,l.action),l=l.next;while(l!==s);rt(i,t.memoizedState)||($e=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function dd(){}function hd(e,t){var n=Y,r=Qe(),s=t(),i=!rt(r.memoizedState,s);if(i&&(r.memoizedState=s,$e=!0),r=r.queue,Ra(gd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ae!==null&&ae.memoizedState.tag&1){if(n.flags|=2048,is(9,md.bind(null,n,r,s,t),void 0,null),ue===null)throw Error(_(349));dn&30||pd(n,t,s)}return s}function pd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function md(e,t,n,r){t.value=n,t.getSnapshot=r,yd(t)&&vd(e)}function gd(e,t,n){return n(function(){yd(t)&&vd(e)})}function yd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!rt(e,n)}catch{return!0}}function vd(e){var t=Et(e,1);t!==null&&nt(t,e,1,-1)}function nc(e){var t=lt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ss,lastRenderedState:e},t.queue=e,e=e.dispatch=_g.bind(null,Y,e),[t.memoizedState,e]}function is(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function wd(){return Qe().memoizedState}function Js(e,t,n,r){var s=lt();Y.flags|=e,s.memoizedState=is(1|t,n,void 0,r===void 0?null:r)}function Qi(e,t,n,r){var s=Qe();r=r===void 0?null:r;var i=void 0;if(ie!==null){var l=ie.memoizedState;if(i=l.destroy,r!==null&&Pa(r,l.deps)){s.memoizedState=is(t,n,i,r);return}}Y.flags|=e,s.memoizedState=is(1|t,n,i,r)}function rc(e,t){return Js(8390656,8,e,t)}function Ra(e,t){return Qi(2048,8,e,t)}function Sd(e,t){return Qi(4,2,e,t)}function kd(e,t){return Qi(4,4,e,t)}function Ed(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Nd(e,t,n){return n=n!=null?n.concat([e]):null,Qi(4,4,Ed.bind(null,t,e),n)}function Da(){}function xd(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Td(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Cd(e,t,n){return dn&21?(rt(n,t)||(n=Of(),Y.lanes|=n,hn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,$e=!0),e.memoizedState=n)}function Tg(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=bl.transition;bl.transition={};try{e(!1),t()}finally{F=n,bl.transition=r}}function _d(){return Qe().memoizedState}function Cg(e,t,n){var r=bt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ld(e))Od(t,n);else if(n=id(e,t,n,r),n!==null){var s=Ne();nt(n,e,r,s),$d(n,t,r)}}function _g(e,t,n){var r=bt(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ld(e))Od(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,o=i(l,n);if(s.hasEagerState=!0,s.eagerState=o,rt(o,l)){var a=t.interleaved;a===null?(s.next=s,La(t)):(s.next=a.next,a.next=s),t.interleaved=s;return}}catch{}finally{}n=id(e,t,s,r),n!==null&&(s=Ne(),nt(n,e,r,s),$d(n,t,r))}}function Ld(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Od(e,t){Br=_i=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function $d(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,pa(e,n)}}var Li={readContext:qe,useCallback:me,useContext:me,useEffect:me,useImperativeHandle:me,useInsertionEffect:me,useLayoutEffect:me,useMemo:me,useReducer:me,useRef:me,useState:me,useDebugValue:me,useDeferredValue:me,useTransition:me,useMutableSource:me,useSyncExternalStore:me,useId:me,unstable_isNewReconciler:!1},Lg={readContext:qe,useCallback:function(e,t){return lt().memoizedState=[e,t===void 0?null:t],e},useContext:qe,useEffect:rc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Js(4194308,4,Ed.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Js(4194308,4,e,t)},useInsertionEffect:function(e,t){return Js(4,2,e,t)},useMemo:function(e,t){var n=lt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=lt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Cg.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=lt();return e={current:e},t.memoizedState=e},useState:nc,useDebugValue:Da,useDeferredValue:function(e){return lt().memoizedState=e},useTransition:function(){var e=nc(!1),t=e[0];return e=Tg.bind(null,e[1]),lt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,s=lt();if(W){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),ue===null)throw Error(_(349));dn&30||pd(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,rc(gd.bind(null,r,i,e),[e]),r.flags|=2048,is(9,md.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=lt(),t=ue.identifierPrefix;if(W){var n=gt,r=mt;n=(r&~(1<<32-tt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=rs++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=xg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Og={readContext:qe,useCallback:xd,useContext:qe,useEffect:Ra,useImperativeHandle:Nd,useInsertionEffect:Sd,useLayoutEffect:kd,useMemo:Td,useReducer:Bl,useRef:wd,useState:function(){return Bl(ss)},useDebugValue:Da,useDeferredValue:function(e){var t=Qe();return Cd(t,ie.memoizedState,e)},useTransition:function(){var e=Bl(ss)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:dd,useSyncExternalStore:hd,useId:_d,unstable_isNewReconciler:!1},$g={readContext:qe,useCallback:xd,useContext:qe,useEffect:Ra,useImperativeHandle:Nd,useInsertionEffect:Sd,useLayoutEffect:kd,useMemo:Td,useReducer:Fl,useRef:wd,useState:function(){return Fl(ss)},useDebugValue:Da,useDeferredValue:function(e){var t=Qe();return ie===null?t.memoizedState=e:Cd(t,ie.memoizedState,e)},useTransition:function(){var e=Fl(ss)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:dd,useSyncExternalStore:hd,useId:_d,unstable_isNewReconciler:!1};function tr(e,t){try{var n="",r=t;do n+=sm(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function zl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Mo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ig=typeof WeakMap=="function"?WeakMap:Map;function Id(e,t,n){n=vt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$i||($i=!0,Ko=r),Mo(e,t)},n}function Ad(e,t,n){n=vt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Mo(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Mo(e,t),typeof r!="function"&&(Dt===null?Dt=new Set([this]):Dt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function sc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ig;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=Wg.bind(null,e,t,n),t.then(e,e))}function ic(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function lc(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=vt(-1,1),t.tag=2,Rt(n,t,1))),n.lanes|=1),e)}var Ag=xt.ReactCurrentOwner,$e=!1;function ke(e,t,n,r){t.child=e===null?cd(t,null,n,r):Zn(t,e.child,n,r)}function oc(e,t,n,r,s){n=n.render;var i=t.ref;return Hn(t,s),r=Ma(e,t,n,r,i,s),n=ja(),e!==null&&!$e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Nt(e,t,s)):(W&&n&&Ea(t),t.flags|=1,ke(e,t,r,s),t.child)}function ac(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Wa(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Pd(e,t,i,r,s)):(e=ei(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:Gr,n(l,r)&&e.ref===t.ref)return Nt(e,t,s)}return t.flags|=1,e=Bt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Pd(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Gr(i,r)&&e.ref===t.ref)if($e=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&($e=!0);else return t.lanes=e.lanes,Nt(e,t,s)}return jo(e,t,n,r,s)}function Md(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(jn,je),je|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(jn,je),je|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,U(jn,je),je|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,U(jn,je),je|=r;return ke(e,t,s,n),t.child}function jd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function jo(e,t,n,r,s){var i=Ae(n)?cn:Se.current;return i=Gn(t,i),Hn(t,s),n=Ma(e,t,n,r,i,s),r=ja(),e!==null&&!$e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Nt(e,t,s)):(W&&r&&Ea(t),t.flags|=1,ke(e,t,n,s),t.child)}function uc(e,t,n,r,s){if(Ae(n)){var i=!0;Si(t)}else i=!1;if(Hn(t,s),t.stateNode===null)Gs(e,t),ad(t,n,r),Po(t,n,r,s),r=!0;else if(e===null){var l=t.stateNode,o=t.memoizedProps;l.props=o;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=qe(u):(u=Ae(n)?cn:Se.current,u=Gn(t,u));var m=n.getDerivedStateFromProps,f=typeof m=="function"||typeof l.getSnapshotBeforeUpdate=="function";f||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(o!==r||a!==u)&&ec(t,l,r,u),_t=!1;var p=t.memoizedState;l.state=p,Ti(t,r,l,s),a=t.memoizedState,o!==r||p!==a||Ie.current||_t?(typeof m=="function"&&(Ao(t,n,m,r),a=t.memoizedState),(o=_t||Zu(t,n,o,r,p,a,u))?(f||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=o):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,ld(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:Xe(t.type,o),l.props=u,f=t.pendingProps,p=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=qe(a):(a=Ae(n)?cn:Se.current,a=Gn(t,a));var g=n.getDerivedStateFromProps;(m=typeof g=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(o!==f||p!==a)&&ec(t,l,r,a),_t=!1,p=t.memoizedState,l.state=p,Ti(t,r,l,s);var w=t.memoizedState;o!==f||p!==w||Ie.current||_t?(typeof g=="function"&&(Ao(t,n,g,r),w=t.memoizedState),(u=_t||Zu(t,n,u,r,p,w,a)||!1)?(m||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,w,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,w,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),l.props=r,l.state=w,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ro(e,t,n,r,i,s)}function Ro(e,t,n,r,s,i){jd(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return s&&Qu(t,n,!1),Nt(e,t,i);r=t.stateNode,Ag.current=t;var o=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=Zn(t,e.child,null,i),t.child=Zn(t,null,o,i)):ke(e,t,o,i),t.memoizedState=r.state,s&&Qu(t,n,!0),t.child}function Rd(e){var t=e.stateNode;t.pendingContext?qu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&qu(e,t.context,!1),$a(e,t.containerInfo)}function cc(e,t,n,r,s){return Xn(),xa(s),t.flags|=256,ke(e,t,n,r),t.child}var Do={dehydrated:null,treeContext:null,retryLane:0};function bo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Dd(e,t,n){var r=t.pendingProps,s=q.current,i=!1,l=(t.flags&128)!==0,o;if((o=l)||(o=e!==null&&e.memoizedState===null?!1:(s&2)!==0),o?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),U(q,s&1),e===null)return $o(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=Gi(l,r,0,null),e=un(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=bo(n),t.memoizedState=Do,e):ba(t,l));if(s=e.memoizedState,s!==null&&(o=s.dehydrated,o!==null))return Pg(e,t,l,r,o,s,n);if(i){i=r.fallback,l=t.mode,s=e.child,o=s.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Bt(s,a),r.subtreeFlags=s.subtreeFlags&14680064),o!==null?i=Bt(o,i):(i=un(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?bo(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Do,r}return i=e.child,e=i.sibling,r=Bt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ba(e,t){return t=Gi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ms(e,t,n,r){return r!==null&&xa(r),Zn(t,e.child,null,n),e=ba(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Pg(e,t,n,r,s,i,l){if(n)return t.flags&256?(t.flags&=-257,r=zl(Error(_(422))),Ms(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=Gi({mode:"visible",children:r.children},s,0,null),i=un(i,s,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Zn(t,e.child,null,l),t.child.memoizedState=bo(l),t.memoizedState=Do,i);if(!(t.mode&1))return Ms(e,t,l,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var o=r.dgst;return r=o,i=Error(_(419)),r=zl(i,r,void 0),Ms(e,t,l,r)}if(o=(l&e.childLanes)!==0,$e||o){if(r=ue,r!==null){switch(l&-l){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|l)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,Et(e,s),nt(r,e,s,-1))}return Ka(),r=zl(Error(_(421))),Ms(e,t,l,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=Hg.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,De=jt(s.nextSibling),be=t,W=!0,et=null,e!==null&&(Ue[Ve++]=mt,Ue[Ve++]=gt,Ue[Ve++]=fn,mt=e.id,gt=e.overflow,fn=t),t=ba(t,r.children),t.flags|=4096,t)}function fc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Io(e.return,t,n)}function Ul(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function bd(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(ke(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&fc(e,n,t);else if(e.tag===19)fc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(q,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Ci(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Ul(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Ci(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Ul(t,!0,n,null,i);break;case"together":Ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Nt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),hn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Mg(e,t,n){switch(t.tag){case 3:Rd(t),Xn();break;case 5:fd(t);break;case 1:Ae(t.type)&&Si(t);break;case 4:$a(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;U(Ni,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Dd(e,t,n):(U(q,q.current&1),e=Nt(e,t,n),e!==null?e.sibling:null);U(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return bd(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),U(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Md(e,t,n)}return Nt(e,t,n)}var Bd,Bo,Fd,zd;Bd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Bo=function(){};Fd=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,nn(ut.current);var i=null;switch(n){case"input":s=lo(e,s),r=lo(e,r),i=[];break;case"select":s=G({},s,{value:void 0}),r=G({},r,{value:void 0}),i=[];break;case"textarea":s=uo(e,s),r=uo(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=vi)}fo(n,r);var l;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var o=s[u];for(l in o)o.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Kr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(o=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&a!==o&&(a!=null||o!=null))if(u==="style")if(o){for(l in o)!o.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&o[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,o=o?o.__html:void 0,a!=null&&o!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Kr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&V("scroll",e),i||o===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};zd=function(e,t,n,r){n!==r&&(t.flags|=4)};function Tr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ge(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function jg(e,t,n){var r=t.pendingProps;switch(Na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ge(t),null;case 1:return Ae(t.type)&&wi(),ge(t),null;case 3:return r=t.stateNode,er(),K(Ie),K(Se),Aa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(As(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,et!==null&&(qo(et),et=null))),Bo(e,t),ge(t),null;case 5:Ia(t);var s=nn(ns.current);if(n=t.type,e!==null&&t.stateNode!=null)Fd(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return ge(t),null}if(e=nn(ut.current),As(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ot]=t,r[es]=i,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(s=0;s<Ar.length;s++)V(Ar[s],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":Su(r,i),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},V("invalid",r);break;case"textarea":Eu(r,i),V("invalid",r)}fo(n,i),s=null;for(var l in i)if(i.hasOwnProperty(l)){var o=i[l];l==="children"?typeof o=="string"?r.textContent!==o&&(i.suppressHydrationWarning!==!0&&Is(r.textContent,o,e),s=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(i.suppressHydrationWarning!==!0&&Is(r.textContent,o,e),s=["children",""+o]):Kr.hasOwnProperty(l)&&o!=null&&l==="onScroll"&&V("scroll",r)}switch(n){case"input":Ns(r),ku(r,i,!0);break;case"textarea":Ns(r),Nu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=vi)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=pf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[ot]=t,e[es]=r,Bd(e,t,!1,!1),t.stateNode=e;e:{switch(l=ho(n,r),n){case"dialog":V("cancel",e),V("close",e),s=r;break;case"iframe":case"object":case"embed":V("load",e),s=r;break;case"video":case"audio":for(s=0;s<Ar.length;s++)V(Ar[s],e);s=r;break;case"source":V("error",e),s=r;break;case"img":case"image":case"link":V("error",e),V("load",e),s=r;break;case"details":V("toggle",e),s=r;break;case"input":Su(e,r),s=lo(e,r),V("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=G({},r,{value:void 0}),V("invalid",e);break;case"textarea":Eu(e,r),s=uo(e,r),V("invalid",e);break;default:s=r}fo(n,s),o=s;for(i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="style"?yf(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&mf(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Wr(e,a):typeof a=="number"&&Wr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Kr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&V("scroll",e):a!=null&&aa(e,i,a,l))}switch(n){case"input":Ns(e),ku(e,r,!1);break;case"textarea":Ns(e),Nu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Un(e,!!r.multiple,i,!1):r.defaultValue!=null&&Un(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=vi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ge(t),null;case 6:if(e&&t.stateNode!=null)zd(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=nn(ns.current),nn(ut.current),As(t)){if(r=t.stateNode,n=t.memoizedProps,r[ot]=t,(i=r.nodeValue!==n)&&(e=be,e!==null))switch(e.tag){case 3:Is(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Is(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ot]=t,t.stateNode=r}return ge(t),null;case 13:if(K(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&De!==null&&t.mode&1&&!(t.flags&128))sd(),Xn(),t.flags|=98560,i=!1;else if(i=As(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(_(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(_(317));i[ot]=t}else Xn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ge(t),i=!1}else et!==null&&(qo(et),et=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?le===0&&(le=3):Ka())),t.updateQueue!==null&&(t.flags|=4),ge(t),null);case 4:return er(),Bo(e,t),e===null&&Xr(t.stateNode.containerInfo),ge(t),null;case 10:return _a(t.type._context),ge(t),null;case 17:return Ae(t.type)&&wi(),ge(t),null;case 19:if(K(q),i=t.memoizedState,i===null)return ge(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Tr(i,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Ci(e),l!==null){for(t.flags|=128,Tr(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(q,q.current&1|2),t.child}e=e.sibling}i.tail!==null&&ee()>nr&&(t.flags|=128,r=!0,Tr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ci(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Tr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!W)return ge(t),null}else 2*ee()-i.renderingStartTime>nr&&n!==1073741824&&(t.flags|=128,r=!0,Tr(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ee(),t.sibling=null,n=q.current,U(q,r?n&1|2:n&1),t):(ge(t),null);case 22:case 23:return Va(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?je&1073741824&&(ge(t),t.subtreeFlags&6&&(t.flags|=8192)):ge(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function Rg(e,t){switch(Na(t),t.tag){case 1:return Ae(t.type)&&wi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return er(),K(Ie),K(Se),Aa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ia(t),null;case 13:if(K(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Xn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(q),null;case 4:return er(),null;case 10:return _a(t.type._context),null;case 22:case 23:return Va(),null;case 24:return null;default:return null}}var js=!1,ve=!1,Dg=typeof WeakSet=="function"?WeakSet:Set,$=null;function Mn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function Fo(e,t,n){try{n()}catch(r){X(e,t,r)}}var dc=!1;function bg(e,t){if(No=mi,e=Wf(),ka(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,o=-1,a=-1,u=0,m=0,f=e,p=null;t:for(;;){for(var g;f!==n||s!==0&&f.nodeType!==3||(o=l+s),f!==i||r!==0&&f.nodeType!==3||(a=l+r),f.nodeType===3&&(l+=f.nodeValue.length),(g=f.firstChild)!==null;)p=f,f=g;for(;;){if(f===e)break t;if(p===n&&++u===s&&(o=l),p===i&&++m===r&&(a=l),(g=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=g}n=o===-1||a===-1?null:{start:o,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(xo={focusedElem:e,selectionRange:n},mi=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var h=w.memoizedProps,v=w.memoizedState,d=t.stateNode,c=d.getSnapshotBeforeUpdate(t.elementType===t.type?h:Xe(t.type,h),v);d.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(S){X(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return w=dc,dc=!1,w}function Fr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Fo(t,n,i)}s=s.next}while(s!==r)}}function Yi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function zo(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ud(e){var t=e.alternate;t!==null&&(e.alternate=null,Ud(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ot],delete t[es],delete t[_o],delete t[Sg],delete t[kg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Vd(e){return e.tag===5||e.tag===3||e.tag===4}function hc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Vd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Uo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=vi));else if(r!==4&&(e=e.child,e!==null))for(Uo(e,t,n),e=e.sibling;e!==null;)Uo(e,t,n),e=e.sibling}function Vo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Vo(e,t,n),e=e.sibling;e!==null;)Vo(e,t,n),e=e.sibling}var ce=null,Ze=!1;function Tt(e,t,n){for(n=n.child;n!==null;)Kd(e,t,n),n=n.sibling}function Kd(e,t,n){if(at&&typeof at.onCommitFiberUnmount=="function")try{at.onCommitFiberUnmount(zi,n)}catch{}switch(n.tag){case 5:ve||Mn(n,t);case 6:var r=ce,s=Ze;ce=null,Tt(e,t,n),ce=r,Ze=s,ce!==null&&(Ze?(e=ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ce.removeChild(n.stateNode));break;case 18:ce!==null&&(Ze?(e=ce,n=n.stateNode,e.nodeType===8?jl(e.parentNode,n):e.nodeType===1&&jl(e,n),Yr(e)):jl(ce,n.stateNode));break;case 4:r=ce,s=Ze,ce=n.stateNode.containerInfo,Ze=!0,Tt(e,t,n),ce=r,Ze=s;break;case 0:case 11:case 14:case 15:if(!ve&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&Fo(n,t,l),s=s.next}while(s!==r)}Tt(e,t,n);break;case 1:if(!ve&&(Mn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){X(n,t,o)}Tt(e,t,n);break;case 21:Tt(e,t,n);break;case 22:n.mode&1?(ve=(r=ve)||n.memoizedState!==null,Tt(e,t,n),ve=r):Tt(e,t,n);break;default:Tt(e,t,n)}}function pc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Dg),t.forEach(function(r){var s=qg.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Je(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,l=t,o=l;e:for(;o!==null;){switch(o.tag){case 5:ce=o.stateNode,Ze=!1;break e;case 3:ce=o.stateNode.containerInfo,Ze=!0;break e;case 4:ce=o.stateNode.containerInfo,Ze=!0;break e}o=o.return}if(ce===null)throw Error(_(160));Kd(i,l,s),ce=null,Ze=!1;var a=s.alternate;a!==null&&(a.return=null),s.return=null}catch(u){X(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Wd(t,e),t=t.sibling}function Wd(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Je(t,e),it(e),r&4){try{Fr(3,e,e.return),Yi(3,e)}catch(h){X(e,e.return,h)}try{Fr(5,e,e.return)}catch(h){X(e,e.return,h)}}break;case 1:Je(t,e),it(e),r&512&&n!==null&&Mn(n,n.return);break;case 5:if(Je(t,e),it(e),r&512&&n!==null&&Mn(n,n.return),e.flags&32){var s=e.stateNode;try{Wr(s,"")}catch(h){X(e,e.return,h)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,o=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{o==="input"&&i.type==="radio"&&i.name!=null&&df(s,i),ho(o,l);var u=ho(o,i);for(l=0;l<a.length;l+=2){var m=a[l],f=a[l+1];m==="style"?yf(s,f):m==="dangerouslySetInnerHTML"?mf(s,f):m==="children"?Wr(s,f):aa(s,m,f,u)}switch(o){case"input":oo(s,i);break;case"textarea":hf(s,i);break;case"select":var p=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Un(s,!!i.multiple,g,!1):p!==!!i.multiple&&(i.defaultValue!=null?Un(s,!!i.multiple,i.defaultValue,!0):Un(s,!!i.multiple,i.multiple?[]:"",!1))}s[es]=i}catch(h){X(e,e.return,h)}}break;case 6:if(Je(t,e),it(e),r&4){if(e.stateNode===null)throw Error(_(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(h){X(e,e.return,h)}}break;case 3:if(Je(t,e),it(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Yr(t.containerInfo)}catch(h){X(e,e.return,h)}break;case 4:Je(t,e),it(e);break;case 13:Je(t,e),it(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(za=ee())),r&4&&pc(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(ve=(u=ve)||m,Je(t,e),ve=u):Je(t,e),it(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!m&&e.mode&1)for($=e,m=e.child;m!==null;){for(f=$=m;$!==null;){switch(p=$,g=p.child,p.tag){case 0:case 11:case 14:case 15:Fr(4,p,p.return);break;case 1:Mn(p,p.return);var w=p.stateNode;if(typeof w.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(h){X(r,n,h)}}break;case 5:Mn(p,p.return);break;case 22:if(p.memoizedState!==null){gc(f);continue}}g!==null?(g.return=p,$=g):gc(f)}m=m.sibling}e:for(m=null,f=e;;){if(f.tag===5){if(m===null){m=f;try{s=f.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(o=f.stateNode,a=f.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,o.style.display=gf("display",l))}catch(h){X(e,e.return,h)}}}else if(f.tag===6){if(m===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(h){X(e,e.return,h)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;m===f&&(m=null),f=f.return}m===f&&(m=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Je(t,e),it(e),r&4&&pc(e);break;case 21:break;default:Je(t,e),it(e)}}function it(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Vd(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Wr(s,""),r.flags&=-33);var i=hc(e);Vo(e,i,s);break;case 3:case 4:var l=r.stateNode.containerInfo,o=hc(e);Uo(e,o,l);break;default:throw Error(_(161))}}catch(a){X(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Bg(e,t,n){$=e,Hd(e)}function Hd(e,t,n){for(var r=(e.mode&1)!==0;$!==null;){var s=$,i=s.child;if(s.tag===22&&r){var l=s.memoizedState!==null||js;if(!l){var o=s.alternate,a=o!==null&&o.memoizedState!==null||ve;o=js;var u=ve;if(js=l,(ve=a)&&!u)for($=s;$!==null;)l=$,a=l.child,l.tag===22&&l.memoizedState!==null?yc(s):a!==null?(a.return=l,$=a):yc(s);for(;i!==null;)$=i,Hd(i),i=i.sibling;$=s,js=o,ve=u}mc(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,$=i):mc(e)}}function mc(e){for(;$!==null;){var t=$;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ve||Yi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ve)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Xe(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Xu(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Xu(t,l,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var m=u.memoizedState;if(m!==null){var f=m.dehydrated;f!==null&&Yr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}ve||t.flags&512&&zo(t)}catch(p){X(t,t.return,p)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function gc(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function yc(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Yi(4,t)}catch(a){X(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(a){X(t,s,a)}}var i=t.return;try{zo(t)}catch(a){X(t,i,a)}break;case 5:var l=t.return;try{zo(t)}catch(a){X(t,l,a)}}}catch(a){X(t,t.return,a)}if(t===e){$=null;break}var o=t.sibling;if(o!==null){o.return=t.return,$=o;break}$=t.return}}var Fg=Math.ceil,Oi=xt.ReactCurrentDispatcher,Ba=xt.ReactCurrentOwner,We=xt.ReactCurrentBatchConfig,b=0,ue=null,re=null,de=0,je=0,jn=Ht(0),le=0,ls=null,hn=0,Ji=0,Fa=0,zr=null,_e=null,za=0,nr=1/0,ht=null,$i=!1,Ko=null,Dt=null,Rs=!1,It=null,Ii=0,Ur=0,Wo=null,Xs=-1,Zs=0;function Ne(){return b&6?ee():Xs!==-1?Xs:Xs=ee()}function bt(e){return e.mode&1?b&2&&de!==0?de&-de:Ng.transition!==null?(Zs===0&&(Zs=Of()),Zs):(e=F,e!==0||(e=window.event,e=e===void 0?16:Rf(e.type)),e):1}function nt(e,t,n,r){if(50<Ur)throw Ur=0,Wo=null,Error(_(185));fs(e,n,r),(!(b&2)||e!==ue)&&(e===ue&&(!(b&2)&&(Ji|=n),le===4&&Ot(e,de)),Pe(e,r),n===1&&b===0&&!(t.mode&1)&&(nr=ee()+500,Hi&&qt()))}function Pe(e,t){var n=e.callbackNode;Nm(e,t);var r=pi(e,e===ue?de:0);if(r===0)n!==null&&Cu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Cu(n),t===1)e.tag===0?Eg(vc.bind(null,e)):td(vc.bind(null,e)),vg(function(){!(b&6)&&qt()}),n=null;else{switch($f(r)){case 1:n=ha;break;case 4:n=_f;break;case 16:n=hi;break;case 536870912:n=Lf;break;default:n=hi}n=eh(n,qd.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function qd(e,t){if(Xs=-1,Zs=0,b&6)throw Error(_(327));var n=e.callbackNode;if(qn()&&e.callbackNode!==n)return null;var r=pi(e,e===ue?de:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ai(e,r);else{t=r;var s=b;b|=2;var i=Yd();(ue!==e||de!==t)&&(ht=null,nr=ee()+500,an(e,t));do try{Vg();break}catch(o){Qd(e,o)}while(!0);Ca(),Oi.current=i,b=s,re!==null?t=0:(ue=null,de=0,t=le)}if(t!==0){if(t===2&&(s=vo(e),s!==0&&(r=s,t=Ho(e,s))),t===1)throw n=ls,an(e,0),Ot(e,r),Pe(e,ee()),n;if(t===6)Ot(e,r);else{if(s=e.current.alternate,!(r&30)&&!zg(s)&&(t=Ai(e,r),t===2&&(i=vo(e),i!==0&&(r=i,t=Ho(e,i))),t===1))throw n=ls,an(e,0),Ot(e,r),Pe(e,ee()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:Gt(e,_e,ht);break;case 3:if(Ot(e,r),(r&130023424)===r&&(t=za+500-ee(),10<t)){if(pi(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Ne(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Co(Gt.bind(null,e,_e,ht),t);break}Gt(e,_e,ht);break;case 4:if(Ot(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var l=31-tt(r);i=1<<l,l=t[l],l>s&&(s=l),r&=~i}if(r=s,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Fg(r/1960))-r,10<r){e.timeoutHandle=Co(Gt.bind(null,e,_e,ht),r);break}Gt(e,_e,ht);break;case 5:Gt(e,_e,ht);break;default:throw Error(_(329))}}}return Pe(e,ee()),e.callbackNode===n?qd.bind(null,e):null}function Ho(e,t){var n=zr;return e.current.memoizedState.isDehydrated&&(an(e,t).flags|=256),e=Ai(e,t),e!==2&&(t=_e,_e=n,t!==null&&qo(t)),e}function qo(e){_e===null?_e=e:_e.push.apply(_e,e)}function zg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!rt(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ot(e,t){for(t&=~Fa,t&=~Ji,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-tt(t),r=1<<n;e[n]=-1,t&=~r}}function vc(e){if(b&6)throw Error(_(327));qn();var t=pi(e,0);if(!(t&1))return Pe(e,ee()),null;var n=Ai(e,t);if(e.tag!==0&&n===2){var r=vo(e);r!==0&&(t=r,n=Ho(e,r))}if(n===1)throw n=ls,an(e,0),Ot(e,t),Pe(e,ee()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Gt(e,_e,ht),Pe(e,ee()),null}function Ua(e,t){var n=b;b|=1;try{return e(t)}finally{b=n,b===0&&(nr=ee()+500,Hi&&qt())}}function pn(e){It!==null&&It.tag===0&&!(b&6)&&qn();var t=b;b|=1;var n=We.transition,r=F;try{if(We.transition=null,F=1,e)return e()}finally{F=r,We.transition=n,b=t,!(b&6)&&qt()}}function Va(){je=jn.current,K(jn)}function an(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,yg(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(Na(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&wi();break;case 3:er(),K(Ie),K(Se),Aa();break;case 5:Ia(r);break;case 4:er();break;case 13:K(q);break;case 19:K(q);break;case 10:_a(r.type._context);break;case 22:case 23:Va()}n=n.return}if(ue=e,re=e=Bt(e.current,null),de=je=t,le=0,ls=null,Fa=Ji=hn=0,_e=zr=null,tn!==null){for(t=0;t<tn.length;t++)if(n=tn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=s,r.next=l}n.pending=r}tn=null}return e}function Qd(e,t){do{var n=re;try{if(Ca(),Ys.current=Li,_i){for(var r=Y.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}_i=!1}if(dn=0,ae=ie=Y=null,Br=!1,rs=0,Ba.current=null,n===null||n.return===null){le=1,ls=t,re=null;break}e:{var i=e,l=n.return,o=n,a=t;if(t=de,o.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,m=o,f=m.tag;if(!(m.mode&1)&&(f===0||f===11||f===15)){var p=m.alternate;p?(m.updateQueue=p.updateQueue,m.memoizedState=p.memoizedState,m.lanes=p.lanes):(m.updateQueue=null,m.memoizedState=null)}var g=ic(l);if(g!==null){g.flags&=-257,lc(g,l,o,i,t),g.mode&1&&sc(i,u,t),t=g,a=u;var w=t.updateQueue;if(w===null){var h=new Set;h.add(a),t.updateQueue=h}else w.add(a);break e}else{if(!(t&1)){sc(i,u,t),Ka();break e}a=Error(_(426))}}else if(W&&o.mode&1){var v=ic(l);if(v!==null){!(v.flags&65536)&&(v.flags|=256),lc(v,l,o,i,t),xa(tr(a,o));break e}}i=a=tr(a,o),le!==4&&(le=2),zr===null?zr=[i]:zr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var d=Id(i,a,t);Gu(i,d);break e;case 1:o=a;var c=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Dt===null||!Dt.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=Ad(i,o,t);Gu(i,S);break e}}i=i.return}while(i!==null)}Gd(n)}catch(x){t=x,re===n&&n!==null&&(re=n=n.return);continue}break}while(!0)}function Yd(){var e=Oi.current;return Oi.current=Li,e===null?Li:e}function Ka(){(le===0||le===3||le===2)&&(le=4),ue===null||!(hn&268435455)&&!(Ji&268435455)||Ot(ue,de)}function Ai(e,t){var n=b;b|=2;var r=Yd();(ue!==e||de!==t)&&(ht=null,an(e,t));do try{Ug();break}catch(s){Qd(e,s)}while(!0);if(Ca(),b=n,Oi.current=r,re!==null)throw Error(_(261));return ue=null,de=0,le}function Ug(){for(;re!==null;)Jd(re)}function Vg(){for(;re!==null&&!pm();)Jd(re)}function Jd(e){var t=Zd(e.alternate,e,je);e.memoizedProps=e.pendingProps,t===null?Gd(e):re=t,Ba.current=null}function Gd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Rg(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,re=null;return}}else if(n=jg(n,t,je),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);le===0&&(le=5)}function Gt(e,t,n){var r=F,s=We.transition;try{We.transition=null,F=1,Kg(e,t,n,r)}finally{We.transition=s,F=r}return null}function Kg(e,t,n,r){do qn();while(It!==null);if(b&6)throw Error(_(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(xm(e,i),e===ue&&(re=ue=null,de=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Rs||(Rs=!0,eh(hi,function(){return qn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=We.transition,We.transition=null;var l=F;F=1;var o=b;b|=4,Ba.current=null,bg(e,n),Wd(n,e),cg(xo),mi=!!No,xo=No=null,e.current=n,Bg(n),mm(),b=o,F=l,We.transition=i}else e.current=n;if(Rs&&(Rs=!1,It=e,Ii=s),i=e.pendingLanes,i===0&&(Dt=null),vm(n.stateNode),Pe(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if($i)throw $i=!1,e=Ko,Ko=null,e;return Ii&1&&e.tag!==0&&qn(),i=e.pendingLanes,i&1?e===Wo?Ur++:(Ur=0,Wo=e):Ur=0,qt(),null}function qn(){if(It!==null){var e=$f(Ii),t=We.transition,n=F;try{if(We.transition=null,F=16>e?16:e,It===null)var r=!1;else{if(e=It,It=null,Ii=0,b&6)throw Error(_(331));var s=b;for(b|=4,$=e.current;$!==null;){var i=$,l=i.child;if($.flags&16){var o=i.deletions;if(o!==null){for(var a=0;a<o.length;a++){var u=o[a];for($=u;$!==null;){var m=$;switch(m.tag){case 0:case 11:case 15:Fr(8,m,i)}var f=m.child;if(f!==null)f.return=m,$=f;else for(;$!==null;){m=$;var p=m.sibling,g=m.return;if(Ud(m),m===u){$=null;break}if(p!==null){p.return=g,$=p;break}$=g}}}var w=i.alternate;if(w!==null){var h=w.child;if(h!==null){w.child=null;do{var v=h.sibling;h.sibling=null,h=v}while(h!==null)}}$=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,$=l;else e:for(;$!==null;){if(i=$,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Fr(9,i,i.return)}var d=i.sibling;if(d!==null){d.return=i.return,$=d;break e}$=i.return}}var c=e.current;for($=c;$!==null;){l=$;var y=l.child;if(l.subtreeFlags&2064&&y!==null)y.return=l,$=y;else e:for(l=c;$!==null;){if(o=$,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Yi(9,o)}}catch(x){X(o,o.return,x)}if(o===l){$=null;break e}var S=o.sibling;if(S!==null){S.return=o.return,$=S;break e}$=o.return}}if(b=s,qt(),at&&typeof at.onPostCommitFiberRoot=="function")try{at.onPostCommitFiberRoot(zi,e)}catch{}r=!0}return r}finally{F=n,We.transition=t}}return!1}function wc(e,t,n){t=tr(n,t),t=Id(e,t,1),e=Rt(e,t,1),t=Ne(),e!==null&&(fs(e,1,t),Pe(e,t))}function X(e,t,n){if(e.tag===3)wc(e,e,n);else for(;t!==null;){if(t.tag===3){wc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Dt===null||!Dt.has(r))){e=tr(n,e),e=Ad(t,e,1),t=Rt(t,e,1),e=Ne(),t!==null&&(fs(t,1,e),Pe(t,e));break}}t=t.return}}function Wg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ne(),e.pingedLanes|=e.suspendedLanes&n,ue===e&&(de&n)===n&&(le===4||le===3&&(de&130023424)===de&&500>ee()-za?an(e,0):Fa|=n),Pe(e,t)}function Xd(e,t){t===0&&(e.mode&1?(t=Cs,Cs<<=1,!(Cs&130023424)&&(Cs=4194304)):t=1);var n=Ne();e=Et(e,t),e!==null&&(fs(e,t,n),Pe(e,n))}function Hg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Xd(e,n)}function qg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),Xd(e,n)}var Zd;Zd=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ie.current)$e=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return $e=!1,Mg(e,t,n);$e=!!(e.flags&131072)}else $e=!1,W&&t.flags&1048576&&nd(t,Ei,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Gs(e,t),e=t.pendingProps;var s=Gn(t,Se.current);Hn(t,n),s=Ma(null,t,r,e,s,n);var i=ja();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ae(r)?(i=!0,Si(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Oa(t),s.updater=qi,t.stateNode=s,s._reactInternals=t,Po(t,r,e,n),t=Ro(null,t,r,!0,i,n)):(t.tag=0,W&&i&&Ea(t),ke(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Gs(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=Yg(r),e=Xe(r,e),s){case 0:t=jo(null,t,r,e,n);break e;case 1:t=uc(null,t,r,e,n);break e;case 11:t=oc(null,t,r,e,n);break e;case 14:t=ac(null,t,r,Xe(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Xe(r,s),jo(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Xe(r,s),uc(e,t,r,s,n);case 3:e:{if(Rd(t),e===null)throw Error(_(387));r=t.pendingProps,i=t.memoizedState,s=i.element,ld(e,t),Ti(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=tr(Error(_(423)),t),t=cc(e,t,r,n,s);break e}else if(r!==s){s=tr(Error(_(424)),t),t=cc(e,t,r,n,s);break e}else for(De=jt(t.stateNode.containerInfo.firstChild),be=t,W=!0,et=null,n=cd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Xn(),r===s){t=Nt(e,t,n);break e}ke(e,t,r,n)}t=t.child}return t;case 5:return fd(t),e===null&&$o(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,l=s.children,To(r,s)?l=null:i!==null&&To(r,i)&&(t.flags|=32),jd(e,t),ke(e,t,l,n),t.child;case 6:return e===null&&$o(t),null;case 13:return Dd(e,t,n);case 4:return $a(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Zn(t,null,r,n):ke(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Xe(r,s),oc(e,t,r,s,n);case 7:return ke(e,t,t.pendingProps,n),t.child;case 8:return ke(e,t,t.pendingProps.children,n),t.child;case 12:return ke(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,l=s.value,U(Ni,r._currentValue),r._currentValue=l,i!==null)if(rt(i.value,l)){if(i.children===s.children&&!Ie.current){t=Nt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var o=i.dependencies;if(o!==null){l=i.child;for(var a=o.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=vt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var m=u.pending;m===null?a.next=a:(a.next=m.next,m.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Io(i.return,n,t),o.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(_(341));l.lanes|=n,o=l.alternate,o!==null&&(o.lanes|=n),Io(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ke(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Hn(t,n),s=qe(s),r=r(s),t.flags|=1,ke(e,t,r,n),t.child;case 14:return r=t.type,s=Xe(r,t.pendingProps),s=Xe(r.type,s),ac(e,t,r,s,n);case 15:return Pd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Xe(r,s),Gs(e,t),t.tag=1,Ae(r)?(e=!0,Si(t)):e=!1,Hn(t,n),ad(t,r,s),Po(t,r,s,n),Ro(null,t,r,!0,e,n);case 19:return bd(e,t,n);case 22:return Md(e,t,n)}throw Error(_(156,t.tag))};function eh(e,t){return Cf(e,t)}function Qg(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(e,t,n,r){return new Qg(e,t,n,r)}function Wa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Yg(e){if(typeof e=="function")return Wa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ca)return 11;if(e===fa)return 14}return 2}function Bt(e,t){var n=e.alternate;return n===null?(n=Ke(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ei(e,t,n,r,s,i){var l=2;if(r=e,typeof e=="function")Wa(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case Tn:return un(n.children,s,i,t);case ua:l=8,s|=8;break;case no:return e=Ke(12,n,t,s|2),e.elementType=no,e.lanes=i,e;case ro:return e=Ke(13,n,t,s),e.elementType=ro,e.lanes=i,e;case so:return e=Ke(19,n,t,s),e.elementType=so,e.lanes=i,e;case uf:return Gi(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case of:l=10;break e;case af:l=9;break e;case ca:l=11;break e;case fa:l=14;break e;case Ct:l=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=Ke(l,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function un(e,t,n,r){return e=Ke(7,e,r,t),e.lanes=n,e}function Gi(e,t,n,r){return e=Ke(22,e,r,t),e.elementType=uf,e.lanes=n,e.stateNode={isHidden:!1},e}function Vl(e,t,n){return e=Ke(6,e,null,t),e.lanes=n,e}function Kl(e,t,n){return t=Ke(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Jg(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=xl(0),this.expirationTimes=xl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xl(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Ha(e,t,n,r,s,i,l,o,a){return e=new Jg(e,t,n,o,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ke(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oa(i),e}function Gg(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function th(e){if(!e)return Ut;e=e._reactInternals;e:{if(yn(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ae(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(Ae(n))return ed(e,n,t)}return t}function nh(e,t,n,r,s,i,l,o,a){return e=Ha(n,r,!0,e,s,i,l,o,a),e.context=th(null),n=e.current,r=Ne(),s=bt(n),i=vt(r,s),i.callback=t??null,Rt(n,i,s),e.current.lanes=s,fs(e,s,r),Pe(e,r),e}function Xi(e,t,n,r){var s=t.current,i=Ne(),l=bt(s);return n=th(n),t.context===null?t.context=n:t.pendingContext=n,t=vt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Rt(s,t,l),e!==null&&(nt(e,s,l,i),Qs(e,s,l)),l}function Pi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Sc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function qa(e,t){Sc(e,t),(e=e.alternate)&&Sc(e,t)}function Xg(){return null}var rh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Qa(e){this._internalRoot=e}Zi.prototype.render=Qa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));Xi(e,t,null,null)};Zi.prototype.unmount=Qa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;pn(function(){Xi(null,e,null,null)}),t[kt]=null}};function Zi(e){this._internalRoot=e}Zi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&t!==0&&t<Lt[n].priority;n++);Lt.splice(n,0,e),n===0&&jf(e)}};function Ya(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function el(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function kc(){}function Zg(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=Pi(l);i.call(u)}}var l=nh(t,r,e,0,null,!1,!1,"",kc);return e._reactRootContainer=l,e[kt]=l.current,Xr(e.nodeType===8?e.parentNode:e),pn(),l}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var o=r;r=function(){var u=Pi(a);o.call(u)}}var a=Ha(e,0,!1,null,null,!1,!1,"",kc);return e._reactRootContainer=a,e[kt]=a.current,Xr(e.nodeType===8?e.parentNode:e),pn(function(){Xi(t,a,n,r)}),a}function tl(e,t,n,r,s){var i=n._reactRootContainer;if(i){var l=i;if(typeof s=="function"){var o=s;s=function(){var a=Pi(l);o.call(a)}}Xi(t,l,e,s)}else l=Zg(n,t,e,s,r);return Pi(l)}If=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ir(t.pendingLanes);n!==0&&(pa(t,n|1),Pe(t,ee()),!(b&6)&&(nr=ee()+500,qt()))}break;case 13:pn(function(){var r=Et(e,1);if(r!==null){var s=Ne();nt(r,e,1,s)}}),qa(e,1)}};ma=function(e){if(e.tag===13){var t=Et(e,134217728);if(t!==null){var n=Ne();nt(t,e,134217728,n)}qa(e,134217728)}};Af=function(e){if(e.tag===13){var t=bt(e),n=Et(e,t);if(n!==null){var r=Ne();nt(n,e,t,r)}qa(e,t)}};Pf=function(){return F};Mf=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};mo=function(e,t,n){switch(t){case"input":if(oo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Wi(r);if(!s)throw Error(_(90));ff(r),oo(r,s)}}}break;case"textarea":hf(e,n);break;case"select":t=n.value,t!=null&&Un(e,!!n.multiple,t,!1)}};Sf=Ua;kf=pn;var ey={usingClientEntryPoint:!1,Events:[hs,On,Wi,vf,wf,Ua]},Cr={findFiberByHostInstance:en,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},ty={bundleType:Cr.bundleType,version:Cr.version,rendererPackageName:Cr.rendererPackageName,rendererConfig:Cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=xf(e),e===null?null:e.stateNode},findFiberByHostInstance:Cr.findFiberByHostInstance||Xg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ds=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ds.isDisabled&&Ds.supportsFiber)try{zi=Ds.inject(ty),at=Ds}catch{}}Fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ey;Fe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ya(t))throw Error(_(200));return Gg(e,t,null,n)};Fe.createRoot=function(e,t){if(!Ya(e))throw Error(_(299));var n=!1,r="",s=rh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Ha(e,1,!1,null,null,n,!1,r,s),e[kt]=t.current,Xr(e.nodeType===8?e.parentNode:e),new Qa(t)};Fe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=xf(t),e=e===null?null:e.stateNode,e};Fe.flushSync=function(e){return pn(e)};Fe.hydrate=function(e,t,n){if(!el(t))throw Error(_(200));return tl(null,e,t,!0,n)};Fe.hydrateRoot=function(e,t,n){if(!Ya(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",l=rh;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=nh(t,null,e,1,n??null,s,!1,i,l),e[kt]=t.current,Xr(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Zi(t)};Fe.render=function(e,t,n){if(!el(t))throw Error(_(200));return tl(null,e,t,!1,n)};Fe.unmountComponentAtNode=function(e){if(!el(e))throw Error(_(40));return e._reactRootContainer?(pn(function(){tl(null,null,e,!1,function(){e._reactRootContainer=null,e[kt]=null})}),!0):!1};Fe.unstable_batchedUpdates=Ua;Fe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!el(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return tl(e,t,n,!1,r)};Fe.version="18.2.0-next-9e3b772b8-20220608";function sh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(sh)}catch(e){console.error(e)}}sh(),tf.exports=Fe;var ny=tf.exports,ih,Ec=ny;ih=Ec.createRoot,Ec.hydrateRoot;const ry="modulepreload",sy=function(e){return"/"+e},Nc={},iy=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),o=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));s=Promise.allSettled(n.map(a=>{if(a=sy(a),a in Nc)return;Nc[a]=!0;const u=a.endsWith(".css"),m=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${m}`))return;const f=document.createElement("link");if(f.rel=u?"stylesheet":ry,u||(f.as="script"),f.crossOrigin="",f.href=a,o&&f.setAttribute("nonce",o),document.head.appendChild(f),u)return new Promise((p,g)=>{f.addEventListener("load",p),f.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${a}`)))})}))}function i(l){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=l,window.dispatchEvent(o),!o.defaultPrevented)throw l}return s.then(l=>{for(const o of l||[])o.status==="rejected"&&i(o.reason);return t().catch(i)})};function ly(e,t){const n=/(\x1b\[(\d+(;\d+)*)m)|([^\x1b]+)/g,r=[];let s,i={},l=!1,o=t==null?void 0:t.fg,a=t==null?void 0:t.bg;for(;(s=n.exec(e))!==null;){const[,,u,,m]=s;if(u){const f=+u;switch(f){case 0:i={};break;case 1:i["font-weight"]="bold";break;case 2:i.opacity="0.8";break;case 3:i["font-style"]="italic";break;case 4:i["text-decoration"]="underline";break;case 7:l=!0;break;case 8:i.display="none";break;case 9:i["text-decoration"]="line-through";break;case 22:delete i["font-weight"],delete i["font-style"],delete i.opacity,delete i["text-decoration"];break;case 23:delete i["font-weight"],delete i["font-style"],delete i.opacity;break;case 24:delete i["text-decoration"];break;case 27:l=!1;break;case 30:case 31:case 32:case 33:case 34:case 35:case 36:case 37:o=xc[f-30];break;case 39:o=t==null?void 0:t.fg;break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:a=xc[f-40];break;case 49:a=t==null?void 0:t.bg;break;case 53:i["text-decoration"]="overline";break;case 90:case 91:case 92:case 93:case 94:case 95:case 96:case 97:o=Tc[f-90];break;case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:a=Tc[f-100];break}}else if(m){const f={...i},p=l?a:o;p!==void 0&&(f.color=p);const g=l?o:a;g!==void 0&&(f["background-color"]=g),r.push(`<span style="${ay(f)}">${oy(m)}</span>`)}}return r.join("")}const xc={0:"var(--vscode-terminal-ansiBlack)",1:"var(--vscode-terminal-ansiRed)",2:"var(--vscode-terminal-ansiGreen)",3:"var(--vscode-terminal-ansiYellow)",4:"var(--vscode-terminal-ansiBlue)",5:"var(--vscode-terminal-ansiMagenta)",6:"var(--vscode-terminal-ansiCyan)",7:"var(--vscode-terminal-ansiWhite)"},Tc={0:"var(--vscode-terminal-ansiBrightBlack)",1:"var(--vscode-terminal-ansiBrightRed)",2:"var(--vscode-terminal-ansiBrightGreen)",3:"var(--vscode-terminal-ansiBrightYellow)",4:"var(--vscode-terminal-ansiBrightBlue)",5:"var(--vscode-terminal-ansiBrightMagenta)",6:"var(--vscode-terminal-ansiBrightCyan)",7:"var(--vscode-terminal-ansiBrightWhite)"};function oy(e){return e.replace(/[&"<>]/g,t=>({"&":"&amp;",'"':"&quot;","<":"&lt;",">":"&gt;"})[t])}function ay(e){return Object.entries(e).map(([t,n])=>`${t}: ${n}`).join("; ")}const Wl=({text:e,language:t,mimeType:n,linkify:r,readOnly:s,highlight:i,revealLine:l,lineNumbers:o,isFocused:a,focusOnChange:u,wrapLines:m,onChange:f,dataTestId:p,placeholder:g})=>{const[w,h]=ef(),[v]=B.useState(iy(()=>import("./codeMirrorModule-CNAqJrkA.js"),__vite__mapDeps([0,1])).then(S=>S.default)),d=B.useRef(null),[c,y]=B.useState();return B.useEffect(()=>{(async()=>{var N,E;const S=await v;cy(S);const x=h.current;if(!x)return;const k=dy(t)||fy(n)||(r?"text/linkified":"");if(d.current&&k===d.current.cm.getOption("mode")&&!!s===d.current.cm.getOption("readOnly")&&o===d.current.cm.getOption("lineNumbers")&&m===d.current.cm.getOption("lineWrapping")&&g===d.current.cm.getOption("placeholder"))return;(E=(N=d.current)==null?void 0:N.cm)==null||E.getWrapperElement().remove();const T=S(x,{value:"",mode:k,readOnly:!!s,lineNumbers:o,lineWrapping:m,placeholder:g});return d.current={cm:T},a&&T.focus(),y(T),T})()},[v,c,h,t,n,r,o,m,s,a,g]),B.useEffect(()=>{d.current&&d.current.cm.setSize(w.width,w.height)},[w]),B.useLayoutEffect(()=>{var k;if(!c)return;let S=!1;if(c.getValue()!==e&&(c.setValue(e),S=!0,u&&(c.execCommand("selectAll"),c.focus())),S||JSON.stringify(i)!==JSON.stringify(d.current.highlight)){for(const E of d.current.highlight||[])c.removeLineClass(E.line-1,"wrap");for(const E of i||[])c.addLineClass(E.line-1,"wrap",`source-line-${E.type}`);for(const E of d.current.widgets||[])c.removeLineWidget(E);for(const E of d.current.markers||[])E.clear();const T=[],N=[];for(const E of i||[]){if(E.type!=="subtle-error"&&E.type!=="error")continue;const O=(k=d.current)==null?void 0:k.cm.getLine(E.line-1);if(O){const M={};M.title=E.message||"",N.push(c.markText({line:E.line-1,ch:0},{line:E.line-1,ch:E.column||O.length},{className:"source-line-error-underline",attributes:M}))}if(E.type==="error"){const M=document.createElement("div");M.innerHTML=ly(E.message||""),M.className="source-line-error-widget",T.push(c.addLineWidget(E.line,M,{above:!0,coverGutter:!1}))}}d.current.highlight=i,d.current.widgets=T,d.current.markers=N}typeof l=="number"&&d.current.cm.lineCount()>=l&&c.scrollIntoView({line:Math.max(0,l-1),ch:0},50);let x;return f&&(x=()=>f(c.getValue()),c.on("change",x)),()=>{x&&c.off("change",x)}},[c,e,i,l,u,f]),L.jsx("div",{"data-testid":p,className:"cm-wrapper",ref:h,onClick:uy})};function uy(e){var n;if(!(e.target instanceof HTMLElement))return;let t;e.target.classList.contains("cm-linkified")?t=e.target.textContent:e.target.classList.contains("cm-link")&&((n=e.target.nextElementSibling)!=null&&n.classList.contains("cm-url"))&&(t=e.target.nextElementSibling.textContent.slice(1,-1)),t&&(e.preventDefault(),e.stopPropagation(),window.open(t,"_blank"))}let Cc=!1;function cy(e){Cc||(Cc=!0,e.defineSimpleMode("text/linkified",{start:[{regex:Qp,token:"linkified"}]}))}function fy(e){if(e){if(e.includes("javascript")||e.includes("json"))return"javascript";if(e.includes("python"))return"python";if(e.includes("csharp"))return"text/x-csharp";if(e.includes("java"))return"text/x-java";if(e.includes("markdown"))return"markdown";if(e.includes("html")||e.includes("svg"))return"htmlmixed";if(e.includes("css"))return"css"}}function dy(e){if(e)return{javascript:"javascript",jsonl:"javascript",python:"python",csharp:"text/x-csharp",java:"text/x-java",markdown:"markdown",html:"htmlmixed",css:"css",yaml:"yaml"}[e]}const hy=50,py=({sidebarSize:e,sidebarHidden:t=!1,sidebarIsFirst:n=!1,orientation:r="vertical",minSidebarSize:s=hy,settingName:i,sidebar:l,main:o})=>{const a=Math.max(s,e)*window.devicePixelRatio,[u,m]=eo(i?i+"."+r+":size":void 0,a),[f,p]=eo(i?i+"."+r+":size":void 0,a),[g,w]=B.useState(null),[h,v]=ef();let d;r==="vertical"?(d=f/window.devicePixelRatio,h&&h.height<d&&(d=h.height-10)):(d=u/window.devicePixelRatio,h&&h.width<d&&(d=h.width-10)),document.body.style.userSelect=g?"none":"inherit";let c={};return r==="vertical"?n?c={top:g?0:d-4,bottom:g?0:void 0,height:g?"initial":8}:c={bottom:g?0:d-4,top:g?0:void 0,height:g?"initial":8}:n?c={left:g?0:d-4,right:g?0:void 0,width:g?"initial":8}:c={right:g?0:d-4,left:g?0:void 0,width:g?"initial":8},L.jsxs("div",{className:on("split-view",r,n&&"sidebar-first"),ref:v,children:[L.jsx("div",{className:"split-view-main",children:o}),!t&&L.jsx("div",{style:{flexBasis:d},className:"split-view-sidebar",children:l}),!t&&L.jsx("div",{style:c,className:"split-view-resizer",onMouseDown:y=>w({offset:r==="vertical"?y.clientY:y.clientX,size:d}),onMouseUp:()=>w(null),onMouseMove:y=>{if(!y.buttons)w(null);else if(g){const x=(r==="vertical"?y.clientY:y.clientX)-g.offset,k=n?g.size+x:g.size-x,N=y.target.parentElement.getBoundingClientRect(),E=Math.min(Math.max(s,k),(r==="vertical"?N.height:N.width)-s);r==="vertical"?p(E*window.devicePixelRatio):m(E*window.devicePixelRatio)}}})]})},lh=({noShadow:e,children:t,noMinHeight:n,className:r,sidebarBackground:s,onClick:i})=>L.jsx("div",{className:on("toolbar",e&&"no-shadow",n&&"no-min-height",r,s&&"toolbar-sidebar-background"),onClick:i,children:t}),my=({tabs:e,selectedTab:t,setSelectedTab:n,leftToolbar:r,rightToolbar:s,dataTestId:i,mode:l})=>{const o=B.useId();return t||(t=e[0].id),l||(l="default"),L.jsx("div",{className:"tabbed-pane","data-testid":i,children:L.jsxs("div",{className:"vbox",children:[L.jsxs(lh,{children:[r&&L.jsxs("div",{style:{flex:"none",display:"flex",margin:"0 4px",alignItems:"center"},children:[...r]}),l==="default"&&L.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},role:"tablist",children:[...e.map(a=>L.jsx(gy,{id:a.id,ariaControls:`${o}-${a.id}`,title:a.title,count:a.count,errorCount:a.errorCount,selected:t===a.id,onSelect:n},a.id))]}),l==="select"&&L.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},role:"tablist",children:L.jsx("select",{style:{width:"100%",background:"none",cursor:"pointer"},onChange:a=>{n==null||n(e[a.currentTarget.selectedIndex].id)},children:e.map(a=>{let u="";return a.count&&(u=` (${a.count})`),a.errorCount&&(u=` (${a.errorCount})`),L.jsxs("option",{value:a.id,selected:a.id===t,role:"tab","aria-controls":`${o}-${a.id}`,children:[a.title,u]},a.id)})})}),s&&L.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center"},children:[...s]})]}),e.map(a=>{const u="tab-content tab-"+a.id;if(a.component)return L.jsx("div",{id:`${o}-${a.id}`,role:"tabpanel","aria-label":a.title,className:u,style:{display:t===a.id?"inherit":"none"},children:a.component},a.id);if(t===a.id)return L.jsx("div",{id:`${o}-${a.id}`,role:"tabpanel","aria-label":a.title,className:u,children:a.render()},a.id)})]})})},gy=({id:e,title:t,count:n,errorCount:r,selected:s,onSelect:i,ariaControls:l})=>L.jsxs("div",{className:on("tabbed-pane-tab",s&&"selected"),onClick:()=>i==null?void 0:i(e),role:"tab",title:t,"aria-controls":l,children:[L.jsx("div",{className:"tabbed-pane-tab-label",children:t}),!!n&&L.jsx("div",{className:"tabbed-pane-tab-counter",children:n}),!!r&&L.jsx("div",{className:"tabbed-pane-tab-counter error",children:r})]}),yy=({sources:e,fileId:t,setFileId:n})=>L.jsx("select",{className:"source-chooser",hidden:!e.length,title:"Source chooser",value:t,onChange:r=>{n(r.target.selectedOptions[0].value)},children:vy(e)});function vy(e){const t=s=>s.replace(/.*[/\\]([^/\\]+)/,"$1"),n=s=>L.jsx("option",{value:s.id,children:t(s.label)},s.id),r=new Map;for(const s of e){let i=r.get(s.group||"Debugger");i||(i=[],r.set(s.group||"Debugger",i)),i.push(s)}return[...r.entries()].map(([s,i])=>L.jsx("optgroup",{label:s,children:i.filter(l=>(l.group||"Debugger")===s).map(l=>n(l))},s))}function wy(){return{id:"default",isRecorded:!1,text:"",language:"javascript",label:"",highlight:[]}}const Me=B.forwardRef(function({children:t,title:n="",icon:r,disabled:s=!1,toggled:i=!1,onClick:l=()=>{},style:o,testId:a,className:u,ariaLabel:m},f){return L.jsxs("button",{ref:f,className:on(u,"toolbar-button",r,i&&"toggled"),onMouseDown:Lc,onClick:l,onDoubleClick:Lc,title:n,disabled:!!s,style:o,"data-testid":a,"aria-label":m||n,children:[r&&L.jsx("span",{className:`codicon codicon-${r}`,style:t?{marginRight:5}:{}}),t]})}),_c=({style:e})=>L.jsx("div",{className:"toolbar-separator",style:e}),Lc=e=>{e.stopPropagation(),e.preventDefault()};function nl(e,t="'"){const n=JSON.stringify(e),r=n.substring(1,n.length-1).replace(/\\"/g,'"');if(t==="'")return t+r.replace(/[']/g,"\\'")+t;if(t==='"')return t+r.replace(/["]/g,'\\"')+t;if(t==="`")return t+r.replace(/[`]/g,"`")+t;throw new Error("Invalid escape char")}function Mi(e){return e.charAt(0).toUpperCase()+e.substring(1)}function oh(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1_$2").replace(/([A-Z])([A-Z][a-z])/g,"$1_$2").toLowerCase()}function rl(e){return e.replace(/(^|[^\\])(\\\\)*\\(['"`])/g,"$1$2$3")}const ne=function(e,t,n){return e>=t&&e<=n};function Ce(e){return ne(e,48,57)}function Oc(e){return Ce(e)||ne(e,65,70)||ne(e,97,102)}function Sy(e){return ne(e,65,90)}function ky(e){return ne(e,97,122)}function Ey(e){return Sy(e)||ky(e)}function Ny(e){return e>=128}function ti(e){return Ey(e)||Ny(e)||e===95}function $c(e){return ti(e)||Ce(e)||e===45}function xy(e){return ne(e,0,8)||e===11||ne(e,14,31)||e===127}function ni(e){return e===10}function dt(e){return ni(e)||e===9||e===32}const Ty=1114111;class Ja extends Error{constructor(t){super(t),this.name="InvalidCharacterError"}}function Cy(e){const t=[];for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);if(r===13&&e.charCodeAt(n+1)===10&&(r=10,n++),(r===13||r===12)&&(r=10),r===0&&(r=65533),ne(r,55296,56319)&&ne(e.charCodeAt(n+1),56320,57343)){const s=r-55296,i=e.charCodeAt(n+1)-56320;r=Math.pow(2,16)+s*Math.pow(2,10)+i,n++}t.push(r)}return t}function se(e){if(e<=65535)return String.fromCharCode(e);e-=Math.pow(2,16);const t=Math.floor(e/Math.pow(2,10))+55296,n=e%Math.pow(2,10)+56320;return String.fromCharCode(t)+String.fromCharCode(n)}function _y(e){const t=Cy(e);let n=-1;const r=[];let s;const i=function(C){return C>=t.length?-1:t[C]},l=function(C){if(C===void 0&&(C=1),C>3)throw"Spec Error: no more than three codepoints of lookahead.";return i(n+C)},o=function(C){return C===void 0&&(C=1),n+=C,s=i(n),!0},a=function(){return n-=1,!0},u=function(C){return C===void 0&&(C=s),C===-1},m=function(){if(f(),o(),dt(s)){for(;dt(l());)o();return new Qo}else{if(s===34)return w();if(s===35)if($c(l())||d(l(1),l(2))){const C=new Eh("");return y(l(1),l(2),l(3))&&(C.type="id"),C.value=T(),C}else return new ye(s);else return s===36?l()===61?(o(),new Iy):new ye(s):s===39?w():s===40?new yh:s===41?new vh:s===42?l()===61?(o(),new Ay):new ye(s):s===43?k()?(a(),p()):new ye(s):s===44?new hh:s===45?k()?(a(),p()):l(1)===45&&l(2)===62?(o(2),new ch):S()?(a(),g()):new ye(s):s===46?k()?(a(),p()):new ye(s):s===58?new fh:s===59?new dh:s===60?l(1)===33&&l(2)===45&&l(3)===45?(o(3),new uh):new ye(s):s===64?y(l(1),l(2),l(3))?new kh(T()):new ye(s):s===91?new gh:s===92?c()?(a(),g()):new ye(s):s===93?new Yo:s===94?l()===61?(o(),new $y):new ye(s):s===123?new ph:s===124?l()===61?(o(),new Oy):l()===124?(o(),new wh):new ye(s):s===125?new mh:s===126?l()===61?(o(),new Ly):new ye(s):Ce(s)?(a(),p()):ti(s)?(a(),g()):u()?new si:new ye(s)}},f=function(){for(;l(1)===47&&l(2)===42;)for(o(2);;)if(o(),s===42&&l()===47){o();break}else if(u())return},p=function(){const C=N();if(y(l(1),l(2),l(3))){const A=new Py;return A.value=C.value,A.repr=C.repr,A.type=C.type,A.unit=T(),A}else if(l()===37){o();const A=new Ch;return A.value=C.value,A.repr=C.repr,A}else{const A=new Th;return A.value=C.value,A.repr=C.repr,A.type=C.type,A}},g=function(){const C=T();if(C.toLowerCase()==="url"&&l()===40){for(o();dt(l(1))&&dt(l(2));)o();return l()===34||l()===39?new ii(C):dt(l())&&(l(2)===34||l(2)===39)?new ii(C):h()}else return l()===40?(o(),new ii(C)):new Sh(C)},w=function(C){C===void 0&&(C=s);let A="";for(;o();){if(s===C||u())return new Nh(A);if(ni(s))return a(),new ah;s===92?u(l())||(ni(l())?o():A+=se(v())):A+=se(s)}throw new Error("Internal error")},h=function(){const C=new xh("");for(;dt(l());)o();if(u(l()))return C;for(;o();){if(s===41||u())return C;if(dt(s)){for(;dt(l());)o();return l()===41||u(l())?(o(),C):(O(),new ri)}else{if(s===34||s===39||s===40||xy(s))return O(),new ri;if(s===92)if(c())C.value+=se(v());else return O(),new ri;else C.value+=se(s)}}throw new Error("Internal error")},v=function(){if(o(),Oc(s)){const C=[s];for(let pe=0;pe<5&&Oc(l());pe++)o(),C.push(s);dt(l())&&o();let A=parseInt(C.map(function(pe){return String.fromCharCode(pe)}).join(""),16);return A>Ty&&(A=65533),A}else return u()?65533:s},d=function(C,A){return!(C!==92||ni(A))},c=function(){return d(s,l())},y=function(C,A,pe){return C===45?ti(A)||A===45||d(A,pe):ti(C)?!0:C===92?d(C,A):!1},S=function(){return y(s,l(1),l(2))},x=function(C,A,pe){return C===43||C===45?!!(Ce(A)||A===46&&Ce(pe)):C===46?!!Ce(A):!!Ce(C)},k=function(){return x(s,l(1),l(2))},T=function(){let C="";for(;o();)if($c(s))C+=se(s);else if(c())C+=se(v());else return a(),C;throw new Error("Internal parse error")},N=function(){let C="",A="integer";for((l()===43||l()===45)&&(o(),C+=se(s));Ce(l());)o(),C+=se(s);if(l(1)===46&&Ce(l(2)))for(o(),C+=se(s),o(),C+=se(s),A="number";Ce(l());)o(),C+=se(s);const pe=l(1),gr=l(2),yr=l(3);if((pe===69||pe===101)&&Ce(gr))for(o(),C+=se(s),o(),C+=se(s),A="number";Ce(l());)o(),C+=se(s);else if((pe===69||pe===101)&&(gr===43||gr===45)&&Ce(yr))for(o(),C+=se(s),o(),C+=se(s),o(),C+=se(s),A="number";Ce(l());)o(),C+=se(s);const vr=E(C);return{type:A,value:vr,repr:C}},E=function(C){return+C},O=function(){for(;o();){if(s===41||u())return;c()&&v()}};let M=0;for(;!u(l());)if(r.push(m()),M++,M>t.length*2)throw new Error("I'm infinite-looping!");return r}class te{constructor(){this.tokenType=""}toJSON(){return{token:this.tokenType}}toString(){return this.tokenType}toSource(){return""+this}}class ah extends te{constructor(){super(...arguments),this.tokenType="BADSTRING"}}class ri extends te{constructor(){super(...arguments),this.tokenType="BADURL"}}class Qo extends te{constructor(){super(...arguments),this.tokenType="WHITESPACE"}toString(){return"WS"}toSource(){return" "}}class uh extends te{constructor(){super(...arguments),this.tokenType="CDO"}toSource(){return"<!--"}}class ch extends te{constructor(){super(...arguments),this.tokenType="CDC"}toSource(){return"-->"}}class fh extends te{constructor(){super(...arguments),this.tokenType=":"}}class dh extends te{constructor(){super(...arguments),this.tokenType=";"}}class hh extends te{constructor(){super(...arguments),this.tokenType=","}}class ar extends te{constructor(){super(...arguments),this.value="",this.mirror=""}}class ph extends ar{constructor(){super(),this.tokenType="{",this.value="{",this.mirror="}"}}class mh extends ar{constructor(){super(),this.tokenType="}",this.value="}",this.mirror="{"}}class gh extends ar{constructor(){super(),this.tokenType="[",this.value="[",this.mirror="]"}}class Yo extends ar{constructor(){super(),this.tokenType="]",this.value="]",this.mirror="["}}class yh extends ar{constructor(){super(),this.tokenType="(",this.value="(",this.mirror=")"}}class vh extends ar{constructor(){super(),this.tokenType=")",this.value=")",this.mirror="("}}class Ly extends te{constructor(){super(...arguments),this.tokenType="~="}}class Oy extends te{constructor(){super(...arguments),this.tokenType="|="}}class $y extends te{constructor(){super(...arguments),this.tokenType="^="}}class Iy extends te{constructor(){super(...arguments),this.tokenType="$="}}class Ay extends te{constructor(){super(...arguments),this.tokenType="*="}}class wh extends te{constructor(){super(...arguments),this.tokenType="||"}}class si extends te{constructor(){super(...arguments),this.tokenType="EOF"}toSource(){return""}}class ye extends te{constructor(t){super(),this.tokenType="DELIM",this.value="",this.value=se(t)}toString(){return"DELIM("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t}toSource(){return this.value==="\\"?`\\
`:this.value}}class ur extends te{constructor(){super(...arguments),this.value=""}ASCIIMatch(t){return this.value.toLowerCase()===t.toLowerCase()}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t}}class Sh extends ur{constructor(t){super(),this.tokenType="IDENT",this.value=t}toString(){return"IDENT("+this.value+")"}toSource(){return ms(this.value)}}class ii extends ur{constructor(t){super(),this.tokenType="FUNCTION",this.value=t,this.mirror=")"}toString(){return"FUNCTION("+this.value+")"}toSource(){return ms(this.value)+"("}}class kh extends ur{constructor(t){super(),this.tokenType="AT-KEYWORD",this.value=t}toString(){return"AT("+this.value+")"}toSource(){return"@"+ms(this.value)}}class Eh extends ur{constructor(t){super(),this.tokenType="HASH",this.value=t,this.type="unrestricted"}toString(){return"HASH("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.type=this.type,t}toSource(){return this.type==="id"?"#"+ms(this.value):"#"+My(this.value)}}class Nh extends ur{constructor(t){super(),this.tokenType="STRING",this.value=t}toString(){return'"'+_h(this.value)+'"'}}class xh extends ur{constructor(t){super(),this.tokenType="URL",this.value=t}toString(){return"URL("+this.value+")"}toSource(){return'url("'+_h(this.value)+'")'}}class Th extends te{constructor(){super(),this.tokenType="NUMBER",this.type="integer",this.repr=""}toString(){return this.type==="integer"?"INT("+this.value+")":"NUMBER("+this.value+")"}toJSON(){const t=super.toJSON();return t.value=this.value,t.type=this.type,t.repr=this.repr,t}toSource(){return this.repr}}class Ch extends te{constructor(){super(),this.tokenType="PERCENTAGE",this.repr=""}toString(){return"PERCENTAGE("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.repr=this.repr,t}toSource(){return this.repr+"%"}}class Py extends te{constructor(){super(),this.tokenType="DIMENSION",this.type="integer",this.repr="",this.unit=""}toString(){return"DIM("+this.value+","+this.unit+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.type=this.type,t.repr=this.repr,t.unit=this.unit,t}toSource(){const t=this.repr;let n=ms(this.unit);return n[0].toLowerCase()==="e"&&(n[1]==="-"||ne(n.charCodeAt(1),48,57))&&(n="\\65 "+n.slice(1,n.length)),t+n}}function ms(e){e=""+e;let t="";const n=e.charCodeAt(0);for(let r=0;r<e.length;r++){const s=e.charCodeAt(r);if(s===0)throw new Ja("Invalid character: the input contains U+0000.");ne(s,1,31)||s===127||r===0&&ne(s,48,57)||r===1&&ne(s,48,57)&&n===45?t+="\\"+s.toString(16)+" ":s>=128||s===45||s===95||ne(s,48,57)||ne(s,65,90)||ne(s,97,122)?t+=e[r]:t+="\\"+e[r]}return t}function My(e){e=""+e;let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(r===0)throw new Ja("Invalid character: the input contains U+0000.");r>=128||r===45||r===95||ne(r,48,57)||ne(r,65,90)||ne(r,97,122)?t+=e[n]:t+="\\"+r.toString(16)+" "}return t}function _h(e){e=""+e;let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(r===0)throw new Ja("Invalid character: the input contains U+0000.");ne(r,1,31)||r===127?t+="\\"+r.toString(16)+" ":r===34||r===92?t+="\\"+e[n]:t+=e[n]}return t}class Le extends Error{}function jy(e,t){let n;try{n=_y(e),n[n.length-1]instanceof si||n.push(new si)}catch(E){const O=E.message+` while parsing css selector "${e}". Did you mean to CSS.escape it?`,M=(E.stack||"").indexOf(E.message);throw M!==-1&&(E.stack=E.stack.substring(0,M)+O+E.stack.substring(M+E.message.length)),E.message=O,E}const r=n.find(E=>E instanceof kh||E instanceof ah||E instanceof ri||E instanceof wh||E instanceof uh||E instanceof ch||E instanceof dh||E instanceof ph||E instanceof mh||E instanceof xh||E instanceof Ch);if(r)throw new Le(`Unsupported token "${r.toSource()}" while parsing css selector "${e}". Did you mean to CSS.escape it?`);let s=0;const i=new Set;function l(){return new Le(`Unexpected token "${n[s].toSource()}" while parsing css selector "${e}". Did you mean to CSS.escape it?`)}function o(){for(;n[s]instanceof Qo;)s++}function a(E=s){return n[E]instanceof Sh}function u(E=s){return n[E]instanceof Nh}function m(E=s){return n[E]instanceof Th}function f(E=s){return n[E]instanceof hh}function p(E=s){return n[E]instanceof yh}function g(E=s){return n[E]instanceof vh}function w(E=s){return n[E]instanceof ii}function h(E=s){return n[E]instanceof ye&&n[E].value==="*"}function v(E=s){return n[E]instanceof si}function d(E=s){return n[E]instanceof ye&&[">","+","~"].includes(n[E].value)}function c(E=s){return f(E)||g(E)||v(E)||d(E)||n[E]instanceof Qo}function y(){const E=[S()];for(;o(),!!f();)s++,E.push(S());return E}function S(){return o(),m()||u()?n[s++].value:x()}function x(){const E={simples:[]};for(o(),d()?E.simples.push({selector:{functions:[{name:"scope",args:[]}]},combinator:""}):E.simples.push({selector:k(),combinator:""});;){if(o(),d())E.simples[E.simples.length-1].combinator=n[s++].value,o();else if(c())break;E.simples.push({combinator:"",selector:k()})}return E}function k(){let E="";const O=[];for(;!c();)if(a()||h())E+=n[s++].toSource();else if(n[s]instanceof Eh)E+=n[s++].toSource();else if(n[s]instanceof ye&&n[s].value===".")if(s++,a())E+="."+n[s++].toSource();else throw l();else if(n[s]instanceof fh)if(s++,a())if(!t.has(n[s].value.toLowerCase()))E+=":"+n[s++].toSource();else{const M=n[s++].value.toLowerCase();O.push({name:M,args:[]}),i.add(M)}else if(w()){const M=n[s++].value.toLowerCase();if(t.has(M)?(O.push({name:M,args:y()}),i.add(M)):E+=`:${M}(${T()})`,o(),!g())throw l();s++}else throw l();else if(n[s]instanceof gh){for(E+="[",s++;!(n[s]instanceof Yo)&&!v();)E+=n[s++].toSource();if(!(n[s]instanceof Yo))throw l();E+="]",s++}else throw l();if(!E&&!O.length)throw l();return{css:E||void 0,functions:O}}function T(){let E="",O=1;for(;!v()&&((p()||w())&&O++,g()&&O--,!!O);)E+=n[s++].toSource();return E}const N=y();if(!v())throw l();if(N.some(E=>typeof E!="object"||!("simples"in E)))throw new Le(`Error while parsing css selector "${e}". Did you mean to CSS.escape it?`);return{selector:N,names:Array.from(i)}}const Ic=new Set(["internal:has","internal:has-not","internal:and","internal:or","internal:chain","left-of","right-of","above","below","near"]),Ry=new Set(["left-of","right-of","above","below","near"]),Dy=new Set(["not","is","where","has","scope","light","visible","text","text-matches","text-is","has-text","above","below","right-of","left-of","near","nth-match"]);function Lh(e){const t=By(e),n=[];for(const r of t.parts){if(r.name==="css"||r.name==="css:light"){r.name==="css:light"&&(r.body=":light("+r.body+")");const s=jy(r.body,Dy);n.push({name:"css",body:s.selector,source:r.body});continue}if(Ic.has(r.name)){let s,i;try{const u=JSON.parse("["+r.body+"]");if(!Array.isArray(u)||u.length<1||u.length>2||typeof u[0]!="string")throw new Le(`Malformed selector: ${r.name}=`+r.body);if(s=u[0],u.length===2){if(typeof u[1]!="number"||!Ry.has(r.name))throw new Le(`Malformed selector: ${r.name}=`+r.body);i=u[1]}}catch{throw new Le(`Malformed selector: ${r.name}=`+r.body)}const l={name:r.name,source:r.body,body:{parsed:Lh(s),distance:i}},o=[...l.body.parsed.parts].reverse().find(u=>u.name==="internal:control"&&u.body==="enter-frame"),a=o?l.body.parsed.parts.indexOf(o):-1;a!==-1&&by(l.body.parsed.parts.slice(0,a+1),n.slice(0,a+1))&&l.body.parsed.parts.splice(0,a+1),n.push(l);continue}n.push({...r,source:r.body})}if(Ic.has(n[0].name))throw new Le(`"${n[0].name}" selector cannot be first`);return{capture:t.capture,parts:n}}function by(e,t){return Rn({parts:e})===Rn({parts:t})}function Rn(e,t){return typeof e=="string"?e:e.parts.map((n,r)=>{let s=!0;!t&&r!==e.capture&&(n.name==="css"||n.name==="xpath"&&n.source.startsWith("//")||n.source.startsWith(".."))&&(s=!1);const i=s?n.name+"=":"";return`${r===e.capture?"*":""}${i}${n.source}`}).join(" >> ")}function By(e){let t=0,n,r=0;const s={parts:[]},i=()=>{const o=e.substring(r,t).trim(),a=o.indexOf("=");let u,m;a!==-1&&o.substring(0,a).trim().match(/^[a-zA-Z_0-9-+:*]+$/)?(u=o.substring(0,a).trim(),m=o.substring(a+1)):o.length>1&&o[0]==='"'&&o[o.length-1]==='"'||o.length>1&&o[0]==="'"&&o[o.length-1]==="'"?(u="text",m=o):/^\(*\/\//.test(o)||o.startsWith("..")?(u="xpath",m=o):(u="css",m=o);let f=!1;if(u[0]==="*"&&(f=!0,u=u.substring(1)),s.parts.push({name:u,body:m}),f){if(s.capture!==void 0)throw new Le("Only one of the selectors can capture using * modifier");s.capture=s.parts.length-1}};if(!e.includes(">>"))return t=e.length,i(),s;const l=()=>{const a=e.substring(r,t).match(/^\s*text\s*=(.*)$/);return!!a&&!!a[1]};for(;t<e.length;){const o=e[t];o==="\\"&&t+1<e.length?t+=2:o===n?(n=void 0,t++):!n&&(o==='"'||o==="'"||o==="`")&&!l()?(n=o,t++):!n&&o===">"&&e[t+1]===">"?(i(),t+=2,r=t):t++}return i(),s}function Hl(e,t){let n=0,r=e.length===0;const s=()=>e[n]||"",i=()=>{const v=s();return++n,r=n>=e.length,v},l=v=>{throw r?new Le(`Unexpected end of selector while parsing selector \`${e}\``):new Le(`Error while parsing selector \`${e}\` - unexpected symbol "${s()}" at position ${n}`+(v?" during "+v:""))};function o(){for(;!r&&/\s/.test(s());)i()}function a(v){return v>=""||v>="0"&&v<="9"||v>="A"&&v<="Z"||v>="a"&&v<="z"||v>="0"&&v<="9"||v==="_"||v==="-"}function u(){let v="";for(o();!r&&a(s());)v+=i();return v}function m(v){let d=i();for(d!==v&&l("parsing quoted string");!r&&s()!==v;)s()==="\\"&&i(),d+=i();return s()!==v&&l("parsing quoted string"),d+=i(),d}function f(){i()!=="/"&&l("parsing regular expression");let v="",d=!1;for(;!r;){if(s()==="\\")v+=i(),r&&l("parsing regular expression");else if(d&&s()==="]")d=!1;else if(!d&&s()==="[")d=!0;else if(!d&&s()==="/")break;v+=i()}i()!=="/"&&l("parsing regular expression");let c="";for(;!r&&s().match(/[dgimsuy]/);)c+=i();try{return new RegExp(v,c)}catch(y){throw new Le(`Error while parsing selector \`${e}\`: ${y.message}`)}}function p(){let v="";return o(),s()==="'"||s()==='"'?v=m(s()).slice(1,-1):v=u(),v||l("parsing property path"),v}function g(){o();let v="";return r||(v+=i()),!r&&v!=="="&&(v+=i()),["=","*=","^=","$=","|=","~="].includes(v)||l("parsing operator"),v}function w(){i();const v=[];for(v.push(p()),o();s()===".";)i(),v.push(p()),o();if(s()==="]")return i(),{name:v.join("."),jsonPath:v,op:"<truthy>",value:null,caseSensitive:!1};const d=g();let c,y=!0;if(o(),s()==="/"){if(d!=="=")throw new Le(`Error while parsing selector \`${e}\` - cannot use ${d} in attribute with regular expression`);c=f()}else if(s()==="'"||s()==='"')c=m(s()).slice(1,-1),o(),s()==="i"||s()==="I"?(y=!1,i()):(s()==="s"||s()==="S")&&(y=!0,i());else{for(c="";!r&&(a(s())||s()==="+"||s()===".");)c+=i();c==="true"?c=!0:c==="false"&&(c=!1)}if(o(),s()!=="]"&&l("parsing attribute value"),i(),d!=="="&&typeof c!="string")throw new Le(`Error while parsing selector \`${e}\` - cannot use ${d} in attribute with non-string matching value - ${c}`);return{name:v.join("."),jsonPath:v,op:d,value:c,caseSensitive:y}}const h={name:"",attributes:[]};for(h.name=u(),o();s()==="[";)h.attributes.push(w()),o();if(r||l(void 0),!h.name&&!h.attributes.length)throw new Le(`Error while parsing selector \`${e}\` - selector cannot be empty`);return h}function Oh(e,t,n=!1){return Fy(e,t,n,1)[0]}function Fy(e,t,n=!1,r=20,s){try{return Nn(new qy[e](s),Lh(t),n,r)}catch{return[t]}}function Nn(e,t,n=!1,r=20){const s=[...t.parts],i=[];let l=n?"frame-locator":"page";for(let o=0;o<s.length;o++){const a=s[o],u=l;if(l="locator",a.name==="nth"){a.body==="0"?i.push([e.generateLocator(u,"first",""),e.generateLocator(u,"nth","0")]):a.body==="-1"?i.push([e.generateLocator(u,"last",""),e.generateLocator(u,"nth","-1")]):i.push([e.generateLocator(u,"nth",a.body)]);continue}if(a.name==="internal:text"){const{exact:w,text:h}=_r(a.body);i.push([e.generateLocator(u,"text",h,{exact:w})]);continue}if(a.name==="internal:has-text"){const{exact:w,text:h}=_r(a.body);if(!w){i.push([e.generateLocator(u,"has-text",h,{exact:w})]);continue}}if(a.name==="internal:has-not-text"){const{exact:w,text:h}=_r(a.body);if(!w){i.push([e.generateLocator(u,"has-not-text",h,{exact:w})]);continue}}if(a.name==="internal:has"){const w=Nn(e,a.body.parsed,!1,r);i.push(w.map(h=>e.generateLocator(u,"has",h)));continue}if(a.name==="internal:has-not"){const w=Nn(e,a.body.parsed,!1,r);i.push(w.map(h=>e.generateLocator(u,"hasNot",h)));continue}if(a.name==="internal:and"){const w=Nn(e,a.body.parsed,!1,r);i.push(w.map(h=>e.generateLocator(u,"and",h)));continue}if(a.name==="internal:or"){const w=Nn(e,a.body.parsed,!1,r);i.push(w.map(h=>e.generateLocator(u,"or",h)));continue}if(a.name==="internal:chain"){const w=Nn(e,a.body.parsed,!1,r);i.push(w.map(h=>e.generateLocator(u,"chain",h)));continue}if(a.name==="internal:label"){const{exact:w,text:h}=_r(a.body);i.push([e.generateLocator(u,"label",h,{exact:w})]);continue}if(a.name==="internal:role"){const w=Hl(a.body),h={attrs:[]};for(const v of w.attributes)v.name==="name"?(h.exact=v.caseSensitive,h.name=v.value):(v.name==="level"&&typeof v.value=="string"&&(v.value=+v.value),h.attrs.push({name:v.name==="include-hidden"?"includeHidden":v.name,value:v.value}));i.push([e.generateLocator(u,"role",w.name,h)]);continue}if(a.name==="internal:testid"){const w=Hl(a.body),{value:h}=w.attributes[0];i.push([e.generateLocator(u,"test-id",h)]);continue}if(a.name==="internal:attr"){const w=Hl(a.body),{name:h,value:v,caseSensitive:d}=w.attributes[0],c=v,y=!!d;if(h==="placeholder"){i.push([e.generateLocator(u,"placeholder",c,{exact:y})]);continue}if(h==="alt"){i.push([e.generateLocator(u,"alt",c,{exact:y})]);continue}if(h==="title"){i.push([e.generateLocator(u,"title",c,{exact:y})]);continue}}if(a.name==="internal:control"&&a.body==="enter-frame"){const w=i[i.length-1],h=s[o-1],v=w.map(d=>e.chainLocators([d,e.generateLocator(u,"frame","")]));["xpath","css"].includes(h.name)&&v.push(e.generateLocator(u,"frame-locator",Rn({parts:[h]})),e.generateLocator(u,"frame-locator",Rn({parts:[h]},!0))),w.splice(0,w.length,...v),l="frame-locator";continue}const m=s[o+1],f=Rn({parts:[a]}),p=e.generateLocator(u,"default",f);if(m&&["internal:has-text","internal:has-not-text"].includes(m.name)){const{exact:w,text:h}=_r(m.body);if(!w){const v=e.generateLocator("locator",m.name==="internal:has-text"?"has-text":"has-not-text",h,{exact:w}),d={};m.name==="internal:has-text"?d.hasText=h:d.hasNotText=h;const c=e.generateLocator(u,"default",f,d);i.push([e.chainLocators([p,v]),c]),o++;continue}}let g;if(["xpath","css"].includes(a.name)){const w=Rn({parts:[a]},!0);g=e.generateLocator(u,"default",w)}i.push([p,g].filter(Boolean))}return zy(e,i,r)}function zy(e,t,n){const r=t.map(()=>""),s=[],i=l=>{if(l===t.length)return s.push(e.chainLocators(r)),s.length<n;for(const o of t[l])if(r[l]=o,!i(l+1))return!1;return!0};return i(0),s}function _r(e){let t=!1;const n=e.match(/^\/(.*)\/([igm]*)$/);return n?{text:new RegExp(n[1],n[2])}:(e.endsWith('"')?(e=JSON.parse(e),t=!0):e.endsWith('"s')?(e=JSON.parse(e.substring(0,e.length-1)),t=!0):e.endsWith('"i')&&(e=JSON.parse(e.substring(0,e.length-1)),t=!1),{exact:t,text:e})}class Uy{constructor(t){this.preferredQuote=t}generateLocator(t,n,r,s={}){switch(n){case"default":return s.hasText!==void 0?`locator(${this.quote(r)}, { hasText: ${this.toHasText(s.hasText)} })`:s.hasNotText!==void 0?`locator(${this.quote(r)}, { hasNotText: ${this.toHasText(s.hasNotText)} })`:`locator(${this.quote(r)})`;case"frame-locator":return`frameLocator(${this.quote(r)})`;case"frame":return"contentFrame()";case"nth":return`nth(${r})`;case"first":return"first()";case"last":return"last()";case"role":const i=[];fe(s.name)?i.push(`name: ${this.regexToSourceString(s.name)}`):typeof s.name=="string"&&(i.push(`name: ${this.quote(s.name)}`),s.exact&&i.push("exact: true"));for(const{name:o,value:a}of s.attrs)i.push(`${o}: ${typeof a=="string"?this.quote(a):a}`);const l=i.length?`, { ${i.join(", ")} }`:"";return`getByRole(${this.quote(r)}${l})`;case"has-text":return`filter({ hasText: ${this.toHasText(r)} })`;case"has-not-text":return`filter({ hasNotText: ${this.toHasText(r)} })`;case"has":return`filter({ has: ${r} })`;case"hasNot":return`filter({ hasNot: ${r} })`;case"and":return`and(${r})`;case"or":return`or(${r})`;case"chain":return`locator(${r})`;case"test-id":return`getByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("getByText",r,!!s.exact);case"alt":return this.toCallWithExact("getByAltText",r,!!s.exact);case"placeholder":return this.toCallWithExact("getByPlaceholder",r,!!s.exact);case"label":return this.toCallWithExact("getByLabel",r,!!s.exact);case"title":return this.toCallWithExact("getByTitle",r,!!s.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToSourceString(t){return rl(String(t))}toCallWithExact(t,n,r){return fe(n)?`${t}(${this.regexToSourceString(n)})`:r?`${t}(${this.quote(n)}, { exact: true })`:`${t}(${this.quote(n)})`}toHasText(t){return fe(t)?this.regexToSourceString(t):this.quote(t)}toTestIdValue(t){return fe(t)?this.regexToSourceString(t):this.quote(t)}quote(t){return nl(t,this.preferredQuote??"'")}}class Vy{generateLocator(t,n,r,s={}){switch(n){case"default":return s.hasText!==void 0?`locator(${this.quote(r)}, has_text=${this.toHasText(s.hasText)})`:s.hasNotText!==void 0?`locator(${this.quote(r)}, has_not_text=${this.toHasText(s.hasNotText)})`:`locator(${this.quote(r)})`;case"frame-locator":return`frame_locator(${this.quote(r)})`;case"frame":return"content_frame";case"nth":return`nth(${r})`;case"first":return"first";case"last":return"last";case"role":const i=[];fe(s.name)?i.push(`name=${this.regexToString(s.name)}`):typeof s.name=="string"&&(i.push(`name=${this.quote(s.name)}`),s.exact&&i.push("exact=True"));for(const{name:o,value:a}of s.attrs){let u=typeof a=="string"?this.quote(a):a;typeof a=="boolean"&&(u=a?"True":"False"),i.push(`${oh(o)}=${u}`)}const l=i.length?`, ${i.join(", ")}`:"";return`get_by_role(${this.quote(r)}${l})`;case"has-text":return`filter(has_text=${this.toHasText(r)})`;case"has-not-text":return`filter(has_not_text=${this.toHasText(r)})`;case"has":return`filter(has=${r})`;case"hasNot":return`filter(has_not=${r})`;case"and":return`and_(${r})`;case"or":return`or_(${r})`;case"chain":return`locator(${r})`;case"test-id":return`get_by_test_id(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("get_by_text",r,!!s.exact);case"alt":return this.toCallWithExact("get_by_alt_text",r,!!s.exact);case"placeholder":return this.toCallWithExact("get_by_placeholder",r,!!s.exact);case"label":return this.toCallWithExact("get_by_label",r,!!s.exact);case"title":return this.toCallWithExact("get_by_title",r,!!s.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", re.IGNORECASE":"";return`re.compile(r"${rl(t.source).replace(/\\\//,"/").replace(/"/g,'\\"')}"${n})`}toCallWithExact(t,n,r){return fe(n)?`${t}(${this.regexToString(n)})`:r?`${t}(${this.quote(n)}, exact=True)`:`${t}(${this.quote(n)})`}toHasText(t){return fe(t)?this.regexToString(t):`${this.quote(t)}`}toTestIdValue(t){return fe(t)?this.regexToString(t):this.quote(t)}quote(t){return nl(t,'"')}}class Ky{generateLocator(t,n,r,s={}){let i;switch(t){case"page":i="Page";break;case"frame-locator":i="FrameLocator";break;case"locator":i="Locator";break}switch(n){case"default":return s.hasText!==void 0?`locator(${this.quote(r)}, new ${i}.LocatorOptions().setHasText(${this.toHasText(s.hasText)}))`:s.hasNotText!==void 0?`locator(${this.quote(r)}, new ${i}.LocatorOptions().setHasNotText(${this.toHasText(s.hasNotText)}))`:`locator(${this.quote(r)})`;case"frame-locator":return`frameLocator(${this.quote(r)})`;case"frame":return"contentFrame()";case"nth":return`nth(${r})`;case"first":return"first()";case"last":return"last()";case"role":const l=[];fe(s.name)?l.push(`.setName(${this.regexToString(s.name)})`):typeof s.name=="string"&&(l.push(`.setName(${this.quote(s.name)})`),s.exact&&l.push(".setExact(true)"));for(const{name:a,value:u}of s.attrs)l.push(`.set${Mi(a)}(${typeof u=="string"?this.quote(u):u})`);const o=l.length?`, new ${i}.GetByRoleOptions()${l.join("")}`:"";return`getByRole(AriaRole.${oh(r).toUpperCase()}${o})`;case"has-text":return`filter(new ${i}.FilterOptions().setHasText(${this.toHasText(r)}))`;case"has-not-text":return`filter(new ${i}.FilterOptions().setHasNotText(${this.toHasText(r)}))`;case"has":return`filter(new ${i}.FilterOptions().setHas(${r}))`;case"hasNot":return`filter(new ${i}.FilterOptions().setHasNot(${r}))`;case"and":return`and(${r})`;case"or":return`or(${r})`;case"chain":return`locator(${r})`;case"test-id":return`getByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact(i,"getByText",r,!!s.exact);case"alt":return this.toCallWithExact(i,"getByAltText",r,!!s.exact);case"placeholder":return this.toCallWithExact(i,"getByPlaceholder",r,!!s.exact);case"label":return this.toCallWithExact(i,"getByLabel",r,!!s.exact);case"title":return this.toCallWithExact(i,"getByTitle",r,!!s.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", Pattern.CASE_INSENSITIVE":"";return`Pattern.compile(${this.quote(rl(t.source))}${n})`}toCallWithExact(t,n,r,s){return fe(r)?`${n}(${this.regexToString(r)})`:s?`${n}(${this.quote(r)}, new ${t}.${Mi(n)}Options().setExact(true))`:`${n}(${this.quote(r)})`}toHasText(t){return fe(t)?this.regexToString(t):this.quote(t)}toTestIdValue(t){return fe(t)?this.regexToString(t):this.quote(t)}quote(t){return nl(t,'"')}}class Wy{generateLocator(t,n,r,s={}){switch(n){case"default":return s.hasText!==void 0?`Locator(${this.quote(r)}, new() { ${this.toHasText(s.hasText)} })`:s.hasNotText!==void 0?`Locator(${this.quote(r)}, new() { ${this.toHasNotText(s.hasNotText)} })`:`Locator(${this.quote(r)})`;case"frame-locator":return`FrameLocator(${this.quote(r)})`;case"frame":return"ContentFrame";case"nth":return`Nth(${r})`;case"first":return"First";case"last":return"Last";case"role":const i=[];fe(s.name)?i.push(`NameRegex = ${this.regexToString(s.name)}`):typeof s.name=="string"&&(i.push(`Name = ${this.quote(s.name)}`),s.exact&&i.push("Exact = true"));for(const{name:o,value:a}of s.attrs)i.push(`${Mi(o)} = ${typeof a=="string"?this.quote(a):a}`);const l=i.length?`, new() { ${i.join(", ")} }`:"";return`GetByRole(AriaRole.${Mi(r)}${l})`;case"has-text":return`Filter(new() { ${this.toHasText(r)} })`;case"has-not-text":return`Filter(new() { ${this.toHasNotText(r)} })`;case"has":return`Filter(new() { Has = ${r} })`;case"hasNot":return`Filter(new() { HasNot = ${r} })`;case"and":return`And(${r})`;case"or":return`Or(${r})`;case"chain":return`Locator(${r})`;case"test-id":return`GetByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("GetByText",r,!!s.exact);case"alt":return this.toCallWithExact("GetByAltText",r,!!s.exact);case"placeholder":return this.toCallWithExact("GetByPlaceholder",r,!!s.exact);case"label":return this.toCallWithExact("GetByLabel",r,!!s.exact);case"title":return this.toCallWithExact("GetByTitle",r,!!s.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", RegexOptions.IgnoreCase":"";return`new Regex(${this.quote(rl(t.source))}${n})`}toCallWithExact(t,n,r){return fe(n)?`${t}(${this.regexToString(n)})`:r?`${t}(${this.quote(n)}, new() { Exact = true })`:`${t}(${this.quote(n)})`}toHasText(t){return fe(t)?`HasTextRegex = ${this.regexToString(t)}`:`HasText = ${this.quote(t)}`}toTestIdValue(t){return fe(t)?this.regexToString(t):this.quote(t)}toHasNotText(t){return fe(t)?`HasNotTextRegex = ${this.regexToString(t)}`:`HasNotText = ${this.quote(t)}`}quote(t){return nl(t,'"')}}class Hy{generateLocator(t,n,r,s={}){return JSON.stringify({kind:n,body:r,options:s})}chainLocators(t){const n=t.map(r=>JSON.parse(r));for(let r=0;r<n.length-1;++r)n[r].next=n[r+1];return JSON.stringify(n[0])}}const qy={javascript:Uy,python:Vy,java:Ky,csharp:Wy,jsonl:Hy};function fe(e){return e instanceof RegExp}const Qy=({language:e,log:t})=>{const n=B.useRef(null),[r,s]=B.useState(new Map);return B.useLayoutEffect(()=>{var i;t.find(l=>l.reveal)&&((i=n.current)==null||i.scrollIntoView({block:"center",inline:"nearest"}))},[n,t]),L.jsxs("div",{className:"call-log",style:{flex:"auto"},children:[t.map(i=>{const l=r.get(i.id),o=typeof l=="boolean"?l:i.status!=="done",a=i.params.selector?Oh(e,i.params.selector):null;let u=i.title,m="";return i.title.startsWith("expect.to")||i.title.startsWith("expect.not.to")?(u="expect(",m=`).${i.title.substring(7)}()`):i.title.startsWith("locator.")?(u="",m=`.${i.title.substring(8)}()`):(a||i.params.url)&&(u=i.title+"(",m=")"),L.jsxs("div",{className:on("call-log-call",i.status),children:[L.jsxs("div",{className:"call-log-call-header",children:[L.jsx("span",{className:on("codicon",`codicon-chevron-${o?"down":"right"}`),style:{cursor:"pointer"},onClick:()=>{const f=new Map(r);f.set(i.id,!o),s(f)}}),u,i.params.url?L.jsx("span",{className:"call-log-details",children:L.jsx("span",{className:"call-log-url",title:i.params.url,children:i.params.url})}):void 0,a?L.jsx("span",{className:"call-log-details",children:L.jsx("span",{className:"call-log-selector",title:`page.${a}`,children:`page.${a}`})}):void 0,m,L.jsx("span",{className:on("codicon",Yy(i))}),typeof i.duration=="number"?L.jsxs("span",{className:"call-log-time",children:["— ",Hp(i.duration)]}):void 0]}),(o?i.messages:[]).map((f,p)=>L.jsx("div",{className:"call-log-message",children:f.trim()},p)),!!i.error&&L.jsx("div",{className:"call-log-message error",hidden:!o,children:i.error})]},i.id)}),L.jsx("div",{ref:n})]})};function Yy(e){switch(e.status){case"done":return"codicon-check";case"in-progress":return"codicon-clock";case"paused":return"codicon-debug-pause";case"error":return"codicon-error"}}const Ga=Symbol.for("yaml.alias"),Jo=Symbol.for("yaml.document"),Ft=Symbol.for("yaml.map"),$h=Symbol.for("yaml.pair"),ft=Symbol.for("yaml.scalar"),cr=Symbol.for("yaml.seq"),Ye=Symbol.for("yaml.node.type"),vn=e=>!!e&&typeof e=="object"&&e[Ye]===Ga,wn=e=>!!e&&typeof e=="object"&&e[Ye]===Jo,fr=e=>!!e&&typeof e=="object"&&e[Ye]===Ft,H=e=>!!e&&typeof e=="object"&&e[Ye]===$h,z=e=>!!e&&typeof e=="object"&&e[Ye]===ft,dr=e=>!!e&&typeof e=="object"&&e[Ye]===cr;function Q(e){if(e&&typeof e=="object")switch(e[Ye]){case Ft:case cr:return!0}return!1}function J(e){if(e&&typeof e=="object")switch(e[Ye]){case Ga:case Ft:case ft:case cr:return!0}return!1}const Jy=e=>(z(e)||Q(e))&&!!e.anchor,Oe=Symbol("break visit"),Ih=Symbol("skip children"),ct=Symbol("remove node");function Vt(e,t){const n=Ah(t);wn(e)?Dn(null,e.contents,n,Object.freeze([e]))===ct&&(e.contents=null):Dn(null,e,n,Object.freeze([]))}Vt.BREAK=Oe;Vt.SKIP=Ih;Vt.REMOVE=ct;function Dn(e,t,n,r){const s=Ph(e,t,n,r);if(J(s)||H(s))return Mh(e,r,s),Dn(e,s,n,r);if(typeof s!="symbol"){if(Q(t)){r=Object.freeze(r.concat(t));for(let i=0;i<t.items.length;++i){const l=Dn(i,t.items[i],n,r);if(typeof l=="number")i=l-1;else{if(l===Oe)return Oe;l===ct&&(t.items.splice(i,1),i-=1)}}}else if(H(t)){r=Object.freeze(r.concat(t));const i=Dn("key",t.key,n,r);if(i===Oe)return Oe;i===ct&&(t.key=null);const l=Dn("value",t.value,n,r);if(l===Oe)return Oe;l===ct&&(t.value=null)}}return s}async function sl(e,t){const n=Ah(t);wn(e)?await bn(null,e.contents,n,Object.freeze([e]))===ct&&(e.contents=null):await bn(null,e,n,Object.freeze([]))}sl.BREAK=Oe;sl.SKIP=Ih;sl.REMOVE=ct;async function bn(e,t,n,r){const s=await Ph(e,t,n,r);if(J(s)||H(s))return Mh(e,r,s),bn(e,s,n,r);if(typeof s!="symbol"){if(Q(t)){r=Object.freeze(r.concat(t));for(let i=0;i<t.items.length;++i){const l=await bn(i,t.items[i],n,r);if(typeof l=="number")i=l-1;else{if(l===Oe)return Oe;l===ct&&(t.items.splice(i,1),i-=1)}}}else if(H(t)){r=Object.freeze(r.concat(t));const i=await bn("key",t.key,n,r);if(i===Oe)return Oe;i===ct&&(t.key=null);const l=await bn("value",t.value,n,r);if(l===Oe)return Oe;l===ct&&(t.value=null)}}return s}function Ah(e){return typeof e=="object"&&(e.Collection||e.Node||e.Value)?Object.assign({Alias:e.Node,Map:e.Node,Scalar:e.Node,Seq:e.Node},e.Value&&{Map:e.Value,Scalar:e.Value,Seq:e.Value},e.Collection&&{Map:e.Collection,Seq:e.Collection},e):e}function Ph(e,t,n,r){var s,i,l,o,a;if(typeof n=="function")return n(e,t,r);if(fr(t))return(s=n.Map)==null?void 0:s.call(n,e,t,r);if(dr(t))return(i=n.Seq)==null?void 0:i.call(n,e,t,r);if(H(t))return(l=n.Pair)==null?void 0:l.call(n,e,t,r);if(z(t))return(o=n.Scalar)==null?void 0:o.call(n,e,t,r);if(vn(t))return(a=n.Alias)==null?void 0:a.call(n,e,t,r)}function Mh(e,t,n){const r=t[t.length-1];if(Q(r))r.items[e]=n;else if(H(r))e==="key"?r.key=n:r.value=n;else if(wn(r))r.contents=n;else{const s=vn(r)?"alias":"scalar";throw new Error(`Cannot replace node with ${s} parent`)}}const Gy={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},Xy=e=>e.replace(/[!,[\]{}]/g,t=>Gy[t]);class Ee{constructor(t,n){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},Ee.defaultYaml,t),this.tags=Object.assign({},Ee.defaultTags,n)}clone(){const t=new Ee(this.yaml,this.tags);return t.docStart=this.docStart,t}atDocument(){const t=new Ee(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:Ee.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},Ee.defaultTags);break}return t}add(t,n){this.atNextDocument&&(this.yaml={explicit:Ee.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},Ee.defaultTags),this.atNextDocument=!1);const r=t.trim().split(/[ \t]+/),s=r.shift();switch(s){case"%TAG":{if(r.length!==2&&(n(0,"%TAG directive should contain exactly two parts"),r.length<2))return!1;const[i,l]=r;return this.tags[i]=l,!0}case"%YAML":{if(this.yaml.explicit=!0,r.length!==1)return n(0,"%YAML directive should contain exactly one part"),!1;const[i]=r;if(i==="1.1"||i==="1.2")return this.yaml.version=i,!0;{const l=/^\d+\.\d+$/.test(i);return n(6,`Unsupported YAML version ${i}`,l),!1}}default:return n(0,`Unknown directive ${s}`,!0),!1}}tagName(t,n){if(t==="!")return"!";if(t[0]!=="!")return n(`Not a valid tag: ${t}`),null;if(t[1]==="<"){const l=t.slice(2,-1);return l==="!"||l==="!!"?(n(`Verbatim tags aren't resolved, so ${t} is invalid.`),null):(t[t.length-1]!==">"&&n("Verbatim tags must end with a >"),l)}const[,r,s]=t.match(/^(.*!)([^!]*)$/s);s||n(`The ${t} tag has no suffix`);const i=this.tags[r];if(i)try{return i+decodeURIComponent(s)}catch(l){return n(String(l)),null}return r==="!"?t:(n(`Could not resolve tag: ${t}`),null)}tagString(t){for(const[n,r]of Object.entries(this.tags))if(t.startsWith(r))return n+Xy(t.substring(r.length));return t[0]==="!"?t:`!<${t}>`}toString(t){const n=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],r=Object.entries(this.tags);let s;if(t&&r.length>0&&J(t.contents)){const i={};Vt(t.contents,(l,o)=>{J(o)&&o.tag&&(i[o.tag]=!0)}),s=Object.keys(i)}else s=[];for(const[i,l]of r)i==="!!"&&l==="tag:yaml.org,2002:"||(!t||s.some(o=>o.startsWith(l)))&&n.push(`%TAG ${i} ${l}`);return n.join(`
`)}}Ee.defaultYaml={explicit:!1,version:"1.2"};Ee.defaultTags={"!!":"tag:yaml.org,2002:"};function jh(e){if(/[\x00-\x19\s,[\]{}]/.test(e)){const n=`Anchor must not contain whitespace or control characters: ${JSON.stringify(e)}`;throw new Error(n)}return!0}function Rh(e){const t=new Set;return Vt(e,{Value(n,r){r.anchor&&t.add(r.anchor)}}),t}function Dh(e,t){for(let n=1;;++n){const r=`${e}${n}`;if(!t.has(r))return r}}function Zy(e,t){const n=[],r=new Map;let s=null;return{onAnchor:i=>{n.push(i),s||(s=Rh(e));const l=Dh(t,s);return s.add(l),l},setAnchors:()=>{for(const i of n){const l=r.get(i);if(typeof l=="object"&&l.anchor&&(z(l.node)||Q(l.node)))l.node.anchor=l.anchor;else{const o=new Error("Failed to resolve repeated object (this should not happen)");throw o.source=i,o}}},sourceObjects:r}}function Bn(e,t,n,r){if(r&&typeof r=="object")if(Array.isArray(r))for(let s=0,i=r.length;s<i;++s){const l=r[s],o=Bn(e,r,String(s),l);o===void 0?delete r[s]:o!==l&&(r[s]=o)}else if(r instanceof Map)for(const s of Array.from(r.keys())){const i=r.get(s),l=Bn(e,r,s,i);l===void 0?r.delete(s):l!==i&&r.set(s,l)}else if(r instanceof Set)for(const s of Array.from(r)){const i=Bn(e,r,s,s);i===void 0?r.delete(s):i!==s&&(r.delete(s),r.add(i))}else for(const[s,i]of Object.entries(r)){const l=Bn(e,r,s,i);l===void 0?delete r[s]:l!==i&&(r[s]=l)}return e.call(t,n,r)}function He(e,t,n){if(Array.isArray(e))return e.map((r,s)=>He(r,String(s),n));if(e&&typeof e.toJSON=="function"){if(!n||!Jy(e))return e.toJSON(t,n);const r={aliasCount:0,count:1,res:void 0};n.anchors.set(e,r),n.onCreate=i=>{r.res=i,delete n.onCreate};const s=e.toJSON(t,n);return n.onCreate&&n.onCreate(s),s}return typeof e=="bigint"&&!(n!=null&&n.keep)?Number(e):e}class Xa{constructor(t){Object.defineProperty(this,Ye,{value:t})}clone(){const t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(t.range=this.range.slice()),t}toJS(t,{mapAsMap:n,maxAliasCount:r,onAnchor:s,reviver:i}={}){if(!wn(t))throw new TypeError("A document argument is required");const l={anchors:new Map,doc:t,keep:!0,mapAsMap:n===!0,mapKeyWarned:!1,maxAliasCount:typeof r=="number"?r:100},o=He(this,"",l);if(typeof s=="function")for(const{count:a,res:u}of l.anchors.values())s(u,a);return typeof i=="function"?Bn(i,{"":o},"",o):o}}class il extends Xa{constructor(t){super(Ga),this.source=t,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(t){let n;return Vt(t,{Node:(r,s)=>{if(s===this)return Vt.BREAK;s.anchor===this.source&&(n=s)}}),n}toJSON(t,n){if(!n)return{source:this.source};const{anchors:r,doc:s,maxAliasCount:i}=n,l=this.resolve(s);if(!l){const a=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(a)}let o=r.get(l);if(o||(He(l,null,n),o=r.get(l)),!o||o.res===void 0){const a="This should not happen: Alias anchor was not resolved?";throw new ReferenceError(a)}if(i>=0&&(o.count+=1,o.aliasCount===0&&(o.aliasCount=li(s,l,r)),o.count*o.aliasCount>i)){const a="Excessive alias count indicates a resource exhaustion attack";throw new ReferenceError(a)}return o.res}toString(t,n,r){const s=`*${this.source}`;if(t){if(jh(this.source),t.options.verifyAliasOrder&&!t.anchors.has(this.source)){const i=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(i)}if(t.implicitKey)return`${s} `}return s}}function li(e,t,n){if(vn(t)){const r=t.resolve(e),s=n&&r&&n.get(r);return s?s.count*s.aliasCount:0}else if(Q(t)){let r=0;for(const s of t.items){const i=li(e,s,n);i>r&&(r=i)}return r}else if(H(t)){const r=li(e,t.key,n),s=li(e,t.value,n);return Math.max(r,s)}return 1}const bh=e=>!e||typeof e!="function"&&typeof e!="object";class P extends Xa{constructor(t){super(ft),this.value=t}toJSON(t,n){return n!=null&&n.keep?this.value:He(this.value,t,n)}toString(){return String(this.value)}}P.BLOCK_FOLDED="BLOCK_FOLDED";P.BLOCK_LITERAL="BLOCK_LITERAL";P.PLAIN="PLAIN";P.QUOTE_DOUBLE="QUOTE_DOUBLE";P.QUOTE_SINGLE="QUOTE_SINGLE";const e0="tag:yaml.org,2002:";function t0(e,t,n){if(t){const r=n.filter(i=>i.tag===t),s=r.find(i=>!i.format)??r[0];if(!s)throw new Error(`Tag ${t} not found`);return s}return n.find(r=>{var s;return((s=r.identify)==null?void 0:s.call(r,e))&&!r.format})}function os(e,t,n){var f,p,g;if(wn(e)&&(e=e.contents),J(e))return e;if(H(e)){const w=(p=(f=n.schema[Ft]).createNode)==null?void 0:p.call(f,n.schema,null,n);return w.items.push(e),w}(e instanceof String||e instanceof Number||e instanceof Boolean||typeof BigInt<"u"&&e instanceof BigInt)&&(e=e.valueOf());const{aliasDuplicateObjects:r,onAnchor:s,onTagObj:i,schema:l,sourceObjects:o}=n;let a;if(r&&e&&typeof e=="object"){if(a=o.get(e),a)return a.anchor||(a.anchor=s(e)),new il(a.anchor);a={anchor:null,node:null},o.set(e,a)}t!=null&&t.startsWith("!!")&&(t=e0+t.slice(2));let u=t0(e,t,l.tags);if(!u){if(e&&typeof e.toJSON=="function"&&(e=e.toJSON()),!e||typeof e!="object"){const w=new P(e);return a&&(a.node=w),w}u=e instanceof Map?l[Ft]:Symbol.iterator in Object(e)?l[cr]:l[Ft]}i&&(i(u),delete n.onTagObj);const m=u!=null&&u.createNode?u.createNode(n.schema,e,n):typeof((g=u==null?void 0:u.nodeClass)==null?void 0:g.from)=="function"?u.nodeClass.from(n.schema,e,n):new P(e);return t?m.tag=t:u.default||(m.tag=u.tag),a&&(a.node=m),m}function ji(e,t,n){let r=n;for(let s=t.length-1;s>=0;--s){const i=t[s];if(typeof i=="number"&&Number.isInteger(i)&&i>=0){const l=[];l[i]=r,r=l}else r=new Map([[i,r]])}return os(r,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:e,sourceObjects:new Map})}const Pr=e=>e==null||typeof e=="object"&&!!e[Symbol.iterator]().next().done;class Bh extends Xa{constructor(t,n){super(t),Object.defineProperty(this,"schema",{value:n,configurable:!0,enumerable:!1,writable:!0})}clone(t){const n=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return t&&(n.schema=t),n.items=n.items.map(r=>J(r)||H(r)?r.clone(t):r),this.range&&(n.range=this.range.slice()),n}addIn(t,n){if(Pr(t))this.add(n);else{const[r,...s]=t,i=this.get(r,!0);if(Q(i))i.addIn(s,n);else if(i===void 0&&this.schema)this.set(r,ji(this.schema,s,n));else throw new Error(`Expected YAML collection at ${r}. Remaining path: ${s}`)}}deleteIn(t){const[n,...r]=t;if(r.length===0)return this.delete(n);const s=this.get(n,!0);if(Q(s))return s.deleteIn(r);throw new Error(`Expected YAML collection at ${n}. Remaining path: ${r}`)}getIn(t,n){const[r,...s]=t,i=this.get(r,!0);return s.length===0?!n&&z(i)?i.value:i:Q(i)?i.getIn(s,n):void 0}hasAllNullValues(t){return this.items.every(n=>{if(!H(n))return!1;const r=n.value;return r==null||t&&z(r)&&r.value==null&&!r.commentBefore&&!r.comment&&!r.tag})}hasIn(t){const[n,...r]=t;if(r.length===0)return this.has(n);const s=this.get(n,!0);return Q(s)?s.hasIn(r):!1}setIn(t,n){const[r,...s]=t;if(s.length===0)this.set(r,n);else{const i=this.get(r,!0);if(Q(i))i.setIn(s,n);else if(i===void 0&&this.schema)this.set(r,ji(this.schema,s,n));else throw new Error(`Expected YAML collection at ${r}. Remaining path: ${s}`)}}}const n0=e=>e.replace(/^(?!$)(?: $)?/gm,"#");function yt(e,t){return/^\n+$/.test(e)?e.substring(1):t?e.replace(/^(?! *$)/gm,t):e}const rn=(e,t,n)=>e.endsWith(`
`)?yt(n,t):n.includes(`
`)?`
`+yt(n,t):(e.endsWith(" ")?"":" ")+n,Fh="flow",Go="block",oi="quoted";function ll(e,t,n="flow",{indentAtStart:r,lineWidth:s=80,minContentWidth:i=20,onFold:l,onOverflow:o}={}){if(!s||s<0)return e;s<i&&(i=0);const a=Math.max(1+i,1+s-t.length);if(e.length<=a)return e;const u=[],m={};let f=s-t.length;typeof r=="number"&&(r>s-Math.max(2,i)?u.push(0):f=s-r);let p,g,w=!1,h=-1,v=-1,d=-1;n===Go&&(h=Ac(e,h,t.length),h!==-1&&(f=h+a));for(let y;y=e[h+=1];){if(n===oi&&y==="\\"){switch(v=h,e[h+1]){case"x":h+=3;break;case"u":h+=5;break;case"U":h+=9;break;default:h+=1}d=h}if(y===`
`)n===Go&&(h=Ac(e,h,t.length)),f=h+t.length+a,p=void 0;else{if(y===" "&&g&&g!==" "&&g!==`
`&&g!=="	"){const S=e[h+1];S&&S!==" "&&S!==`
`&&S!=="	"&&(p=h)}if(h>=f)if(p)u.push(p),f=p+a,p=void 0;else if(n===oi){for(;g===" "||g==="	";)g=y,y=e[h+=1],w=!0;const S=h>d+1?h-2:v-1;if(m[S])return e;u.push(S),m[S]=!0,f=S+a,p=void 0}else w=!0}g=y}if(w&&o&&o(),u.length===0)return e;l&&l();let c=e.slice(0,u[0]);for(let y=0;y<u.length;++y){const S=u[y],x=u[y+1]||e.length;S===0?c=`
${t}${e.slice(0,x)}`:(n===oi&&m[S]&&(c+=`${e[S]}\\`),c+=`
${t}${e.slice(S+1,x)}`)}return c}function Ac(e,t,n){let r=t,s=t+1,i=e[s];for(;i===" "||i==="	";)if(t<s+n)i=e[++t];else{do i=e[++t];while(i&&i!==`
`);r=t,s=t+1,i=e[s]}return r}const ol=(e,t)=>({indentAtStart:t?e.indent.length:e.indentAtStart,lineWidth:e.options.lineWidth,minContentWidth:e.options.minContentWidth}),al=e=>/^(%|---|\.\.\.)/m.test(e);function r0(e,t,n){if(!t||t<0)return!1;const r=t-n,s=e.length;if(s<=r)return!1;for(let i=0,l=0;i<s;++i)if(e[i]===`
`){if(i-l>r)return!0;if(l=i+1,s-l<=r)return!1}return!0}function Vr(e,t){const n=JSON.stringify(e);if(t.options.doubleQuotedAsJSON)return n;const{implicitKey:r}=t,s=t.options.doubleQuotedMinMultiLineLength,i=t.indent||(al(e)?"  ":"");let l="",o=0;for(let a=0,u=n[a];u;u=n[++a])if(u===" "&&n[a+1]==="\\"&&n[a+2]==="n"&&(l+=n.slice(o,a)+"\\ ",a+=1,o=a,u="\\"),u==="\\")switch(n[a+1]){case"u":{l+=n.slice(o,a);const m=n.substr(a+2,4);switch(m){case"0000":l+="\\0";break;case"0007":l+="\\a";break;case"000b":l+="\\v";break;case"001b":l+="\\e";break;case"0085":l+="\\N";break;case"00a0":l+="\\_";break;case"2028":l+="\\L";break;case"2029":l+="\\P";break;default:m.substr(0,2)==="00"?l+="\\x"+m.substr(2):l+=n.substr(a,6)}a+=5,o=a+1}break;case"n":if(r||n[a+2]==='"'||n.length<s)a+=1;else{for(l+=n.slice(o,a)+`

`;n[a+2]==="\\"&&n[a+3]==="n"&&n[a+4]!=='"';)l+=`
`,a+=2;l+=i,n[a+2]===" "&&(l+="\\"),a+=1,o=a+1}break;default:a+=1}return l=o?l+n.slice(o):n,r?l:ll(l,i,oi,ol(t,!1))}function Xo(e,t){if(t.options.singleQuote===!1||t.implicitKey&&e.includes(`
`)||/[ \t]\n|\n[ \t]/.test(e))return Vr(e,t);const n=t.indent||(al(e)?"  ":""),r="'"+e.replace(/'/g,"''").replace(/\n+/g,`$&
${n}`)+"'";return t.implicitKey?r:ll(r,n,Fh,ol(t,!1))}function Fn(e,t){const{singleQuote:n}=t.options;let r;if(n===!1)r=Vr;else{const s=e.includes('"'),i=e.includes("'");s&&!i?r=Xo:i&&!s?r=Vr:r=n?Xo:Vr}return r(e,t)}let Zo;try{Zo=new RegExp(`(^|(?<!
))
+(?!
|$)`,"g")}catch{Zo=/\n+(?!\n|$)/g}function ai({comment:e,type:t,value:n},r,s,i){const{blockQuote:l,commentString:o,lineWidth:a}=r.options;if(!l||/\n[\t ]+$/.test(n)||/^\s*$/.test(n))return Fn(n,r);const u=r.indent||(r.forceBlockIndent||al(n)?"  ":""),m=l==="literal"?!0:l==="folded"||t===P.BLOCK_FOLDED?!1:t===P.BLOCK_LITERAL?!0:!r0(n,a,u.length);if(!n)return m?`|
`:`>
`;let f,p;for(p=n.length;p>0;--p){const k=n[p-1];if(k!==`
`&&k!=="	"&&k!==" ")break}let g=n.substring(p);const w=g.indexOf(`
`);w===-1?f="-":n===g||w!==g.length-1?(f="+",i&&i()):f="",g&&(n=n.slice(0,-g.length),g[g.length-1]===`
`&&(g=g.slice(0,-1)),g=g.replace(Zo,`$&${u}`));let h=!1,v,d=-1;for(v=0;v<n.length;++v){const k=n[v];if(k===" ")h=!0;else if(k===`
`)d=v;else break}let c=n.substring(0,d<v?d+1:v);c&&(n=n.substring(c.length),c=c.replace(/\n+/g,`$&${u}`));let S=(m?"|":">")+(h?u?"2":"1":"")+f;if(e&&(S+=" "+o(e.replace(/ ?[\r\n]+/g," ")),s&&s()),m)return n=n.replace(/\n+/g,`$&${u}`),`${S}
${u}${c}${n}${g}`;n=n.replace(/\n+/g,`
$&`).replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${u}`);const x=ll(`${c}${n}${g}`,u,Go,ol(r,!0));return`${S}
${u}${x}`}function s0(e,t,n,r){const{type:s,value:i}=e,{actualString:l,implicitKey:o,indent:a,indentStep:u,inFlow:m}=t;if(o&&i.includes(`
`)||m&&/[[\]{},]/.test(i))return Fn(i,t);if(!i||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(i))return o||m||!i.includes(`
`)?Fn(i,t):ai(e,t,n,r);if(!o&&!m&&s!==P.PLAIN&&i.includes(`
`))return ai(e,t,n,r);if(al(i)){if(a==="")return t.forceBlockIndent=!0,ai(e,t,n,r);if(o&&a===u)return Fn(i,t)}const f=i.replace(/\n+/g,`$&
${a}`);if(l){const p=h=>{var v;return h.default&&h.tag!=="tag:yaml.org,2002:str"&&((v=h.test)==null?void 0:v.test(f))},{compat:g,tags:w}=t.doc.schema;if(w.some(p)||g!=null&&g.some(p))return Fn(i,t)}return o?f:ll(f,a,Fh,ol(t,!1))}function gs(e,t,n,r){const{implicitKey:s,inFlow:i}=t,l=typeof e.value=="string"?e:Object.assign({},e,{value:String(e.value)});let{type:o}=e;o!==P.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(l.value)&&(o=P.QUOTE_DOUBLE);const a=m=>{switch(m){case P.BLOCK_FOLDED:case P.BLOCK_LITERAL:return s||i?Fn(l.value,t):ai(l,t,n,r);case P.QUOTE_DOUBLE:return Vr(l.value,t);case P.QUOTE_SINGLE:return Xo(l.value,t);case P.PLAIN:return s0(l,t,n,r);default:return null}};let u=a(o);if(u===null){const{defaultKeyType:m,defaultStringType:f}=t.options,p=s&&m||f;if(u=a(p),u===null)throw new Error(`Unsupported default string type ${p}`)}return u}function zh(e,t){const n=Object.assign({blockQuote:!0,commentString:n0,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},e.schema.toStringOptions,t);let r;switch(n.collectionStyle){case"block":r=!1;break;case"flow":r=!0;break;default:r=null}return{anchors:new Set,doc:e,flowCollectionPadding:n.flowCollectionPadding?" ":"",indent:"",indentStep:typeof n.indent=="number"?" ".repeat(n.indent):"  ",inFlow:r,options:n}}function i0(e,t){var s;if(t.tag){const i=e.filter(l=>l.tag===t.tag);if(i.length>0)return i.find(l=>l.format===t.format)??i[0]}let n,r;if(z(t)){r=t.value;let i=e.filter(l=>{var o;return(o=l.identify)==null?void 0:o.call(l,r)});if(i.length>1){const l=i.filter(o=>o.test);l.length>0&&(i=l)}n=i.find(l=>l.format===t.format)??i.find(l=>!l.format)}else r=t,n=e.find(i=>i.nodeClass&&r instanceof i.nodeClass);if(!n){const i=((s=r==null?void 0:r.constructor)==null?void 0:s.name)??typeof r;throw new Error(`Tag not resolved for ${i} value`)}return n}function l0(e,t,{anchors:n,doc:r}){if(!r.directives)return"";const s=[],i=(z(e)||Q(e))&&e.anchor;i&&jh(i)&&(n.add(i),s.push(`&${i}`));const l=e.tag?e.tag:t.default?null:t.tag;return l&&s.push(r.directives.tagString(l)),s.join(" ")}function rr(e,t,n,r){var a;if(H(e))return e.toString(t,n,r);if(vn(e)){if(t.doc.directives)return e.toString(t);if((a=t.resolvedAliases)!=null&&a.has(e))throw new TypeError("Cannot stringify circular structure without alias nodes");t.resolvedAliases?t.resolvedAliases.add(e):t.resolvedAliases=new Set([e]),e=e.resolve(t.doc)}let s;const i=J(e)?e:t.doc.createNode(e,{onTagObj:u=>s=u});s||(s=i0(t.doc.schema.tags,i));const l=l0(i,s,t);l.length>0&&(t.indentAtStart=(t.indentAtStart??0)+l.length+1);const o=typeof s.stringify=="function"?s.stringify(i,t,n,r):z(i)?gs(i,t,n,r):i.toString(t,n,r);return l?z(i)||o[0]==="{"||o[0]==="["?`${l} ${o}`:`${l}
${t.indent}${o}`:o}function o0({key:e,value:t},n,r,s){const{allNullValues:i,doc:l,indent:o,indentStep:a,options:{commentString:u,indentSeq:m,simpleKeys:f}}=n;let p=J(e)&&e.comment||null;if(f){if(p)throw new Error("With simple keys, key nodes cannot have comments");if(Q(e)||!J(e)&&typeof e=="object"){const T="With simple keys, collection cannot be used as a key value";throw new Error(T)}}let g=!f&&(!e||p&&t==null&&!n.inFlow||Q(e)||(z(e)?e.type===P.BLOCK_FOLDED||e.type===P.BLOCK_LITERAL:typeof e=="object"));n=Object.assign({},n,{allNullValues:!1,implicitKey:!g&&(f||!i),indent:o+a});let w=!1,h=!1,v=rr(e,n,()=>w=!0,()=>h=!0);if(!g&&!n.inFlow&&v.length>1024){if(f)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");g=!0}if(n.inFlow){if(i||t==null)return w&&r&&r(),v===""?"?":g?`? ${v}`:v}else if(i&&!f||t==null&&g)return v=`? ${v}`,p&&!w?v+=rn(v,n.indent,u(p)):h&&s&&s(),v;w&&(p=null),g?(p&&(v+=rn(v,n.indent,u(p))),v=`? ${v}
${o}:`):(v=`${v}:`,p&&(v+=rn(v,n.indent,u(p))));let d,c,y;J(t)?(d=!!t.spaceBefore,c=t.commentBefore,y=t.comment):(d=!1,c=null,y=null,t&&typeof t=="object"&&(t=l.createNode(t))),n.implicitKey=!1,!g&&!p&&z(t)&&(n.indentAtStart=v.length+1),h=!1,!m&&a.length>=2&&!n.inFlow&&!g&&dr(t)&&!t.flow&&!t.tag&&!t.anchor&&(n.indent=n.indent.substring(2));let S=!1;const x=rr(t,n,()=>S=!0,()=>h=!0);let k=" ";if(p||d||c){if(k=d?`
`:"",c){const T=u(c);k+=`
${yt(T,n.indent)}`}x===""&&!n.inFlow?k===`
`&&(k=`

`):k+=`
${n.indent}`}else if(!g&&Q(t)){const T=x[0],N=x.indexOf(`
`),E=N!==-1,O=n.inFlow??t.flow??t.items.length===0;if(E||!O){let M=!1;if(E&&(T==="&"||T==="!")){let C=x.indexOf(" ");T==="&"&&C!==-1&&C<N&&x[C+1]==="!"&&(C=x.indexOf(" ",C+1)),(C===-1||N<C)&&(M=!0)}M||(k=`
${n.indent}`)}}else(x===""||x[0]===`
`)&&(k="");return v+=k+x,n.inFlow?S&&r&&r():y&&!S?v+=rn(v,n.indent,u(y)):h&&s&&s(),v}function Uh(e,t){(e==="debug"||e==="warn")&&(typeof process<"u"&&process.emitWarning?process.emitWarning(t):console.warn(t))}const bs="<<",wt={identify:e=>e===bs||typeof e=="symbol"&&e.description===bs,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new P(Symbol(bs)),{addToJSMap:Vh}),stringify:()=>bs},a0=(e,t)=>(wt.identify(t)||z(t)&&(!t.type||t.type===P.PLAIN)&&wt.identify(t.value))&&(e==null?void 0:e.doc.schema.tags.some(n=>n.tag===wt.tag&&n.default));function Vh(e,t,n){if(n=e&&vn(n)?n.resolve(e.doc):n,dr(n))for(const r of n.items)ql(e,t,r);else if(Array.isArray(n))for(const r of n)ql(e,t,r);else ql(e,t,n)}function ql(e,t,n){const r=e&&vn(n)?n.resolve(e.doc):n;if(!fr(r))throw new Error("Merge sources must be maps or map aliases");const s=r.toJSON(null,e,Map);for(const[i,l]of s)t instanceof Map?t.has(i)||t.set(i,l):t instanceof Set?t.add(i):Object.prototype.hasOwnProperty.call(t,i)||Object.defineProperty(t,i,{value:l,writable:!0,enumerable:!0,configurable:!0});return t}function Kh(e,t,{key:n,value:r}){if(J(n)&&n.addToJSMap)n.addToJSMap(e,t,r);else if(a0(e,n))Vh(e,t,r);else{const s=He(n,"",e);if(t instanceof Map)t.set(s,He(r,s,e));else if(t instanceof Set)t.add(s);else{const i=u0(n,s,e),l=He(r,i,e);i in t?Object.defineProperty(t,i,{value:l,writable:!0,enumerable:!0,configurable:!0}):t[i]=l}}return t}function u0(e,t,n){if(t===null)return"";if(typeof t!="object")return String(t);if(J(e)&&(n!=null&&n.doc)){const r=zh(n.doc,{});r.anchors=new Set;for(const i of n.anchors.keys())r.anchors.add(i.anchor);r.inFlow=!0,r.inStringifyKey=!0;const s=e.toString(r);if(!n.mapKeyWarned){let i=JSON.stringify(s);i.length>40&&(i=i.substring(0,36)+'..."'),Uh(n.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${i}. Set mapAsMap: true to use object keys.`),n.mapKeyWarned=!0}return s}return JSON.stringify(t)}function Za(e,t,n){const r=os(e,void 0,n),s=os(t,void 0,n);return new we(r,s)}class we{constructor(t,n=null){Object.defineProperty(this,Ye,{value:$h}),this.key=t,this.value=n}clone(t){let{key:n,value:r}=this;return J(n)&&(n=n.clone(t)),J(r)&&(r=r.clone(t)),new we(n,r)}toJSON(t,n){const r=n!=null&&n.mapAsMap?new Map:{};return Kh(n,r,this)}toString(t,n,r){return t!=null&&t.doc?o0(this,t,n,r):JSON.stringify(this)}}function Wh(e,t,n){return(t.inFlow??e.flow?f0:c0)(e,t,n)}function c0({comment:e,items:t},n,{blockItemPrefix:r,flowChars:s,itemIndent:i,onChompKeep:l,onComment:o}){const{indent:a,options:{commentString:u}}=n,m=Object.assign({},n,{indent:i,type:null});let f=!1;const p=[];for(let w=0;w<t.length;++w){const h=t[w];let v=null;if(J(h))!f&&h.spaceBefore&&p.push(""),Ri(n,p,h.commentBefore,f),h.comment&&(v=h.comment);else if(H(h)){const c=J(h.key)?h.key:null;c&&(!f&&c.spaceBefore&&p.push(""),Ri(n,p,c.commentBefore,f))}f=!1;let d=rr(h,m,()=>v=null,()=>f=!0);v&&(d+=rn(d,i,u(v))),f&&v&&(f=!1),p.push(r+d)}let g;if(p.length===0)g=s.start+s.end;else{g=p[0];for(let w=1;w<p.length;++w){const h=p[w];g+=h?`
${a}${h}`:`
`}}return e?(g+=`
`+yt(u(e),a),o&&o()):f&&l&&l(),g}function f0({items:e},t,{flowChars:n,itemIndent:r}){const{indent:s,indentStep:i,flowCollectionPadding:l,options:{commentString:o}}=t;r+=i;const a=Object.assign({},t,{indent:r,inFlow:!0,type:null});let u=!1,m=0;const f=[];for(let w=0;w<e.length;++w){const h=e[w];let v=null;if(J(h))h.spaceBefore&&f.push(""),Ri(t,f,h.commentBefore,!1),h.comment&&(v=h.comment);else if(H(h)){const c=J(h.key)?h.key:null;c&&(c.spaceBefore&&f.push(""),Ri(t,f,c.commentBefore,!1),c.comment&&(u=!0));const y=J(h.value)?h.value:null;y?(y.comment&&(v=y.comment),y.commentBefore&&(u=!0)):h.value==null&&(c!=null&&c.comment)&&(v=c.comment)}v&&(u=!0);let d=rr(h,a,()=>v=null);w<e.length-1&&(d+=","),v&&(d+=rn(d,r,o(v))),!u&&(f.length>m||d.includes(`
`))&&(u=!0),f.push(d),m=f.length}const{start:p,end:g}=n;if(f.length===0)return p+g;if(!u){const w=f.reduce((h,v)=>h+v.length+2,2);u=t.options.lineWidth>0&&w>t.options.lineWidth}if(u){let w=p;for(const h of f)w+=h?`
${i}${s}${h}`:`
`;return`${w}
${s}${g}`}else return`${p}${l}${f.join(" ")}${l}${g}`}function Ri({indent:e,options:{commentString:t}},n,r,s){if(r&&s&&(r=r.replace(/^\n+/,"")),r){const i=yt(t(r),e);n.push(i.trimStart())}}function sn(e,t){const n=z(t)?t.value:t;for(const r of e)if(H(r)&&(r.key===t||r.key===n||z(r.key)&&r.key.value===n))return r}class Re extends Bh{static get tagName(){return"tag:yaml.org,2002:map"}constructor(t){super(Ft,t),this.items=[]}static from(t,n,r){const{keepUndefined:s,replacer:i}=r,l=new this(t),o=(a,u)=>{if(typeof i=="function")u=i.call(n,a,u);else if(Array.isArray(i)&&!i.includes(a))return;(u!==void 0||s)&&l.items.push(Za(a,u,r))};if(n instanceof Map)for(const[a,u]of n)o(a,u);else if(n&&typeof n=="object")for(const a of Object.keys(n))o(a,n[a]);return typeof t.sortMapEntries=="function"&&l.items.sort(t.sortMapEntries),l}add(t,n){var l;let r;H(t)?r=t:!t||typeof t!="object"||!("key"in t)?r=new we(t,t==null?void 0:t.value):r=new we(t.key,t.value);const s=sn(this.items,r.key),i=(l=this.schema)==null?void 0:l.sortMapEntries;if(s){if(!n)throw new Error(`Key ${r.key} already set`);z(s.value)&&bh(r.value)?s.value.value=r.value:s.value=r.value}else if(i){const o=this.items.findIndex(a=>i(r,a)<0);o===-1?this.items.push(r):this.items.splice(o,0,r)}else this.items.push(r)}delete(t){const n=sn(this.items,t);return n?this.items.splice(this.items.indexOf(n),1).length>0:!1}get(t,n){const r=sn(this.items,t),s=r==null?void 0:r.value;return(!n&&z(s)?s.value:s)??void 0}has(t){return!!sn(this.items,t)}set(t,n){this.add(new we(t,n),!0)}toJSON(t,n,r){const s=r?new r:n!=null&&n.mapAsMap?new Map:{};n!=null&&n.onCreate&&n.onCreate(s);for(const i of this.items)Kh(n,s,i);return s}toString(t,n,r){if(!t)return JSON.stringify(this);for(const s of this.items)if(!H(s))throw new Error(`Map items must all be pairs; found ${JSON.stringify(s)} instead`);return!t.allNullValues&&this.hasAllNullValues(!1)&&(t=Object.assign({},t,{allNullValues:!0})),Wh(this,t,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:t.indent||"",onChompKeep:r,onComment:n})}}const hr={collection:"map",default:!0,nodeClass:Re,tag:"tag:yaml.org,2002:map",resolve(e,t){return fr(e)||t("Expected a mapping for this tag"),e},createNode:(e,t,n)=>Re.from(e,t,n)};class Kt extends Bh{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(t){super(cr,t),this.items=[]}add(t){this.items.push(t)}delete(t){const n=Bs(t);return typeof n!="number"?!1:this.items.splice(n,1).length>0}get(t,n){const r=Bs(t);if(typeof r!="number")return;const s=this.items[r];return!n&&z(s)?s.value:s}has(t){const n=Bs(t);return typeof n=="number"&&n<this.items.length}set(t,n){const r=Bs(t);if(typeof r!="number")throw new Error(`Expected a valid index, not ${t}.`);const s=this.items[r];z(s)&&bh(n)?s.value=n:this.items[r]=n}toJSON(t,n){const r=[];n!=null&&n.onCreate&&n.onCreate(r);let s=0;for(const i of this.items)r.push(He(i,String(s++),n));return r}toString(t,n,r){return t?Wh(this,t,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(t.indent||"")+"  ",onChompKeep:r,onComment:n}):JSON.stringify(this)}static from(t,n,r){const{replacer:s}=r,i=new this(t);if(n&&Symbol.iterator in Object(n)){let l=0;for(let o of n){if(typeof s=="function"){const a=n instanceof Set?o:String(l++);o=s.call(n,a,o)}i.items.push(os(o,void 0,r))}}return i}}function Bs(e){let t=z(e)?e.value:e;return t&&typeof t=="string"&&(t=Number(t)),typeof t=="number"&&Number.isInteger(t)&&t>=0?t:null}const pr={collection:"seq",default:!0,nodeClass:Kt,tag:"tag:yaml.org,2002:seq",resolve(e,t){return dr(e)||t("Expected a sequence for this tag"),e},createNode:(e,t,n)=>Kt.from(e,t,n)},ul={identify:e=>typeof e=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify(e,t,n,r){return t=Object.assign({actualString:!0},t),gs(e,t,n,r)}},cl={identify:e=>e==null,createNode:()=>new P(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new P(null),stringify:({source:e},t)=>typeof e=="string"&&cl.test.test(e)?e:t.options.nullStr},eu={identify:e=>typeof e=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:e=>new P(e[0]==="t"||e[0]==="T"),stringify({source:e,value:t},n){if(e&&eu.test.test(e)){const r=e[0]==="t"||e[0]==="T";if(t===r)return e}return t?n.options.trueStr:n.options.falseStr}};function st({format:e,minFractionDigits:t,tag:n,value:r}){if(typeof r=="bigint")return String(r);const s=typeof r=="number"?r:Number(r);if(!isFinite(s))return isNaN(s)?".nan":s<0?"-.inf":".inf";let i=JSON.stringify(r);if(!e&&t&&(!n||n==="tag:yaml.org,2002:float")&&/^\d/.test(i)){let l=i.indexOf(".");l<0&&(l=i.length,i+=".");let o=t-(i.length-l-1);for(;o-- >0;)i+="0"}return i}const Hh={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>e.slice(-3).toLowerCase()==="nan"?NaN:e[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:st},qh={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():st(e)}},Qh={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(e){const t=new P(parseFloat(e)),n=e.indexOf(".");return n!==-1&&e[e.length-1]==="0"&&(t.minFractionDigits=e.length-n-1),t},stringify:st},fl=e=>typeof e=="bigint"||Number.isInteger(e),tu=(e,t,n,{intAsBigInt:r})=>r?BigInt(e):parseInt(e.substring(t),n);function Yh(e,t,n){const{value:r}=e;return fl(r)&&r>=0?n+r.toString(t):st(e)}const Jh={identify:e=>fl(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(e,t,n)=>tu(e,2,8,n),stringify:e=>Yh(e,8,"0o")},Gh={identify:fl,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(e,t,n)=>tu(e,0,10,n),stringify:st},Xh={identify:e=>fl(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(e,t,n)=>tu(e,2,16,n),stringify:e=>Yh(e,16,"0x")},d0=[hr,pr,ul,cl,eu,Jh,Gh,Xh,Hh,qh,Qh];function Pc(e){return typeof e=="bigint"||Number.isInteger(e)}const Fs=({value:e})=>JSON.stringify(e),h0=[{identify:e=>typeof e=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:Fs},{identify:e=>e==null,createNode:()=>new P(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:Fs},{identify:e=>typeof e=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true|false$/,resolve:e=>e==="true",stringify:Fs},{identify:Pc,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(e,t,{intAsBigInt:n})=>n?BigInt(e):parseInt(e,10),stringify:({value:e})=>Pc(e)?e.toString():JSON.stringify(e)},{identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:e=>parseFloat(e),stringify:Fs}],p0={default:!0,tag:"",test:/^/,resolve(e,t){return t(`Unresolved plain scalar ${JSON.stringify(e)}`),e}},m0=[hr,pr].concat(h0,p0),nu={identify:e=>e instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(e,t){if(typeof Buffer=="function")return Buffer.from(e,"base64");if(typeof atob=="function"){const n=atob(e.replace(/[\n\r]/g,"")),r=new Uint8Array(n.length);for(let s=0;s<n.length;++s)r[s]=n.charCodeAt(s);return r}else return t("This environment does not support reading binary tags; either Buffer or atob is required"),e},stringify({comment:e,type:t,value:n},r,s,i){const l=n;let o;if(typeof Buffer=="function")o=l instanceof Buffer?l.toString("base64"):Buffer.from(l.buffer).toString("base64");else if(typeof btoa=="function"){let a="";for(let u=0;u<l.length;++u)a+=String.fromCharCode(l[u]);o=btoa(a)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(t||(t=P.BLOCK_LITERAL),t!==P.QUOTE_DOUBLE){const a=Math.max(r.options.lineWidth-r.indent.length,r.options.minContentWidth),u=Math.ceil(o.length/a),m=new Array(u);for(let f=0,p=0;f<u;++f,p+=a)m[f]=o.substr(p,a);o=m.join(t===P.BLOCK_LITERAL?`
`:" ")}return gs({comment:e,type:t,value:o},r,s,i)}};function Zh(e,t){if(dr(e))for(let n=0;n<e.items.length;++n){let r=e.items[n];if(!H(r)){if(fr(r)){r.items.length>1&&t("Each pair must have its own sequence indicator");const s=r.items[0]||new we(new P(null));if(r.commentBefore&&(s.key.commentBefore=s.key.commentBefore?`${r.commentBefore}
${s.key.commentBefore}`:r.commentBefore),r.comment){const i=s.value??s.key;i.comment=i.comment?`${r.comment}
${i.comment}`:r.comment}r=s}e.items[n]=H(r)?r:new we(r)}}else t("Expected a sequence for this tag");return e}function ep(e,t,n){const{replacer:r}=n,s=new Kt(e);s.tag="tag:yaml.org,2002:pairs";let i=0;if(t&&Symbol.iterator in Object(t))for(let l of t){typeof r=="function"&&(l=r.call(t,String(i++),l));let o,a;if(Array.isArray(l))if(l.length===2)o=l[0],a=l[1];else throw new TypeError(`Expected [key, value] tuple: ${l}`);else if(l&&l instanceof Object){const u=Object.keys(l);if(u.length===1)o=u[0],a=l[o];else throw new TypeError(`Expected tuple with one key, not ${u.length} keys`)}else o=l;s.items.push(Za(o,a,n))}return s}const ru={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:Zh,createNode:ep};class Qn extends Kt{constructor(){super(),this.add=Re.prototype.add.bind(this),this.delete=Re.prototype.delete.bind(this),this.get=Re.prototype.get.bind(this),this.has=Re.prototype.has.bind(this),this.set=Re.prototype.set.bind(this),this.tag=Qn.tag}toJSON(t,n){if(!n)return super.toJSON(t);const r=new Map;n!=null&&n.onCreate&&n.onCreate(r);for(const s of this.items){let i,l;if(H(s)?(i=He(s.key,"",n),l=He(s.value,i,n)):i=He(s,"",n),r.has(i))throw new Error("Ordered maps must not include duplicate keys");r.set(i,l)}return r}static from(t,n,r){const s=ep(t,n,r),i=new this;return i.items=s.items,i}}Qn.tag="tag:yaml.org,2002:omap";const su={collection:"seq",identify:e=>e instanceof Map,nodeClass:Qn,default:!1,tag:"tag:yaml.org,2002:omap",resolve(e,t){const n=Zh(e,t),r=[];for(const{key:s}of n.items)z(s)&&(r.includes(s.value)?t(`Ordered maps must not include duplicate keys: ${s.value}`):r.push(s.value));return Object.assign(new Qn,n)},createNode:(e,t,n)=>Qn.from(e,t,n)};function tp({value:e,source:t},n){return t&&(e?np:rp).test.test(t)?t:e?n.options.trueStr:n.options.falseStr}const np={identify:e=>e===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new P(!0),stringify:tp},rp={identify:e=>e===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new P(!1),stringify:tp},g0={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>e.slice(-3).toLowerCase()==="nan"?NaN:e[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:st},y0={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e.replace(/_/g,"")),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():st(e)}},v0={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(e){const t=new P(parseFloat(e.replace(/_/g,""))),n=e.indexOf(".");if(n!==-1){const r=e.substring(n+1).replace(/_/g,"");r[r.length-1]==="0"&&(t.minFractionDigits=r.length)}return t},stringify:st},ys=e=>typeof e=="bigint"||Number.isInteger(e);function dl(e,t,n,{intAsBigInt:r}){const s=e[0];if((s==="-"||s==="+")&&(t+=1),e=e.substring(t).replace(/_/g,""),r){switch(n){case 2:e=`0b${e}`;break;case 8:e=`0o${e}`;break;case 16:e=`0x${e}`;break}const l=BigInt(e);return s==="-"?BigInt(-1)*l:l}const i=parseInt(e,n);return s==="-"?-1*i:i}function iu(e,t,n){const{value:r}=e;if(ys(r)){const s=r.toString(t);return r<0?"-"+n+s.substr(1):n+s}return st(e)}const w0={identify:ys,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(e,t,n)=>dl(e,2,2,n),stringify:e=>iu(e,2,"0b")},S0={identify:ys,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(e,t,n)=>dl(e,1,8,n),stringify:e=>iu(e,8,"0")},k0={identify:ys,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(e,t,n)=>dl(e,0,10,n),stringify:st},E0={identify:ys,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(e,t,n)=>dl(e,2,16,n),stringify:e=>iu(e,16,"0x")};class Yn extends Re{constructor(t){super(t),this.tag=Yn.tag}add(t){let n;H(t)?n=t:t&&typeof t=="object"&&"key"in t&&"value"in t&&t.value===null?n=new we(t.key,null):n=new we(t,null),sn(this.items,n.key)||this.items.push(n)}get(t,n){const r=sn(this.items,t);return!n&&H(r)?z(r.key)?r.key.value:r.key:r}set(t,n){if(typeof n!="boolean")throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof n}`);const r=sn(this.items,t);r&&!n?this.items.splice(this.items.indexOf(r),1):!r&&n&&this.items.push(new we(t))}toJSON(t,n){return super.toJSON(t,n,Set)}toString(t,n,r){if(!t)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},t,{allNullValues:!0}),n,r);throw new Error("Set items must all have null values")}static from(t,n,r){const{replacer:s}=r,i=new this(t);if(n&&Symbol.iterator in Object(n))for(let l of n)typeof s=="function"&&(l=s.call(n,l,l)),i.items.push(Za(l,null,r));return i}}Yn.tag="tag:yaml.org,2002:set";const lu={collection:"map",identify:e=>e instanceof Set,nodeClass:Yn,default:!1,tag:"tag:yaml.org,2002:set",createNode:(e,t,n)=>Yn.from(e,t,n),resolve(e,t){if(fr(e)){if(e.hasAllNullValues(!0))return Object.assign(new Yn,e);t("Set items must all have null values")}else t("Expected a mapping for this tag");return e}};function ou(e,t){const n=e[0],r=n==="-"||n==="+"?e.substring(1):e,s=l=>t?BigInt(l):Number(l),i=r.replace(/_/g,"").split(":").reduce((l,o)=>l*s(60)+s(o),s(0));return n==="-"?s(-1)*i:i}function sp(e){let{value:t}=e,n=l=>l;if(typeof t=="bigint")n=l=>BigInt(l);else if(isNaN(t)||!isFinite(t))return st(e);let r="";t<0&&(r="-",t*=n(-1));const s=n(60),i=[t%s];return t<60?i.unshift(0):(t=(t-i[0])/s,i.unshift(t%s),t>=60&&(t=(t-i[0])/s,i.unshift(t))),r+i.map(l=>String(l).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}const ip={identify:e=>typeof e=="bigint"||Number.isInteger(e),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(e,t,{intAsBigInt:n})=>ou(e,n),stringify:sp},lp={identify:e=>typeof e=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:e=>ou(e,!1),stringify:sp},hl={identify:e=>e instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(e){const t=e.match(hl.test);if(!t)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");const[,n,r,s,i,l,o]=t.map(Number),a=t[7]?Number((t[7]+"00").substr(1,3)):0;let u=Date.UTC(n,r-1,s,i||0,l||0,o||0,a);const m=t[8];if(m&&m!=="Z"){let f=ou(m,!1);Math.abs(f)<30&&(f*=60),u-=6e4*f}return new Date(u)},stringify:({value:e})=>e.toISOString().replace(/((T00:00)?:00)?\.000Z$/,"")},Mc=[hr,pr,ul,cl,np,rp,w0,S0,k0,E0,g0,y0,v0,nu,wt,su,ru,lu,ip,lp,hl],jc=new Map([["core",d0],["failsafe",[hr,pr,ul]],["json",m0],["yaml11",Mc],["yaml-1.1",Mc]]),Rc={binary:nu,bool:eu,float:Qh,floatExp:qh,floatNaN:Hh,floatTime:lp,int:Gh,intHex:Xh,intOct:Jh,intTime:ip,map:hr,merge:wt,null:cl,omap:su,pairs:ru,seq:pr,set:lu,timestamp:hl},N0={"tag:yaml.org,2002:binary":nu,"tag:yaml.org,2002:merge":wt,"tag:yaml.org,2002:omap":su,"tag:yaml.org,2002:pairs":ru,"tag:yaml.org,2002:set":lu,"tag:yaml.org,2002:timestamp":hl};function Ql(e,t,n){const r=jc.get(t);if(r&&!e)return n&&!r.includes(wt)?r.concat(wt):r.slice();let s=r;if(!s)if(Array.isArray(e))s=[];else{const i=Array.from(jc.keys()).filter(l=>l!=="yaml11").map(l=>JSON.stringify(l)).join(", ");throw new Error(`Unknown schema "${t}"; use one of ${i} or define customTags array`)}if(Array.isArray(e))for(const i of e)s=s.concat(i);else typeof e=="function"&&(s=e(s.slice()));return n&&(s=s.concat(wt)),s.reduce((i,l)=>{const o=typeof l=="string"?Rc[l]:l;if(!o){const a=JSON.stringify(l),u=Object.keys(Rc).map(m=>JSON.stringify(m)).join(", ");throw new Error(`Unknown custom tag ${a}; use one of ${u}`)}return i.includes(o)||i.push(o),i},[])}const x0=(e,t)=>e.key<t.key?-1:e.key>t.key?1:0;class pl{constructor({compat:t,customTags:n,merge:r,resolveKnownTags:s,schema:i,sortMapEntries:l,toStringDefaults:o}){this.compat=Array.isArray(t)?Ql(t,"compat"):t?Ql(null,t):null,this.name=typeof i=="string"&&i||"core",this.knownTags=s?N0:{},this.tags=Ql(n,this.name,r),this.toStringOptions=o??null,Object.defineProperty(this,Ft,{value:hr}),Object.defineProperty(this,ft,{value:ul}),Object.defineProperty(this,cr,{value:pr}),this.sortMapEntries=typeof l=="function"?l:l===!0?x0:null}clone(){const t=Object.create(pl.prototype,Object.getOwnPropertyDescriptors(this));return t.tags=this.tags.slice(),t}}function T0(e,t){var a;const n=[];let r=t.directives===!0;if(t.directives!==!1&&e.directives){const u=e.directives.toString(e);u?(n.push(u),r=!0):e.directives.docStart&&(r=!0)}r&&n.push("---");const s=zh(e,t),{commentString:i}=s.options;if(e.commentBefore){n.length!==1&&n.unshift("");const u=i(e.commentBefore);n.unshift(yt(u,""))}let l=!1,o=null;if(e.contents){if(J(e.contents)){if(e.contents.spaceBefore&&r&&n.push(""),e.contents.commentBefore){const f=i(e.contents.commentBefore);n.push(yt(f,""))}s.forceBlockIndent=!!e.comment,o=e.contents.comment}const u=o?void 0:()=>l=!0;let m=rr(e.contents,s,()=>o=null,u);o&&(m+=rn(m,"",i(o))),(m[0]==="|"||m[0]===">")&&n[n.length-1]==="---"?n[n.length-1]=`--- ${m}`:n.push(m)}else n.push(rr(e.contents,s));if((a=e.directives)!=null&&a.docEnd)if(e.comment){const u=i(e.comment);u.includes(`
`)?(n.push("..."),n.push(yt(u,""))):n.push(`... ${u}`)}else n.push("...");else{let u=e.comment;u&&l&&(u=u.replace(/^\n+/,"")),u&&((!l||o)&&n[n.length-1]!==""&&n.push(""),n.push(yt(i(u),"")))}return n.join(`
`)+`
`}class mr{constructor(t,n,r){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,Ye,{value:Jo});let s=null;typeof n=="function"||Array.isArray(n)?s=n:r===void 0&&n&&(r=n,n=void 0);const i=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},r);this.options=i;let{version:l}=i;r!=null&&r._directives?(this.directives=r._directives.atDocument(),this.directives.yaml.explicit&&(l=this.directives.yaml.version)):this.directives=new Ee({version:l}),this.setSchema(l,r),this.contents=t===void 0?null:this.createNode(t,s,r)}clone(){const t=Object.create(mr.prototype,{[Ye]:{value:Jo}});return t.commentBefore=this.commentBefore,t.comment=this.comment,t.errors=this.errors.slice(),t.warnings=this.warnings.slice(),t.options=Object.assign({},this.options),this.directives&&(t.directives=this.directives.clone()),t.schema=this.schema.clone(),t.contents=J(this.contents)?this.contents.clone(t.schema):this.contents,this.range&&(t.range=this.range.slice()),t}add(t){kn(this.contents)&&this.contents.add(t)}addIn(t,n){kn(this.contents)&&this.contents.addIn(t,n)}createAlias(t,n){if(!t.anchor){const r=Rh(this);t.anchor=!n||r.has(n)?Dh(n||"a",r):n}return new il(t.anchor)}createNode(t,n,r){let s;if(typeof n=="function")t=n.call({"":t},"",t),s=n;else if(Array.isArray(n)){const v=c=>typeof c=="number"||c instanceof String||c instanceof Number,d=n.filter(v).map(String);d.length>0&&(n=n.concat(d)),s=n}else r===void 0&&n&&(r=n,n=void 0);const{aliasDuplicateObjects:i,anchorPrefix:l,flow:o,keepUndefined:a,onTagObj:u,tag:m}=r??{},{onAnchor:f,setAnchors:p,sourceObjects:g}=Zy(this,l||"a"),w={aliasDuplicateObjects:i??!0,keepUndefined:a??!1,onAnchor:f,onTagObj:u,replacer:s,schema:this.schema,sourceObjects:g},h=os(t,m,w);return o&&Q(h)&&(h.flow=!0),p(),h}createPair(t,n,r={}){const s=this.createNode(t,null,r),i=this.createNode(n,null,r);return new we(s,i)}delete(t){return kn(this.contents)?this.contents.delete(t):!1}deleteIn(t){return Pr(t)?this.contents==null?!1:(this.contents=null,!0):kn(this.contents)?this.contents.deleteIn(t):!1}get(t,n){return Q(this.contents)?this.contents.get(t,n):void 0}getIn(t,n){return Pr(t)?!n&&z(this.contents)?this.contents.value:this.contents:Q(this.contents)?this.contents.getIn(t,n):void 0}has(t){return Q(this.contents)?this.contents.has(t):!1}hasIn(t){return Pr(t)?this.contents!==void 0:Q(this.contents)?this.contents.hasIn(t):!1}set(t,n){this.contents==null?this.contents=ji(this.schema,[t],n):kn(this.contents)&&this.contents.set(t,n)}setIn(t,n){Pr(t)?this.contents=n:this.contents==null?this.contents=ji(this.schema,Array.from(t),n):kn(this.contents)&&this.contents.setIn(t,n)}setSchema(t,n={}){typeof t=="number"&&(t=String(t));let r;switch(t){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new Ee({version:"1.1"}),r={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=t:this.directives=new Ee({version:t}),r={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,r=null;break;default:{const s=JSON.stringify(t);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${s}`)}}if(n.schema instanceof Object)this.schema=n.schema;else if(r)this.schema=new pl(Object.assign(r,n));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:t,jsonArg:n,mapAsMap:r,maxAliasCount:s,onAnchor:i,reviver:l}={}){const o={anchors:new Map,doc:this,keep:!t,mapAsMap:r===!0,mapKeyWarned:!1,maxAliasCount:typeof s=="number"?s:100},a=He(this.contents,n??"",o);if(typeof i=="function")for(const{count:u,res:m}of o.anchors.values())i(m,u);return typeof l=="function"?Bn(l,{"":a},"",a):a}toJSON(t,n){return this.toJS({json:!0,jsonArg:t,mapAsMap:!1,onAnchor:n})}toString(t={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in t&&(!Number.isInteger(t.indent)||Number(t.indent)<=0)){const n=JSON.stringify(t.indent);throw new Error(`"indent" option must be a positive integer, not ${n}`)}return T0(this,t)}}function kn(e){if(Q(e))return!0;throw new Error("Expected a YAML collection as document contents")}class au extends Error{constructor(t,n,r,s){super(),this.name=t,this.code=r,this.message=s,this.pos=n}}class ln extends au{constructor(t,n,r){super("YAMLParseError",t,n,r)}}class op extends au{constructor(t,n,r){super("YAMLWarning",t,n,r)}}const Di=(e,t)=>n=>{if(n.pos[0]===-1)return;n.linePos=n.pos.map(o=>t.linePos(o));const{line:r,col:s}=n.linePos[0];n.message+=` at line ${r}, column ${s}`;let i=s-1,l=e.substring(t.lineStarts[r-1],t.lineStarts[r]).replace(/[\n\r]+$/,"");if(i>=60&&l.length>80){const o=Math.min(i-39,l.length-79);l="…"+l.substring(o),i-=o-1}if(l.length>80&&(l=l.substring(0,79)+"…"),r>1&&/^ *$/.test(l.substring(0,i))){let o=e.substring(t.lineStarts[r-2],t.lineStarts[r-1]);o.length>80&&(o=o.substring(0,79)+`…
`),l=o+l}if(/[^ ]/.test(l)){let o=1;const a=n.linePos[1];a&&a.line===r&&a.col>s&&(o=Math.max(1,Math.min(a.col-s,80-i)));const u=" ".repeat(i)+"^".repeat(o);n.message+=`:

${l}
${u}
`}};function sr(e,{flow:t,indicator:n,next:r,offset:s,onError:i,parentIndent:l,startOnNewline:o}){let a=!1,u=o,m=o,f="",p="",g=!1,w=!1,h=null,v=null,d=null,c=null,y=null,S=null,x=null;for(const N of e)switch(w&&(N.type!=="space"&&N.type!=="newline"&&N.type!=="comma"&&i(N.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),w=!1),h&&(u&&N.type!=="comment"&&N.type!=="newline"&&i(h,"TAB_AS_INDENT","Tabs are not allowed as indentation"),h=null),N.type){case"space":!t&&(n!=="doc-start"||(r==null?void 0:r.type)!=="flow-collection")&&N.source.includes("	")&&(h=N),m=!0;break;case"comment":{m||i(N,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const E=N.source.substring(1)||" ";f?f+=p+E:f=E,p="",u=!1;break}case"newline":u?f?f+=N.source:a=!0:p+=N.source,u=!0,g=!0,(v||d)&&(c=N),m=!0;break;case"anchor":v&&i(N,"MULTIPLE_ANCHORS","A node can have at most one anchor"),N.source.endsWith(":")&&i(N.offset+N.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),v=N,x===null&&(x=N.offset),u=!1,m=!1,w=!0;break;case"tag":{d&&i(N,"MULTIPLE_TAGS","A node can have at most one tag"),d=N,x===null&&(x=N.offset),u=!1,m=!1,w=!0;break}case n:(v||d)&&i(N,"BAD_PROP_ORDER",`Anchors and tags must be after the ${N.source} indicator`),S&&i(N,"UNEXPECTED_TOKEN",`Unexpected ${N.source} in ${t??"collection"}`),S=N,u=n==="seq-item-ind"||n==="explicit-key-ind",m=!1;break;case"comma":if(t){y&&i(N,"UNEXPECTED_TOKEN",`Unexpected , in ${t}`),y=N,u=!1,m=!1;break}default:i(N,"UNEXPECTED_TOKEN",`Unexpected ${N.type} token`),u=!1,m=!1}const k=e[e.length-1],T=k?k.offset+k.source.length:s;return w&&r&&r.type!=="space"&&r.type!=="newline"&&r.type!=="comma"&&(r.type!=="scalar"||r.source!=="")&&i(r.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),h&&(u&&h.indent<=l||(r==null?void 0:r.type)==="block-map"||(r==null?void 0:r.type)==="block-seq")&&i(h,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:y,found:S,spaceBefore:a,comment:f,hasNewline:g,anchor:v,tag:d,newlineAfterProp:c,end:T,start:x??T}}function as(e){if(!e)return null;switch(e.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(e.source.includes(`
`))return!0;if(e.end){for(const t of e.end)if(t.type==="newline")return!0}return!1;case"flow-collection":for(const t of e.items){for(const n of t.start)if(n.type==="newline")return!0;if(t.sep){for(const n of t.sep)if(n.type==="newline")return!0}if(as(t.key)||as(t.value))return!0}return!1;default:return!0}}function ea(e,t,n){if((t==null?void 0:t.type)==="flow-collection"){const r=t.end[0];r.indent===e&&(r.source==="]"||r.source==="}")&&as(t)&&n(r,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}function ap(e,t,n){const{uniqueKeys:r}=e.options;if(r===!1)return!1;const s=typeof r=="function"?r:(i,l)=>i===l||z(i)&&z(l)&&i.value===l.value;return t.some(i=>s(i.key,n))}const Dc="All mapping items must start at the same column";function C0({composeNode:e,composeEmptyNode:t},n,r,s,i){var m;const l=(i==null?void 0:i.nodeClass)??Re,o=new l(n.schema);n.atRoot&&(n.atRoot=!1);let a=r.offset,u=null;for(const f of r.items){const{start:p,key:g,sep:w,value:h}=f,v=sr(p,{indicator:"explicit-key-ind",next:g??(w==null?void 0:w[0]),offset:a,onError:s,parentIndent:r.indent,startOnNewline:!0}),d=!v.found;if(d){if(g&&(g.type==="block-seq"?s(a,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in g&&g.indent!==r.indent&&s(a,"BAD_INDENT",Dc)),!v.anchor&&!v.tag&&!w){u=v.end,v.comment&&(o.comment?o.comment+=`
`+v.comment:o.comment=v.comment);continue}(v.newlineAfterProp||as(g))&&s(g??p[p.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else((m=v.found)==null?void 0:m.indent)!==r.indent&&s(a,"BAD_INDENT",Dc);n.atKey=!0;const c=v.end,y=g?e(n,g,v,s):t(n,c,p,null,v,s);n.schema.compat&&ea(r.indent,g,s),n.atKey=!1,ap(n,o.items,y)&&s(c,"DUPLICATE_KEY","Map keys must be unique");const S=sr(w??[],{indicator:"map-value-ind",next:h,offset:y.range[2],onError:s,parentIndent:r.indent,startOnNewline:!g||g.type==="block-scalar"});if(a=S.end,S.found){d&&((h==null?void 0:h.type)==="block-map"&&!S.hasNewline&&s(a,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),n.options.strict&&v.start<S.found.offset-1024&&s(y.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));const x=h?e(n,h,S,s):t(n,a,w,null,S,s);n.schema.compat&&ea(r.indent,h,s),a=x.range[2];const k=new we(y,x);n.options.keepSourceTokens&&(k.srcToken=f),o.items.push(k)}else{d&&s(y.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),S.comment&&(y.comment?y.comment+=`
`+S.comment:y.comment=S.comment);const x=new we(y);n.options.keepSourceTokens&&(x.srcToken=f),o.items.push(x)}}return u&&u<a&&s(u,"IMPOSSIBLE","Map comment with trailing content"),o.range=[r.offset,a,u??a],o}function _0({composeNode:e,composeEmptyNode:t},n,r,s,i){const l=(i==null?void 0:i.nodeClass)??Kt,o=new l(n.schema);n.atRoot&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let a=r.offset,u=null;for(const{start:m,value:f}of r.items){const p=sr(m,{indicator:"seq-item-ind",next:f,offset:a,onError:s,parentIndent:r.indent,startOnNewline:!0});if(!p.found)if(p.anchor||p.tag||f)f&&f.type==="block-seq"?s(p.end,"BAD_INDENT","All sequence items must start at the same column"):s(a,"MISSING_CHAR","Sequence item without - indicator");else{u=p.end,p.comment&&(o.comment=p.comment);continue}const g=f?e(n,f,p,s):t(n,p.end,m,null,p,s);n.schema.compat&&ea(r.indent,f,s),a=g.range[2],o.items.push(g)}return o.range=[r.offset,a,u??a],o}function vs(e,t,n,r){let s="";if(e){let i=!1,l="";for(const o of e){const{source:a,type:u}=o;switch(u){case"space":i=!0;break;case"comment":{n&&!i&&r(o,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const m=a.substring(1)||" ";s?s+=l+m:s=m,l="";break}case"newline":s&&(l+=a),i=!0;break;default:r(o,"UNEXPECTED_TOKEN",`Unexpected ${u} at node end`)}t+=a.length}}return{comment:s,offset:t}}const Yl="Block collections are not allowed within flow collections",Jl=e=>e&&(e.type==="block-map"||e.type==="block-seq");function L0({composeNode:e,composeEmptyNode:t},n,r,s,i){const l=r.start.source==="{",o=l?"flow map":"flow sequence",a=(i==null?void 0:i.nodeClass)??(l?Re:Kt),u=new a(n.schema);u.flow=!0;const m=n.atRoot;m&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let f=r.offset+r.start.source.length;for(let v=0;v<r.items.length;++v){const d=r.items[v],{start:c,key:y,sep:S,value:x}=d,k=sr(c,{flow:o,indicator:"explicit-key-ind",next:y??(S==null?void 0:S[0]),offset:f,onError:s,parentIndent:r.indent,startOnNewline:!1});if(!k.found){if(!k.anchor&&!k.tag&&!S&&!x){v===0&&k.comma?s(k.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${o}`):v<r.items.length-1&&s(k.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${o}`),k.comment&&(u.comment?u.comment+=`
`+k.comment:u.comment=k.comment),f=k.end;continue}!l&&n.options.strict&&as(y)&&s(y,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(v===0)k.comma&&s(k.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${o}`);else if(k.comma||s(k.start,"MISSING_CHAR",`Missing , between ${o} items`),k.comment){let T="";e:for(const N of c)switch(N.type){case"comma":case"space":break;case"comment":T=N.source.substring(1);break e;default:break e}if(T){let N=u.items[u.items.length-1];H(N)&&(N=N.value??N.key),N.comment?N.comment+=`
`+T:N.comment=T,k.comment=k.comment.substring(T.length+1)}}if(!l&&!S&&!k.found){const T=x?e(n,x,k,s):t(n,k.end,S,null,k,s);u.items.push(T),f=T.range[2],Jl(x)&&s(T.range,"BLOCK_IN_FLOW",Yl)}else{n.atKey=!0;const T=k.end,N=y?e(n,y,k,s):t(n,T,c,null,k,s);Jl(y)&&s(N.range,"BLOCK_IN_FLOW",Yl),n.atKey=!1;const E=sr(S??[],{flow:o,indicator:"map-value-ind",next:x,offset:N.range[2],onError:s,parentIndent:r.indent,startOnNewline:!1});if(E.found){if(!l&&!k.found&&n.options.strict){if(S)for(const C of S){if(C===E.found)break;if(C.type==="newline"){s(C,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}k.start<E.found.offset-1024&&s(E.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else x&&("source"in x&&x.source&&x.source[0]===":"?s(x,"MISSING_CHAR",`Missing space after : in ${o}`):s(E.start,"MISSING_CHAR",`Missing , or : between ${o} items`));const O=x?e(n,x,E,s):E.found?t(n,E.end,S,null,E,s):null;O?Jl(x)&&s(O.range,"BLOCK_IN_FLOW",Yl):E.comment&&(N.comment?N.comment+=`
`+E.comment:N.comment=E.comment);const M=new we(N,O);if(n.options.keepSourceTokens&&(M.srcToken=d),l){const C=u;ap(n,C.items,N)&&s(T,"DUPLICATE_KEY","Map keys must be unique"),C.items.push(M)}else{const C=new Re(n.schema);C.flow=!0,C.items.push(M);const A=(O??N).range;C.range=[N.range[0],A[1],A[2]],u.items.push(C)}f=O?O.range[2]:E.end}}const p=l?"}":"]",[g,...w]=r.end;let h=f;if(g&&g.source===p)h=g.offset+g.source.length;else{const v=o[0].toUpperCase()+o.substring(1),d=m?`${v} must end with a ${p}`:`${v} in block collection must be sufficiently indented and end with a ${p}`;s(f,m?"MISSING_CHAR":"BAD_INDENT",d),g&&g.source.length!==1&&w.unshift(g)}if(w.length>0){const v=vs(w,h,n.options.strict,s);v.comment&&(u.comment?u.comment+=`
`+v.comment:u.comment=v.comment),u.range=[r.offset,h,v.offset]}else u.range=[r.offset,h,h];return u}function Gl(e,t,n,r,s,i){const l=n.type==="block-map"?C0(e,t,n,r,i):n.type==="block-seq"?_0(e,t,n,r,i):L0(e,t,n,r,i),o=l.constructor;return s==="!"||s===o.tagName?(l.tag=o.tagName,l):(s&&(l.tag=s),l)}function O0(e,t,n,r,s){var p;const i=r.tag,l=i?t.directives.tagName(i.source,g=>s(i,"TAG_RESOLVE_FAILED",g)):null;if(n.type==="block-seq"){const{anchor:g,newlineAfterProp:w}=r,h=g&&i?g.offset>i.offset?g:i:g??i;h&&(!w||w.offset<h.offset)&&s(h,"MISSING_CHAR","Missing newline after block sequence props")}const o=n.type==="block-map"?"map":n.type==="block-seq"?"seq":n.start.source==="{"?"map":"seq";if(!i||!l||l==="!"||l===Re.tagName&&o==="map"||l===Kt.tagName&&o==="seq")return Gl(e,t,n,s,l);let a=t.schema.tags.find(g=>g.tag===l&&g.collection===o);if(!a){const g=t.schema.knownTags[l];if(g&&g.collection===o)t.schema.tags.push(Object.assign({},g,{default:!1})),a=g;else return g!=null&&g.collection?s(i,"BAD_COLLECTION_TYPE",`${g.tag} used for ${o} collection, but expects ${g.collection}`,!0):s(i,"TAG_RESOLVE_FAILED",`Unresolved tag: ${l}`,!0),Gl(e,t,n,s,l)}const u=Gl(e,t,n,s,l,a),m=((p=a.resolve)==null?void 0:p.call(a,u,g=>s(i,"TAG_RESOLVE_FAILED",g),t.options))??u,f=J(m)?m:new P(m);return f.range=u.range,f.tag=l,a!=null&&a.format&&(f.format=a.format),f}function up(e,t,n){const r=t.offset,s=$0(t,e.options.strict,n);if(!s)return{value:"",type:null,comment:"",range:[r,r,r]};const i=s.mode===">"?P.BLOCK_FOLDED:P.BLOCK_LITERAL,l=t.source?I0(t.source):[];let o=l.length;for(let h=l.length-1;h>=0;--h){const v=l[h][1];if(v===""||v==="\r")o=h;else break}if(o===0){const h=s.chomp==="+"&&l.length>0?`
`.repeat(Math.max(1,l.length-1)):"";let v=r+s.length;return t.source&&(v+=t.source.length),{value:h,type:i,comment:s.comment,range:[r,v,v]}}let a=t.indent+s.indent,u=t.offset+s.length,m=0;for(let h=0;h<o;++h){const[v,d]=l[h];if(d===""||d==="\r")s.indent===0&&v.length>a&&(a=v.length);else{v.length<a&&n(u+v.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),s.indent===0&&(a=v.length),m=h,a===0&&!e.atRoot&&n(u,"BAD_INDENT","Block scalar values in collections must be indented");break}u+=v.length+d.length+1}for(let h=l.length-1;h>=o;--h)l[h][0].length>a&&(o=h+1);let f="",p="",g=!1;for(let h=0;h<m;++h)f+=l[h][0].slice(a)+`
`;for(let h=m;h<o;++h){let[v,d]=l[h];u+=v.length+d.length+1;const c=d[d.length-1]==="\r";if(c&&(d=d.slice(0,-1)),d&&v.length<a){const S=`Block scalar lines must not be less indented than their ${s.indent?"explicit indentation indicator":"first line"}`;n(u-d.length-(c?2:1),"BAD_INDENT",S),v=""}i===P.BLOCK_LITERAL?(f+=p+v.slice(a)+d,p=`
`):v.length>a||d[0]==="	"?(p===" "?p=`
`:!g&&p===`
`&&(p=`

`),f+=p+v.slice(a)+d,p=`
`,g=!0):d===""?p===`
`?f+=`
`:p=`
`:(f+=p+d,p=" ",g=!1)}switch(s.chomp){case"-":break;case"+":for(let h=o;h<l.length;++h)f+=`
`+l[h][0].slice(a);f[f.length-1]!==`
`&&(f+=`
`);break;default:f+=`
`}const w=r+s.length+t.source.length;return{value:f,type:i,comment:s.comment,range:[r,w,w]}}function $0({offset:e,props:t},n,r){if(t[0].type!=="block-scalar-header")return r(t[0],"IMPOSSIBLE","Block scalar header not found"),null;const{source:s}=t[0],i=s[0];let l=0,o="",a=-1;for(let p=1;p<s.length;++p){const g=s[p];if(!o&&(g==="-"||g==="+"))o=g;else{const w=Number(g);!l&&w?l=w:a===-1&&(a=e+p)}}a!==-1&&r(a,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${s}`);let u=!1,m="",f=s.length;for(let p=1;p<t.length;++p){const g=t[p];switch(g.type){case"space":u=!0;case"newline":f+=g.source.length;break;case"comment":n&&!u&&r(g,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),f+=g.source.length,m=g.source.substring(1);break;case"error":r(g,"UNEXPECTED_TOKEN",g.message),f+=g.source.length;break;default:{const w=`Unexpected token in block scalar header: ${g.type}`;r(g,"UNEXPECTED_TOKEN",w);const h=g.source;h&&typeof h=="string"&&(f+=h.length)}}}return{mode:i,indent:l,chomp:o,comment:m,length:f}}function I0(e){const t=e.split(/\n( *)/),n=t[0],r=n.match(/^( *)/),i=[r!=null&&r[1]?[r[1],n.slice(r[1].length)]:["",n]];for(let l=1;l<t.length;l+=2)i.push([t[l],t[l+1]]);return i}function cp(e,t,n){const{offset:r,type:s,source:i,end:l}=e;let o,a;const u=(p,g,w)=>n(r+p,g,w);switch(s){case"scalar":o=P.PLAIN,a=A0(i,u);break;case"single-quoted-scalar":o=P.QUOTE_SINGLE,a=P0(i,u);break;case"double-quoted-scalar":o=P.QUOTE_DOUBLE,a=M0(i,u);break;default:return n(e,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${s}`),{value:"",type:null,comment:"",range:[r,r+i.length,r+i.length]}}const m=r+i.length,f=vs(l,m,t,n);return{value:a,type:o,comment:f.comment,range:[r,m,f.offset]}}function A0(e,t){let n="";switch(e[0]){case"	":n="a tab character";break;case",":n="flow indicator character ,";break;case"%":n="directive indicator character %";break;case"|":case">":{n=`block scalar indicator ${e[0]}`;break}case"@":case"`":{n=`reserved character ${e[0]}`;break}}return n&&t(0,"BAD_SCALAR_START",`Plain value cannot start with ${n}`),fp(e)}function P0(e,t){return(e[e.length-1]!=="'"||e.length===1)&&t(e.length,"MISSING_CHAR","Missing closing 'quote"),fp(e.slice(1,-1)).replace(/''/g,"'")}function fp(e){let t,n;try{t=new RegExp(`(.*?)(?<![ 	])[ 	]*\r?
`,"sy"),n=new RegExp(`[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?
`,"sy")}catch{t=/(.*?)[ \t]*\r?\n/sy,n=/[ \t]*(.*?)[ \t]*\r?\n/sy}let r=t.exec(e);if(!r)return e;let s=r[1],i=" ",l=t.lastIndex;for(n.lastIndex=l;r=n.exec(e);)r[1]===""?i===`
`?s+=i:i=`
`:(s+=i+r[1],i=" "),l=n.lastIndex;const o=/[ \t]*(.*)/sy;return o.lastIndex=l,r=o.exec(e),s+i+((r==null?void 0:r[1])??"")}function M0(e,t){let n="";for(let r=1;r<e.length-1;++r){const s=e[r];if(!(s==="\r"&&e[r+1]===`
`))if(s===`
`){const{fold:i,offset:l}=j0(e,r);n+=i,r=l}else if(s==="\\"){let i=e[++r];const l=R0[i];if(l)n+=l;else if(i===`
`)for(i=e[r+1];i===" "||i==="	";)i=e[++r+1];else if(i==="\r"&&e[r+1]===`
`)for(i=e[++r+1];i===" "||i==="	";)i=e[++r+1];else if(i==="x"||i==="u"||i==="U"){const o={x:2,u:4,U:8}[i];n+=D0(e,r+1,o,t),r+=o}else{const o=e.substr(r-1,2);t(r-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${o}`),n+=o}}else if(s===" "||s==="	"){const i=r;let l=e[r+1];for(;l===" "||l==="	";)l=e[++r+1];l!==`
`&&!(l==="\r"&&e[r+2]===`
`)&&(n+=r>i?e.slice(i,r+1):s)}else n+=s}return(e[e.length-1]!=='"'||e.length===1)&&t(e.length,"MISSING_CHAR",'Missing closing "quote'),n}function j0(e,t){let n="",r=e[t+1];for(;(r===" "||r==="	"||r===`
`||r==="\r")&&!(r==="\r"&&e[t+2]!==`
`);)r===`
`&&(n+=`
`),t+=1,r=e[t+1];return n||(n=" "),{fold:n,offset:t}}const R0={0:"\0",a:"\x07",b:"\b",e:"\x1B",f:"\f",n:`
`,r:"\r",t:"	",v:"\v",N:"",_:" ",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function D0(e,t,n,r){const s=e.substr(t,n),l=s.length===n&&/^[0-9a-fA-F]+$/.test(s)?parseInt(s,16):NaN;if(isNaN(l)){const o=e.substr(t-2,n+2);return r(t-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${o}`),o}return String.fromCodePoint(l)}function dp(e,t,n,r){const{value:s,type:i,comment:l,range:o}=t.type==="block-scalar"?up(e,t,r):cp(t,e.options.strict,r),a=n?e.directives.tagName(n.source,f=>r(n,"TAG_RESOLVE_FAILED",f)):null;let u;e.options.stringKeys&&e.atKey?u=e.schema[ft]:a?u=b0(e.schema,s,a,n,r):t.type==="scalar"?u=B0(e,s,t,r):u=e.schema[ft];let m;try{const f=u.resolve(s,p=>r(n??t,"TAG_RESOLVE_FAILED",p),e.options);m=z(f)?f:new P(f)}catch(f){const p=f instanceof Error?f.message:String(f);r(n??t,"TAG_RESOLVE_FAILED",p),m=new P(s)}return m.range=o,m.source=s,i&&(m.type=i),a&&(m.tag=a),u.format&&(m.format=u.format),l&&(m.comment=l),m}function b0(e,t,n,r,s){var o;if(n==="!")return e[ft];const i=[];for(const a of e.tags)if(!a.collection&&a.tag===n)if(a.default&&a.test)i.push(a);else return a;for(const a of i)if((o=a.test)!=null&&o.test(t))return a;const l=e.knownTags[n];return l&&!l.collection?(e.tags.push(Object.assign({},l,{default:!1,test:void 0})),l):(s(r,"TAG_RESOLVE_FAILED",`Unresolved tag: ${n}`,n!=="tag:yaml.org,2002:str"),e[ft])}function B0({atKey:e,directives:t,schema:n},r,s,i){const l=n.tags.find(o=>{var a;return(o.default===!0||e&&o.default==="key")&&((a=o.test)==null?void 0:a.test(r))})||n[ft];if(n.compat){const o=n.compat.find(a=>{var u;return a.default&&((u=a.test)==null?void 0:u.test(r))})??n[ft];if(l.tag!==o.tag){const a=t.tagString(l.tag),u=t.tagString(o.tag),m=`Value may be parsed as either ${a} or ${u}`;i(s,"TAG_RESOLVE_FAILED",m,!0)}}return l}function F0(e,t,n){if(t){n===null&&(n=t.length);for(let r=n-1;r>=0;--r){let s=t[r];switch(s.type){case"space":case"comment":case"newline":e-=s.source.length;continue}for(s=t[++r];(s==null?void 0:s.type)==="space";)e+=s.source.length,s=t[++r];break}}return e}const z0={composeNode:hp,composeEmptyNode:uu};function hp(e,t,n,r){const s=e.atKey,{spaceBefore:i,comment:l,anchor:o,tag:a}=n;let u,m=!0;switch(t.type){case"alias":u=U0(e,t,r),(o||a)&&r(t,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":u=dp(e,t,a,r),o&&(u.anchor=o.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":u=O0(z0,e,t,n,r),o&&(u.anchor=o.source.substring(1));break;default:{const f=t.type==="error"?t.message:`Unsupported token (type: ${t.type})`;r(t,"UNEXPECTED_TOKEN",f),u=uu(e,t.offset,void 0,null,n,r),m=!1}}return o&&u.anchor===""&&r(o,"BAD_ALIAS","Anchor cannot be an empty string"),s&&e.options.stringKeys&&(!z(u)||typeof u.value!="string"||u.tag&&u.tag!=="tag:yaml.org,2002:str")&&r(a??t,"NON_STRING_KEY","With stringKeys, all keys must be strings"),i&&(u.spaceBefore=!0),l&&(t.type==="scalar"&&t.source===""?u.comment=l:u.commentBefore=l),e.options.keepSourceTokens&&m&&(u.srcToken=t),u}function uu(e,t,n,r,{spaceBefore:s,comment:i,anchor:l,tag:o,end:a},u){const m={type:"scalar",offset:F0(t,n,r),indent:-1,source:""},f=dp(e,m,o,u);return l&&(f.anchor=l.source.substring(1),f.anchor===""&&u(l,"BAD_ALIAS","Anchor cannot be an empty string")),s&&(f.spaceBefore=!0),i&&(f.comment=i,f.range[2]=a),f}function U0({options:e},{offset:t,source:n,end:r},s){const i=new il(n.substring(1));i.source===""&&s(t,"BAD_ALIAS","Alias cannot be an empty string"),i.source.endsWith(":")&&s(t+n.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);const l=t+n.length,o=vs(r,l,e.strict,s);return i.range=[t,l,o.offset],o.comment&&(i.comment=o.comment),i}function V0(e,t,{offset:n,start:r,value:s,end:i},l){const o=Object.assign({_directives:t},e),a=new mr(void 0,o),u={atKey:!1,atRoot:!0,directives:a.directives,options:a.options,schema:a.schema},m=sr(r,{indicator:"doc-start",next:s??(i==null?void 0:i[0]),offset:n,onError:l,parentIndent:0,startOnNewline:!0});m.found&&(a.directives.docStart=!0,s&&(s.type==="block-map"||s.type==="block-seq")&&!m.hasNewline&&l(m.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),a.contents=s?hp(u,s,m,l):uu(u,m.end,r,null,m,l);const f=a.contents.range[2],p=vs(i,f,!1,l);return p.comment&&(a.comment=p.comment),a.range=[n,f,p.offset],a}function Lr(e){if(typeof e=="number")return[e,e+1];if(Array.isArray(e))return e.length===2?e:[e[0],e[1]];const{offset:t,source:n}=e;return[t,t+(typeof n=="string"?n.length:1)]}function bc(e){var s;let t="",n=!1,r=!1;for(let i=0;i<e.length;++i){const l=e[i];switch(l[0]){case"#":t+=(t===""?"":r?`

`:`
`)+(l.substring(1)||" "),n=!0,r=!1;break;case"%":((s=e[i+1])==null?void 0:s[0])!=="#"&&(i+=1),n=!1;break;default:n||(r=!0),n=!1}}return{comment:t,afterEmptyLine:r}}class cu{constructor(t={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(n,r,s,i)=>{const l=Lr(n);i?this.warnings.push(new op(l,r,s)):this.errors.push(new ln(l,r,s))},this.directives=new Ee({version:t.version||"1.2"}),this.options=t}decorate(t,n){const{comment:r,afterEmptyLine:s}=bc(this.prelude);if(r){const i=t.contents;if(n)t.comment=t.comment?`${t.comment}
${r}`:r;else if(s||t.directives.docStart||!i)t.commentBefore=r;else if(Q(i)&&!i.flow&&i.items.length>0){let l=i.items[0];H(l)&&(l=l.key);const o=l.commentBefore;l.commentBefore=o?`${r}
${o}`:r}else{const l=i.commentBefore;i.commentBefore=l?`${r}
${l}`:r}}n?(Array.prototype.push.apply(t.errors,this.errors),Array.prototype.push.apply(t.warnings,this.warnings)):(t.errors=this.errors,t.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:bc(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(t,n=!1,r=-1){for(const s of t)yield*this.next(s);yield*this.end(n,r)}*next(t){switch(t.type){case"directive":this.directives.add(t.source,(n,r,s)=>{const i=Lr(t);i[0]+=n,this.onError(i,"BAD_DIRECTIVE",r,s)}),this.prelude.push(t.source),this.atDirectives=!0;break;case"document":{const n=V0(this.options,this.directives,t,this.onError);this.atDirectives&&!n.directives.docStart&&this.onError(t,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(n,!1),this.doc&&(yield this.doc),this.doc=n,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(t.source);break;case"error":{const n=t.source?`${t.message}: ${JSON.stringify(t.source)}`:t.message,r=new ln(Lr(t),"UNEXPECTED_TOKEN",n);this.atDirectives||!this.doc?this.errors.push(r):this.doc.errors.push(r);break}case"doc-end":{if(!this.doc){const r="Unexpected doc-end without preceding document";this.errors.push(new ln(Lr(t),"UNEXPECTED_TOKEN",r));break}this.doc.directives.docEnd=!0;const n=vs(t.end,t.offset+t.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),n.comment){const r=this.doc.comment;this.doc.comment=r?`${r}
${n.comment}`:n.comment}this.doc.range[2]=n.offset;break}default:this.errors.push(new ln(Lr(t),"UNEXPECTED_TOKEN",`Unsupported token ${t.type}`))}}*end(t=!1,n=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(t){const r=Object.assign({_directives:this.directives},this.options),s=new mr(void 0,r);this.atDirectives&&this.onError(n,"MISSING_CHAR","Missing directives-end indicator line"),s.range=[0,n,n],this.decorate(s,!1),yield s}}}function K0(e,t=!0,n){if(e){const r=(s,i,l)=>{const o=typeof s=="number"?s:Array.isArray(s)?s[0]:s.offset;if(n)n(o,i,l);else throw new ln([o,o+1],i,l)};switch(e.type){case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return cp(e,t,r);case"block-scalar":return up({options:{strict:t}},e,r)}}return null}function W0(e,t){const{implicitKey:n=!1,indent:r,inFlow:s=!1,offset:i=-1,type:l="PLAIN"}=t,o=gs({type:l,value:e},{implicitKey:n,indent:r>0?" ".repeat(r):"",inFlow:s,options:{blockQuote:!0,lineWidth:-1}}),a=t.end??[{type:"newline",offset:-1,indent:r,source:`
`}];switch(o[0]){case"|":case">":{const u=o.indexOf(`
`),m=o.substring(0,u),f=o.substring(u+1)+`
`,p=[{type:"block-scalar-header",offset:i,indent:r,source:m}];return pp(p,a)||p.push({type:"newline",offset:-1,indent:r,source:`
`}),{type:"block-scalar",offset:i,indent:r,props:p,source:f}}case'"':return{type:"double-quoted-scalar",offset:i,indent:r,source:o,end:a};case"'":return{type:"single-quoted-scalar",offset:i,indent:r,source:o,end:a};default:return{type:"scalar",offset:i,indent:r,source:o,end:a}}}function H0(e,t,n={}){let{afterKey:r=!1,implicitKey:s=!1,inFlow:i=!1,type:l}=n,o="indent"in e?e.indent:null;if(r&&typeof o=="number"&&(o+=2),!l)switch(e.type){case"single-quoted-scalar":l="QUOTE_SINGLE";break;case"double-quoted-scalar":l="QUOTE_DOUBLE";break;case"block-scalar":{const u=e.props[0];if(u.type!=="block-scalar-header")throw new Error("Invalid block scalar header");l=u.source[0]===">"?"BLOCK_FOLDED":"BLOCK_LITERAL";break}default:l="PLAIN"}const a=gs({type:l,value:t},{implicitKey:s||o===null,indent:o!==null&&o>0?" ".repeat(o):"",inFlow:i,options:{blockQuote:!0,lineWidth:-1}});switch(a[0]){case"|":case">":q0(e,a);break;case'"':Xl(e,a,"double-quoted-scalar");break;case"'":Xl(e,a,"single-quoted-scalar");break;default:Xl(e,a,"scalar")}}function q0(e,t){const n=t.indexOf(`
`),r=t.substring(0,n),s=t.substring(n+1)+`
`;if(e.type==="block-scalar"){const i=e.props[0];if(i.type!=="block-scalar-header")throw new Error("Invalid block scalar header");i.source=r,e.source=s}else{const{offset:i}=e,l="indent"in e?e.indent:-1,o=[{type:"block-scalar-header",offset:i,indent:l,source:r}];pp(o,"end"in e?e.end:void 0)||o.push({type:"newline",offset:-1,indent:l,source:`
`});for(const a of Object.keys(e))a!=="type"&&a!=="offset"&&delete e[a];Object.assign(e,{type:"block-scalar",indent:l,props:o,source:s})}}function pp(e,t){if(t)for(const n of t)switch(n.type){case"space":case"comment":e.push(n);break;case"newline":return e.push(n),!0}return!1}function Xl(e,t,n){switch(e.type){case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":e.type=n,e.source=t;break;case"block-scalar":{const r=e.props.slice(1);let s=t.length;e.props[0].type==="block-scalar-header"&&(s-=e.props[0].source.length);for(const i of r)i.offset+=s;delete e.props,Object.assign(e,{type:n,source:t,end:r});break}case"block-map":case"block-seq":{const s={type:"newline",offset:e.offset+t.length,indent:e.indent,source:`
`};delete e.items,Object.assign(e,{type:n,source:t,end:[s]});break}default:{const r="indent"in e?e.indent:-1,s="end"in e&&Array.isArray(e.end)?e.end.filter(i=>i.type==="space"||i.type==="comment"||i.type==="newline"):[];for(const i of Object.keys(e))i!=="type"&&i!=="offset"&&delete e[i];Object.assign(e,{type:n,indent:r,source:t,end:s})}}}const Q0=e=>"type"in e?bi(e):ui(e);function bi(e){switch(e.type){case"block-scalar":{let t="";for(const n of e.props)t+=bi(n);return t+e.source}case"block-map":case"block-seq":{let t="";for(const n of e.items)t+=ui(n);return t}case"flow-collection":{let t=e.start.source;for(const n of e.items)t+=ui(n);for(const n of e.end)t+=n.source;return t}case"document":{let t=ui(e);if(e.end)for(const n of e.end)t+=n.source;return t}default:{let t=e.source;if("end"in e&&e.end)for(const n of e.end)t+=n.source;return t}}}function ui({start:e,key:t,sep:n,value:r}){let s="";for(const i of e)s+=i.source;if(t&&(s+=bi(t)),n)for(const i of n)s+=i.source;return r&&(s+=bi(r)),s}const ta=Symbol("break visit"),Y0=Symbol("skip children"),mp=Symbol("remove item");function mn(e,t){"type"in e&&e.type==="document"&&(e={start:e.start,value:e.value}),gp(Object.freeze([]),e,t)}mn.BREAK=ta;mn.SKIP=Y0;mn.REMOVE=mp;mn.itemAtPath=(e,t)=>{let n=e;for(const[r,s]of t){const i=n==null?void 0:n[r];if(i&&"items"in i)n=i.items[s];else return}return n};mn.parentCollection=(e,t)=>{const n=mn.itemAtPath(e,t.slice(0,-1)),r=t[t.length-1][0],s=n==null?void 0:n[r];if(s&&"items"in s)return s;throw new Error("Parent collection not found")};function gp(e,t,n){let r=n(t,e);if(typeof r=="symbol")return r;for(const s of["key","value"]){const i=t[s];if(i&&"items"in i){for(let l=0;l<i.items.length;++l){const o=gp(Object.freeze(e.concat([[s,l]])),i.items[l],n);if(typeof o=="number")l=o-1;else{if(o===ta)return ta;o===mp&&(i.items.splice(l,1),l-=1)}}typeof r=="function"&&s==="key"&&(r=r(t,e))}}return typeof r=="function"?r(t,e):r}const ml="\uFEFF",gl="",yl="",us="",J0=e=>!!e&&"items"in e,G0=e=>!!e&&(e.type==="scalar"||e.type==="single-quoted-scalar"||e.type==="double-quoted-scalar"||e.type==="block-scalar");function X0(e){switch(e){case ml:return"<BOM>";case gl:return"<DOC>";case yl:return"<FLOW_END>";case us:return"<SCALAR>";default:return JSON.stringify(e)}}function yp(e){switch(e){case ml:return"byte-order-mark";case gl:return"doc-mode";case yl:return"flow-error-end";case us:return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case`
`:case`\r
`:return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(e[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}const Z0=Object.freeze(Object.defineProperty({__proto__:null,BOM:ml,DOCUMENT:gl,FLOW_END:yl,SCALAR:us,createScalarToken:W0,isCollection:J0,isScalar:G0,prettyToken:X0,resolveAsScalar:K0,setScalarValue:H0,stringify:Q0,tokenType:yp,visit:mn},Symbol.toStringTag,{value:"Module"}));function Ge(e){switch(e){case void 0:case" ":case`
`:case"\r":case"	":return!0;default:return!1}}const Bc=new Set("0123456789ABCDEFabcdef"),e1=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),zs=new Set(",[]{}"),t1=new Set(` ,[]{}
\r	`),Zl=e=>!e||t1.has(e);class vp{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(t,n=!1){if(t){if(typeof t!="string")throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+t:t,this.lineEndPos=null}this.atEnd=!n;let r=this.next??"stream";for(;r&&(n||this.hasChars(1));)r=yield*this.parseNext(r)}atLineEnd(){let t=this.pos,n=this.buffer[t];for(;n===" "||n==="	";)n=this.buffer[++t];return!n||n==="#"||n===`
`?!0:n==="\r"?this.buffer[t+1]===`
`:!1}charAt(t){return this.buffer[this.pos+t]}continueScalar(t){let n=this.buffer[t];if(this.indentNext>0){let r=0;for(;n===" ";)n=this.buffer[++r+t];if(n==="\r"){const s=this.buffer[r+t+1];if(s===`
`||!s&&!this.atEnd)return t+r+1}return n===`
`||r>=this.indentNext||!n&&!this.atEnd?t+r:-1}if(n==="-"||n==="."){const r=this.buffer.substr(t,3);if((r==="---"||r==="...")&&Ge(this.buffer[t+3]))return-1}return t}getLine(){let t=this.lineEndPos;return(typeof t!="number"||t!==-1&&t<this.pos)&&(t=this.buffer.indexOf(`
`,this.pos),this.lineEndPos=t),t===-1?this.atEnd?this.buffer.substring(this.pos):null:(this.buffer[t-1]==="\r"&&(t-=1),this.buffer.substring(this.pos,t))}hasChars(t){return this.pos+t<=this.buffer.length}setNext(t){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=t,null}peek(t){return this.buffer.substr(this.pos,t)}*parseNext(t){switch(t){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let t=this.getLine();if(t===null)return this.setNext("stream");if(t[0]===ml&&(yield*this.pushCount(1),t=t.substring(1)),t[0]==="%"){let n=t.length,r=t.indexOf("#");for(;r!==-1;){const i=t[r-1];if(i===" "||i==="	"){n=r-1;break}else r=t.indexOf("#",r+1)}for(;;){const i=t[n-1];if(i===" "||i==="	")n-=1;else break}const s=(yield*this.pushCount(n))+(yield*this.pushSpaces(!0));return yield*this.pushCount(t.length-s),this.pushNewline(),"stream"}if(this.atLineEnd()){const n=yield*this.pushSpaces(!0);return yield*this.pushCount(t.length-n),yield*this.pushNewline(),"stream"}return yield gl,yield*this.parseLineStart()}*parseLineStart(){const t=this.charAt(0);if(!t&&!this.atEnd)return this.setNext("line-start");if(t==="-"||t==="."){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");const n=this.peek(3);if((n==="---"||n==="...")&&Ge(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,n==="---"?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!Ge(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){const[t,n]=this.peek(2);if(!n&&!this.atEnd)return this.setNext("block-start");if((t==="-"||t==="?"||t===":")&&Ge(n)){const r=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=r,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);const t=this.getLine();if(t===null)return this.setNext("doc");let n=yield*this.pushIndicators();switch(t[n]){case"#":yield*this.pushCount(t.length-n);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(Zl),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return n+=yield*this.parseBlockScalarHeader(),n+=yield*this.pushSpaces(!0),yield*this.pushCount(t.length-n),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let t,n,r=-1;do t=yield*this.pushNewline(),t>0?(n=yield*this.pushSpaces(!1),this.indentValue=r=n):n=0,n+=yield*this.pushSpaces(!0);while(t+n>0);const s=this.getLine();if(s===null)return this.setNext("flow");if((r!==-1&&r<this.indentNext&&s[0]!=="#"||r===0&&(s.startsWith("---")||s.startsWith("..."))&&Ge(s[3]))&&!(r===this.indentNext-1&&this.flowLevel===1&&(s[0]==="]"||s[0]==="}")))return this.flowLevel=0,yield yl,yield*this.parseLineStart();let i=0;for(;s[i]===",";)i+=yield*this.pushCount(1),i+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(i+=yield*this.pushIndicators(),s[i]){case void 0:return"flow";case"#":return yield*this.pushCount(s.length-i),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(Zl),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{const l=this.charAt(1);if(this.flowKey||Ge(l)||l===",")return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){const t=this.charAt(0);let n=this.buffer.indexOf(t,this.pos+1);if(t==="'")for(;n!==-1&&this.buffer[n+1]==="'";)n=this.buffer.indexOf("'",n+2);else for(;n!==-1;){let i=0;for(;this.buffer[n-1-i]==="\\";)i+=1;if(i%2===0)break;n=this.buffer.indexOf('"',n+1)}const r=this.buffer.substring(0,n);let s=r.indexOf(`
`,this.pos);if(s!==-1){for(;s!==-1;){const i=this.continueScalar(s+1);if(i===-1)break;s=r.indexOf(`
`,i)}s!==-1&&(n=s-(r[s-1]==="\r"?2:1))}if(n===-1){if(!this.atEnd)return this.setNext("quoted-scalar");n=this.buffer.length}return yield*this.pushToIndex(n+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let t=this.pos;for(;;){const n=this.buffer[++t];if(n==="+")this.blockScalarKeep=!0;else if(n>"0"&&n<="9")this.blockScalarIndent=Number(n)-1;else if(n!=="-")break}return yield*this.pushUntil(n=>Ge(n)||n==="#")}*parseBlockScalar(){let t=this.pos-1,n=0,r;e:for(let i=this.pos;r=this.buffer[i];++i)switch(r){case" ":n+=1;break;case`
`:t=i,n=0;break;case"\r":{const l=this.buffer[i+1];if(!l&&!this.atEnd)return this.setNext("block-scalar");if(l===`
`)break}default:break e}if(!r&&!this.atEnd)return this.setNext("block-scalar");if(n>=this.indentNext){this.blockScalarIndent===-1?this.indentNext=n:this.indentNext=this.blockScalarIndent+(this.indentNext===0?1:this.indentNext);do{const i=this.continueScalar(t+1);if(i===-1)break;t=this.buffer.indexOf(`
`,i)}while(t!==-1);if(t===-1){if(!this.atEnd)return this.setNext("block-scalar");t=this.buffer.length}}let s=t+1;for(r=this.buffer[s];r===" ";)r=this.buffer[++s];if(r==="	"){for(;r==="	"||r===" "||r==="\r"||r===`
`;)r=this.buffer[++s];t=s-1}else if(!this.blockScalarKeep)do{let i=t-1,l=this.buffer[i];l==="\r"&&(l=this.buffer[--i]);const o=i;for(;l===" ";)l=this.buffer[--i];if(l===`
`&&i>=this.pos&&i+1+n>o)t=i;else break}while(!0);return yield us,yield*this.pushToIndex(t+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){const t=this.flowLevel>0;let n=this.pos-1,r=this.pos-1,s;for(;s=this.buffer[++r];)if(s===":"){const i=this.buffer[r+1];if(Ge(i)||t&&zs.has(i))break;n=r}else if(Ge(s)){let i=this.buffer[r+1];if(s==="\r"&&(i===`
`?(r+=1,s=`
`,i=this.buffer[r+1]):n=r),i==="#"||t&&zs.has(i))break;if(s===`
`){const l=this.continueScalar(r+1);if(l===-1)break;r=Math.max(r,l-2)}}else{if(t&&zs.has(s))break;n=r}return!s&&!this.atEnd?this.setNext("plain-scalar"):(yield us,yield*this.pushToIndex(n+1,!0),t?"flow":"doc")}*pushCount(t){return t>0?(yield this.buffer.substr(this.pos,t),this.pos+=t,t):0}*pushToIndex(t,n){const r=this.buffer.slice(this.pos,t);return r?(yield r,this.pos+=r.length,r.length):(n&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(Zl))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{const t=this.flowLevel>0,n=this.charAt(1);if(Ge(n)||t&&zs.has(n))return t?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if(this.charAt(1)==="<"){let t=this.pos+2,n=this.buffer[t];for(;!Ge(n)&&n!==">";)n=this.buffer[++t];return yield*this.pushToIndex(n===">"?t+1:t,!1)}else{let t=this.pos+1,n=this.buffer[t];for(;n;)if(e1.has(n))n=this.buffer[++t];else if(n==="%"&&Bc.has(this.buffer[t+1])&&Bc.has(this.buffer[t+2]))n=this.buffer[t+=3];else break;return yield*this.pushToIndex(t,!1)}}*pushNewline(){const t=this.buffer[this.pos];return t===`
`?yield*this.pushCount(1):t==="\r"&&this.charAt(1)===`
`?yield*this.pushCount(2):0}*pushSpaces(t){let n=this.pos-1,r;do r=this.buffer[++n];while(r===" "||t&&r==="	");const s=n-this.pos;return s>0&&(yield this.buffer.substr(this.pos,s),this.pos=n),s}*pushUntil(t){let n=this.pos,r=this.buffer[n];for(;!t(r);)r=this.buffer[++n];return yield*this.pushToIndex(n,!1)}}class wp{constructor(){this.lineStarts=[],this.addNewLine=t=>this.lineStarts.push(t),this.linePos=t=>{let n=0,r=this.lineStarts.length;for(;n<r;){const i=n+r>>1;this.lineStarts[i]<t?n=i+1:r=i}if(this.lineStarts[n]===t)return{line:n+1,col:1};if(n===0)return{line:0,col:t};const s=this.lineStarts[n-1];return{line:n,col:t-s+1}}}}function Zt(e,t){for(let n=0;n<e.length;++n)if(e[n].type===t)return!0;return!1}function Fc(e){for(let t=0;t<e.length;++t)switch(e[t].type){case"space":case"comment":case"newline":break;default:return t}return-1}function Sp(e){switch(e==null?void 0:e.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Us(e){switch(e.type){case"document":return e.start;case"block-map":{const t=e.items[e.items.length-1];return t.sep??t.start}case"block-seq":return e.items[e.items.length-1].start;default:return[]}}function En(e){var n;if(e.length===0)return[];let t=e.length;e:for(;--t>=0;)switch(e[t].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;((n=e[++t])==null?void 0:n.type)==="space";);return e.splice(t,e.length)}function zc(e){if(e.start.type==="flow-seq-start")for(const t of e.items)t.sep&&!t.value&&!Zt(t.start,"explicit-key-ind")&&!Zt(t.sep,"map-value-ind")&&(t.key&&(t.value=t.key),delete t.key,Sp(t.value)?t.value.end?Array.prototype.push.apply(t.value.end,t.sep):t.value.end=t.sep:Array.prototype.push.apply(t.start,t.sep),delete t.sep)}class fu{constructor(t){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new vp,this.onNewLine=t}*parse(t,n=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(const r of this.lexer.lex(t,n))yield*this.next(r);n||(yield*this.end())}*next(t){if(this.source=t,this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=t.length;return}const n=yp(t);if(n)if(n==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=n,yield*this.step(),n){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+t.length);break;case"space":this.atNewLine&&t[0]===" "&&(this.indent+=t.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=t.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=t.length}else{const r=`Not a YAML token: ${t}`;yield*this.pop({type:"error",offset:this.offset,message:r,source:t}),this.offset+=t.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){const t=this.peek(1);if(this.type==="doc-end"&&(!t||t.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!t)return yield*this.stream();switch(t.type){case"document":return yield*this.document(t);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(t);case"block-scalar":return yield*this.blockScalar(t);case"block-map":return yield*this.blockMap(t);case"block-seq":return yield*this.blockSequence(t);case"flow-collection":return yield*this.flowCollection(t);case"doc-end":return yield*this.documentEnd(t)}yield*this.pop()}peek(t){return this.stack[this.stack.length-t]}*pop(t){const n=t??this.stack.pop();if(!n)yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"};else if(this.stack.length===0)yield n;else{const r=this.peek(1);switch(n.type==="block-scalar"?n.indent="indent"in r?r.indent:0:n.type==="flow-collection"&&r.type==="document"&&(n.indent=0),n.type==="flow-collection"&&zc(n),r.type){case"document":r.value=n;break;case"block-scalar":r.props.push(n);break;case"block-map":{const s=r.items[r.items.length-1];if(s.value){r.items.push({start:[],key:n,sep:[]}),this.onKeyLine=!0;return}else if(s.sep)s.value=n;else{Object.assign(s,{key:n,sep:[]}),this.onKeyLine=!s.explicitKey;return}break}case"block-seq":{const s=r.items[r.items.length-1];s.value?r.items.push({start:[],value:n}):s.value=n;break}case"flow-collection":{const s=r.items[r.items.length-1];!s||s.value?r.items.push({start:[],key:n,sep:[]}):s.sep?s.value=n:Object.assign(s,{key:n,sep:[]});return}default:yield*this.pop(),yield*this.pop(n)}if((r.type==="document"||r.type==="block-map"||r.type==="block-seq")&&(n.type==="block-map"||n.type==="block-seq")){const s=n.items[n.items.length-1];s&&!s.sep&&!s.value&&s.start.length>0&&Fc(s.start)===-1&&(n.indent===0||s.start.every(i=>i.type!=="comment"||i.indent<n.indent))&&(r.type==="document"?r.end=s.start:r.items.push({start:s.start}),n.items.splice(-1,1))}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{const t={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&t.start.push(this.sourceToken),this.stack.push(t);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(t){if(t.value)return yield*this.lineEnd(t);switch(this.type){case"doc-start":{Fc(t.start)!==-1?(yield*this.pop(),yield*this.step()):t.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":t.start.push(this.sourceToken);return}const n=this.startBlockValue(t);n?this.stack.push(n):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(t){if(this.type==="map-value-ind"){const n=Us(this.peek(2)),r=En(n);let s;t.end?(s=t.end,s.push(this.sourceToken),delete t.end):s=[this.sourceToken];const i={type:"block-map",offset:t.offset,indent:t.indent,items:[{start:r,key:t,sep:s}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=i}else yield*this.lineEnd(t)}*blockScalar(t){switch(this.type){case"space":case"comment":case"newline":t.props.push(this.sourceToken);return;case"scalar":if(t.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let n=this.source.indexOf(`
`)+1;for(;n!==0;)this.onNewLine(this.offset+n),n=this.source.indexOf(`
`,n)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(t){var r;const n=t.items[t.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,n.value){const s="end"in n.value?n.value.end:void 0,i=Array.isArray(s)?s[s.length-1]:void 0;(i==null?void 0:i.type)==="comment"?s==null||s.push(this.sourceToken):t.items.push({start:[this.sourceToken]})}else n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)t.items.push({start:[this.sourceToken]});else if(n.sep)n.sep.push(this.sourceToken);else{if(this.atIndentedComment(n.start,t.indent)){const s=t.items[t.items.length-2],i=(r=s==null?void 0:s.value)==null?void 0:r.end;if(Array.isArray(i)){Array.prototype.push.apply(i,n.start),i.push(this.sourceToken),t.items.pop();return}}n.start.push(this.sourceToken)}return}if(this.indent>=t.indent){const s=!this.onKeyLine&&this.indent===t.indent,i=s&&(n.sep||n.explicitKey)&&this.type!=="seq-item-ind";let l=[];if(i&&n.sep&&!n.value){const o=[];for(let a=0;a<n.sep.length;++a){const u=n.sep[a];switch(u.type){case"newline":o.push(a);break;case"space":break;case"comment":u.indent>t.indent&&(o.length=0);break;default:o.length=0}}o.length>=2&&(l=n.sep.splice(o[1]))}switch(this.type){case"anchor":case"tag":i||n.value?(l.push(this.sourceToken),t.items.push({start:l}),this.onKeyLine=!0):n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken);return;case"explicit-key-ind":!n.sep&&!n.explicitKey?(n.start.push(this.sourceToken),n.explicitKey=!0):i||n.value?(l.push(this.sourceToken),t.items.push({start:l,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(n.explicitKey)if(n.sep)if(n.value)t.items.push({start:[],key:null,sep:[this.sourceToken]});else if(Zt(n.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:l,key:null,sep:[this.sourceToken]}]});else if(Sp(n.key)&&!Zt(n.sep,"newline")){const o=En(n.start),a=n.key,u=n.sep;u.push(this.sourceToken),delete n.key,delete n.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:a,sep:u}]})}else l.length>0?n.sep=n.sep.concat(l,this.sourceToken):n.sep.push(this.sourceToken);else if(Zt(n.start,"newline"))Object.assign(n,{key:null,sep:[this.sourceToken]});else{const o=En(n.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:null,sep:[this.sourceToken]}]})}else n.sep?n.value||i?t.items.push({start:l,key:null,sep:[this.sourceToken]}):Zt(n.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):n.sep.push(this.sourceToken):Object.assign(n,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const o=this.flowScalar(this.type);i||n.value?(t.items.push({start:l,key:o,sep:[]}),this.onKeyLine=!0):n.sep?this.stack.push(o):(Object.assign(n,{key:o,sep:[]}),this.onKeyLine=!0);return}default:{const o=this.startBlockValue(t);if(o){s&&o.type!=="block-seq"&&t.items.push({start:l}),this.stack.push(o);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(t){var r;const n=t.items[t.items.length-1];switch(this.type){case"newline":if(n.value){const s="end"in n.value?n.value.end:void 0,i=Array.isArray(s)?s[s.length-1]:void 0;(i==null?void 0:i.type)==="comment"?s==null||s.push(this.sourceToken):t.items.push({start:[this.sourceToken]})}else n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)t.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(n.start,t.indent)){const s=t.items[t.items.length-2],i=(r=s==null?void 0:s.value)==null?void 0:r.end;if(Array.isArray(i)){Array.prototype.push.apply(i,n.start),i.push(this.sourceToken),t.items.pop();return}}n.start.push(this.sourceToken)}return;case"anchor":case"tag":if(n.value||this.indent<=t.indent)break;n.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==t.indent)break;n.value||Zt(n.start,"seq-item-ind")?t.items.push({start:[this.sourceToken]}):n.start.push(this.sourceToken);return}if(this.indent>t.indent){const s=this.startBlockValue(t);if(s){this.stack.push(s);return}}yield*this.pop(),yield*this.step()}*flowCollection(t){const n=t.items[t.items.length-1];if(this.type==="flow-error-end"){let r;do yield*this.pop(),r=this.peek(1);while(r&&r.type==="flow-collection")}else if(t.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!n||n.sep?t.items.push({start:[this.sourceToken]}):n.start.push(this.sourceToken);return;case"map-value-ind":!n||n.value?t.items.push({start:[],key:null,sep:[this.sourceToken]}):n.sep?n.sep.push(this.sourceToken):Object.assign(n,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!n||n.value?t.items.push({start:[this.sourceToken]}):n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const s=this.flowScalar(this.type);!n||n.value?t.items.push({start:[],key:s,sep:[]}):n.sep?this.stack.push(s):Object.assign(n,{key:s,sep:[]});return}case"flow-map-end":case"flow-seq-end":t.end.push(this.sourceToken);return}const r=this.startBlockValue(t);r?this.stack.push(r):(yield*this.pop(),yield*this.step())}else{const r=this.peek(2);if(r.type==="block-map"&&(this.type==="map-value-ind"&&r.indent===t.indent||this.type==="newline"&&!r.items[r.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&r.type!=="flow-collection"){const s=Us(r),i=En(s);zc(t);const l=t.end.splice(1,t.end.length);l.push(this.sourceToken);const o={type:"block-map",offset:t.offset,indent:t.indent,items:[{start:i,key:t,sep:l}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=o}else yield*this.lineEnd(t)}}flowScalar(t){if(this.onNewLine){let n=this.source.indexOf(`
`)+1;for(;n!==0;)this.onNewLine(this.offset+n),n=this.source.indexOf(`
`,n)+1}return{type:t,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(t){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;const n=Us(t),r=En(n);return r.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;const n=Us(t),r=En(n);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(t,n){return this.type!=="comment"||this.indent<=n?!1:t.every(r=>r.type==="newline"||r.type==="space")}*documentEnd(t){this.type!=="doc-mode"&&(t.end?t.end.push(this.sourceToken):t.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(t){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:t.end?t.end.push(this.sourceToken):t.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}}function kp(e){const t=e.prettyErrors!==!1;return{lineCounter:e.lineCounter||t&&new wp||null,prettyErrors:t}}function n1(e,t={}){const{lineCounter:n,prettyErrors:r}=kp(t),s=new fu(n==null?void 0:n.addNewLine),i=new cu(t),l=Array.from(i.compose(s.parse(e)));if(r&&n)for(const o of l)o.errors.forEach(Di(e,n)),o.warnings.forEach(Di(e,n));return l.length>0?l:Object.assign([],{empty:!0},i.streamInfo())}function Ep(e,t={}){const{lineCounter:n,prettyErrors:r}=kp(t),s=new fu(n==null?void 0:n.addNewLine),i=new cu(t);let l=null;for(const o of i.compose(s.parse(e),!0,e.length))if(!l)l=o;else if(l.options.logLevel!=="silent"){l.errors.push(new ln(o.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return r&&n&&(l.errors.forEach(Di(e,n)),l.warnings.forEach(Di(e,n))),l}function r1(e,t,n){let r;typeof t=="function"?r=t:n===void 0&&t&&typeof t=="object"&&(n=t);const s=Ep(e,n);if(!s)return null;if(s.warnings.forEach(i=>Uh(s.options.logLevel,i)),s.errors.length>0){if(s.options.logLevel!=="silent")throw s.errors[0];s.errors=[]}return s.toJS(Object.assign({reviver:r},n))}function s1(e,t,n){let r=null;if(typeof t=="function"||Array.isArray(t)?r=t:n===void 0&&t&&(n=t),typeof n=="string"&&(n=n.length),typeof n=="number"){const s=Math.round(n);n=s<1?void 0:s>8?{indent:8}:{indent:s}}if(e===void 0){const{keepUndefined:s}=n??t??{};if(!s)return}return wn(e)&&!r?e.toString(n):new mr(e,r,n).toString(n)}const i1=Object.freeze(Object.defineProperty({__proto__:null,Alias:il,CST:Z0,Composer:cu,Document:mr,Lexer:vp,LineCounter:wp,Pair:we,Parser:fu,Scalar:P,Schema:pl,YAMLError:au,YAMLMap:Re,YAMLParseError:ln,YAMLSeq:Kt,YAMLWarning:op,isAlias:vn,isCollection:Q,isDocument:wn,isMap:fr,isNode:J,isPair:H,isScalar:z,isSeq:dr,parse:r1,parseAllDocuments:n1,parseDocument:Ep,stringify:s1,visit:Vt,visitAsync:sl},Symbol.toStringTag,{value:"Module"}));function l1(e,t,n={}){var p;const r=new e.LineCounter,s={keepSourceTokens:!0,lineCounter:r,...n},i=e.parseDocument(t,s),l=[],o=g=>[r.linePos(g[0]),r.linePos(g[1])],a=g=>{l.push({message:g.message,range:[r.linePos(g.pos[0]),r.linePos(g.pos[1])]})},u=(g,w)=>{for(const h of w.items){if(h instanceof e.Scalar&&typeof h.value=="string"){const c=Bi.parse(h,s,l);c&&(g.children=g.children||[],g.children.push(c));continue}if(h instanceof e.YAMLMap){m(g,h);continue}l.push({message:"Sequence items should be strings or maps",range:o(h.range||w.range)})}},m=(g,w)=>{for(const h of w.items){if(g.children=g.children||[],!(h.key instanceof e.Scalar&&typeof h.key.value=="string")){l.push({message:"Only string keys are supported",range:o(h.key.range||w.range)});continue}const d=h.key,c=h.value;if(d.value==="text"){if(!(c instanceof e.Scalar&&typeof c.value=="string")){l.push({message:"Text value should be a string",range:o(h.value.range||w.range)});continue}g.children.push({kind:"text",text:Uc(c.value)});continue}const y=Bi.parse(d,s,l);if(!y)continue;if(c instanceof e.Scalar){const k=typeof c.value;if(k!=="string"&&k!=="number"&&k!=="boolean"){l.push({message:"Node value should be a string or a sequence",range:o(h.value.range||w.range)});continue}g.children.push({...y,children:[{kind:"text",text:Uc(String(c.value))}]});continue}if(c instanceof e.YAMLSeq){g.children.push(y),u(y,c);continue}l.push({message:"Map values should be strings or sequences",range:o(h.value.range||w.range)})}},f={kind:"role",role:"fragment"};return i.errors.forEach(a),l.length?{errors:l,fragment:f}:(i.contents instanceof e.YAMLSeq||l.push({message:'Aria snapshot must be a YAML sequence, elements starting with " -"',range:i.contents?o(i.contents.range):[{line:0,col:0},{line:0,col:0}]}),l.length?{errors:l,fragment:f}:(u(f,i.contents),l.length?{errors:l,fragment:o1}:((p=f.children)==null?void 0:p.length)===1?{fragment:f.children[0],errors:l}:{fragment:f,errors:l}))}const o1={kind:"role",role:"fragment"};function Np(e){return e.replace(/[\r\n\s\t]+/g," ").trim()}function Uc(e){return e.startsWith("/")&&e.endsWith("/")&&e.length>1?{pattern:e.slice(1,-1)}:Np(e)}class Bi{static parse(t,n,r){try{return new Bi(t.value)._parse()}catch(s){if(s instanceof Vc){const i=n.prettyErrors===!1?s.message:s.message+`:

`+t.value+`
`+" ".repeat(s.pos)+`^
`;return r.push({message:i,range:[n.lineCounter.linePos(t.range[0]),n.lineCounter.linePos(t.range[0]+s.pos)]}),null}throw s}}constructor(t){this._input=t,this._pos=0,this._length=t.length}_peek(){return this._input[this._pos]||""}_next(){return this._pos<this._length?this._input[this._pos++]:null}_eof(){return this._pos>=this._length}_isWhitespace(){return!this._eof()&&/\s/.test(this._peek())}_skipWhitespace(){for(;this._isWhitespace();)this._pos++}_readIdentifier(t){this._eof()&&this._throwError(`Unexpected end of input when expecting ${t}`);const n=this._pos;for(;!this._eof()&&/[a-zA-Z]/.test(this._peek());)this._pos++;return this._input.slice(n,this._pos)}_readString(){let t="",n=!1;for(;!this._eof();){const r=this._next();if(n)t+=r,n=!1;else if(r==="\\")n=!0;else{if(r==='"')return t;t+=r}}this._throwError("Unterminated string")}_throwError(t,n=0){throw new Vc(t,n||this._pos)}_readRegex(){let t="",n=!1,r=!1;for(;!this._eof();){const s=this._next();if(n)t+=s,n=!1;else if(s==="\\")n=!0,t+=s;else{if(s==="/"&&!r)return{pattern:t};s==="["?(r=!0,t+=s):s==="]"&&r?(t+=s,r=!1):t+=s}}this._throwError("Unterminated regex")}_readStringOrRegex(){const t=this._peek();return t==='"'?(this._next(),Np(this._readString())):t==="/"?(this._next(),this._readRegex()):null}_readAttributes(t){let n=this._pos;for(;this._skipWhitespace(),this._peek()==="[";){this._next(),this._skipWhitespace(),n=this._pos;const r=this._readIdentifier("attribute");this._skipWhitespace();let s="";if(this._peek()==="=")for(this._next(),this._skipWhitespace(),n=this._pos;this._peek()!=="]"&&!this._isWhitespace()&&!this._eof();)s+=this._next();this._skipWhitespace(),this._peek()!=="]"&&this._throwError("Expected ]"),this._next(),this._applyAttribute(t,r,s||"true",n)}}_parse(){this._skipWhitespace();const t=this._readIdentifier("role");this._skipWhitespace();const n=this._readStringOrRegex()||"",r={kind:"role",role:t,name:n};return this._readAttributes(r),this._skipWhitespace(),this._eof()||this._throwError("Unexpected input"),r}_applyAttribute(t,n,r,s){if(n==="checked"){this._assert(r==="true"||r==="false"||r==="mixed",'Value of "checked" attribute must be a boolean or "mixed"',s),t.checked=r==="true"?!0:r==="false"?!1:"mixed";return}if(n==="disabled"){this._assert(r==="true"||r==="false",'Value of "disabled" attribute must be a boolean',s),t.disabled=r==="true";return}if(n==="expanded"){this._assert(r==="true"||r==="false",'Value of "expanded" attribute must be a boolean',s),t.expanded=r==="true";return}if(n==="level"){this._assert(!isNaN(Number(r)),'Value of "level" attribute must be a number',s),t.level=Number(r);return}if(n==="pressed"){this._assert(r==="true"||r==="false"||r==="mixed",'Value of "pressed" attribute must be a boolean or "mixed"',s),t.pressed=r==="true"?!0:r==="false"?!1:"mixed";return}if(n==="selected"){this._assert(r==="true"||r==="false",'Value of "selected" attribute must be a boolean',s),t.selected=r==="true";return}this._assert(!1,`Unsupported attribute [${n}]`,s)}_assert(t,n,r){t||this._throwError(n||"Assertion error",r)}}class Vc extends Error{constructor(t,n){super(t),this.pos=n}}const a1=({sources:e,paused:t,log:n,mode:r})=>{var x;const[s,i]=B.useState(),[l,o]=B.useState(),[a,u]=eo("recorderPropertiesTab","log"),[m,f]=B.useState(),[p,g]=B.useState(),w=s||l||((x=e[0])==null?void 0:x.id),h=B.useMemo(()=>{if(w){const k=e.find(T=>T.id===w);if(k)return k}return wy()},[e,w]),[v,d]=B.useState("");window.playwrightElementPicked=(k,T)=>{const N=h.language;d(Oh(N,k.selector)),f(k.ariaSnapshot),g([]),T&&a!=="locator"&&a!=="aria"&&u("locator"),r==="inspecting"&&a==="aria"||window.dispatch({event:"setMode",params:{mode:r==="inspecting"?"standby":"recording"}}).catch(()=>{})},window.playwrightSetRunningFile=o;const c=B.useRef(null);B.useLayoutEffect(()=>{var k;(k=c.current)==null||k.scrollIntoView({block:"center",inline:"nearest"})},[c]),B.useEffect(()=>{const k=T=>{switch(T.key){case"F8":T.preventDefault(),t?window.dispatch({event:"resume"}):window.dispatch({event:"pause"});break;case"F10":T.preventDefault(),t&&window.dispatch({event:"step"});break}};return document.addEventListener("keydown",k),()=>document.removeEventListener("keydown",k)},[t]);const y=B.useCallback(k=>{(r==="none"||r==="inspecting")&&window.dispatch({event:"setMode",params:{mode:"standby"}}),d(k),window.dispatch({event:"highlightRequested",params:{selector:k}})},[r]),S=B.useCallback(k=>{(r==="none"||r==="inspecting")&&window.dispatch({event:"setMode",params:{mode:"standby"}});const{fragment:T,errors:N}=l1(i1,k,{prettyErrors:!1}),E=N.map(O=>({message:O.message,line:O.range[1].line,column:O.range[1].col,type:"subtle-error"}));g(E),f(k),N.length||window.dispatch({event:"highlightRequested",params:{ariaTemplate:T}})},[r]);return L.jsxs("div",{className:"recorder",children:[L.jsxs(lh,{children:[L.jsx(Me,{icon:"circle-large-filled",title:"Record",toggled:r==="recording"||r==="recording-inspecting"||r==="assertingText"||r==="assertingVisibility",onClick:()=>{window.dispatch({event:"setMode",params:{mode:r==="none"||r==="standby"||r==="inspecting"?"recording":"standby"}})},children:"Record"}),L.jsx(_c,{}),L.jsx(Me,{icon:"inspect",title:"Pick locator",toggled:r==="inspecting"||r==="recording-inspecting",onClick:()=>{const k={inspecting:"standby",none:"inspecting",standby:"inspecting",recording:"recording-inspecting","recording-inspecting":"recording",assertingText:"recording-inspecting",assertingVisibility:"recording-inspecting",assertingValue:"recording-inspecting",assertingSnapshot:"recording-inspecting"}[r];window.dispatch({event:"setMode",params:{mode:k}}).catch(()=>{})}}),L.jsx(Me,{icon:"eye",title:"Assert visibility",toggled:r==="assertingVisibility",disabled:r==="none"||r==="standby"||r==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:r==="assertingVisibility"?"recording":"assertingVisibility"}})}}),L.jsx(Me,{icon:"whole-word",title:"Assert text",toggled:r==="assertingText",disabled:r==="none"||r==="standby"||r==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:r==="assertingText"?"recording":"assertingText"}})}}),L.jsx(Me,{icon:"symbol-constant",title:"Assert value",toggled:r==="assertingValue",disabled:r==="none"||r==="standby"||r==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:r==="assertingValue"?"recording":"assertingValue"}})}}),L.jsx(Me,{icon:"gist",title:"Assert snapshot",toggled:r==="assertingSnapshot",disabled:r==="none"||r==="standby"||r==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:r==="assertingSnapshot"?"recording":"assertingSnapshot"}})}}),L.jsx(_c,{}),L.jsx(Me,{icon:"files",title:"Copy",disabled:!h||!h.text,onClick:()=>{mu(h.text)}}),L.jsx(Me,{icon:"debug-continue",title:"Resume (F8)",ariaLabel:"Resume",disabled:!t,onClick:()=>{window.dispatch({event:"resume"})}}),L.jsx(Me,{icon:"debug-pause",title:"Pause (F8)",ariaLabel:"Pause",disabled:t,onClick:()=>{window.dispatch({event:"pause"})}}),L.jsx(Me,{icon:"debug-step-over",title:"Step over (F10)",ariaLabel:"Step over",disabled:!t,onClick:()=>{window.dispatch({event:"step"})}}),L.jsx("div",{style:{flex:"auto"}}),L.jsx("div",{children:"Target:"}),L.jsx(yy,{fileId:w,sources:e,setFileId:k=>{i(k),window.dispatch({event:"fileChanged",params:{file:k}})}}),L.jsx(Me,{icon:"clear-all",title:"Clear",disabled:!h||!h.text,onClick:()=>{window.dispatch({event:"clear"})}}),L.jsx(Me,{icon:"color-mode",title:"Toggle color mode",toggled:!1,onClick:()=>Gp()})]}),L.jsx(py,{sidebarSize:200,main:L.jsx(Wl,{text:h.text,language:h.language,highlight:h.highlight,revealLine:h.revealLine,readOnly:!0,lineNumbers:!0}),sidebar:L.jsx(my,{rightToolbar:a==="locator"||a==="aria"?[L.jsx(Me,{icon:"files",title:"Copy",onClick:()=>mu((a==="locator"?v:m)||"")},1)]:[],tabs:[{id:"locator",title:"Locator",render:()=>L.jsx(Wl,{text:v,placeholder:"Type locator to inspect",language:h.language,focusOnChange:!0,onChange:y,wrapLines:!0})},{id:"log",title:"Log",render:()=>L.jsx(Qy,{language:h.language,log:Array.from(n.values())})},{id:"aria",title:"Aria",render:()=>L.jsx(Wl,{text:m||"",placeholder:"Type aria template to match",language:"yaml",onChange:S,highlight:p,wrapLines:!0})}],selectedTab:a,setSelectedTab:u})})]})},u1=({})=>{const[e,t]=B.useState([]),[n,r]=B.useState(!1),[s,i]=B.useState(new Map),[l,o]=B.useState("none");return window.playwrightSetMode=o,window.playwrightSetSources=B.useCallback(a=>{t(a),window.playwrightSourcesEchoForTest=a},[]),window.playwrightSetPaused=r,window.playwrightUpdateLogs=a=>{i(u=>{const m=new Map(u);for(const f of a)f.reveal=!u.has(f.id),m.set(f.id,f);return m})},L.jsx(a1,{sources:e,paused:n,log:s,mode:l})};(async()=>(Yp(),ih(document.querySelector("#root")).render(L.jsx(u1,{}))))();export{c1 as c,xp as g};
