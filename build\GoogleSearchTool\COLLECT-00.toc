([('GoogleSearchTool.exe',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\GoogleSearchTool.exe',
   'EXECUTABLE'),
  ('playwright\\driver\\node.exe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('pyarrow\\arrow_flight.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_python.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_python.dll',
   'BINARY'),
  ('pyarrow\\arrow.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow.dll',
   'BINARY'),
  ('pyarrow\\parquet.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\parquet.dll',
   'BINARY'),
  ('pyarrow\\msvcp140-c691275f5538a516494cdd39ad3f5ca6.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\msvcp140-c691275f5538a516494cdd39ad3f5ca6.dll',
   'BINARY'),
  ('pyarrow\\arrow_dataset.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_dataset.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_parquet_encryption.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.dll',
   'BINARY'),
  ('pyarrow\\arrow_acero.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_acero.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_flight.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_python_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_substrait.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_substrait.dll',
   'BINARY'),
  ('python311.dll',
   'D:\\software\\miniconda\\envs\\email_page\\python311.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\greenlet\\_greenlet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\writers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\testing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\sas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\reshape.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\properties.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\parsers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\ops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\missing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\interval.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\internals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\indexing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\index.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\hashing.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\groupby.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\arrays.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\algos.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_compute.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_compute.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_substrait.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_substrait.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_s3fs.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_s3fs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet_encryption.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_parquet_encryption.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_parquet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_orc.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_orc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_json.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_json.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_hdfs.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_hdfs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_gcsfs.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_gcsfs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_fs.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_fs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_flight.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_flight.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet_encryption.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_parquet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_orc.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_orc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_csv.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_csv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_acero.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_acero.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_feather.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_feather.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\lib.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\lib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\software\\miniconda\\envs\\email_page\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\software\\miniconda\\envs\\email_page\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\software\\miniconda\\envs\\email_page\\zlib.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'D:\\software\\miniconda\\envs\\email_page\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('python3.dll',
   'D:\\software\\miniconda\\envs\\email_page\\python3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\software\\miniconda\\envs\\email_page\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\software\\miniconda\\envs\\email_page\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CFOW-Ezb.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CFOW-Ezb.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.BatfzHMG.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.BatfzHMG.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\task.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\transport.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\index.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\ariaSnapshot.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\ariaSnapshot.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\rtti.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\errors.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.C3UTv-Ge.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.C3UTv-Ge.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-DGS0JLxS.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-DGS0JLxS.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\clock.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\time.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\py.typed',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-C3UTv-Ge.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-C3UTv-Ge.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\multimap.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-CyuxU5C-.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-CyuxU5C-.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\LICENSE',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\defaultSettingsView.2xeEXCXv.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\defaultSettingsView.2xeEXCXv.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\chat.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\chat.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\network.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.m4IPRPOd.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.m4IPRPOd.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clock.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debug.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\headers.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\debug.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-CNAqJrkA.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-CNAqJrkA.js',
   'DATA'),
  ('playwright\\driver\\README.md',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\profiler.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.Beg8tuEN.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.Beg8tuEN.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\comparators.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-c-SNdYZy.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-c-SNdYZy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.qVn2ZnpC.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.qVn2ZnpC.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\ascii.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\defaultSettingsView-5nVJRt0A.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\defaultSettingsView-5nVJRt0A.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-eHBmevrY.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-eHBmevrY.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zones.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\sequence.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\sequence.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\NOTICE',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\NOTICE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\types.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\common\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\crypto.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\errors.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\env.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_primitive.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_primitive.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\expression.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\expression.h',
   'DATA'),
  ('pyarrow\\io.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\io.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'DATA'),
  ('pyarrow\\arrow_python.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_python.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\message.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\message.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\properties.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\properties.h',
   'DATA'),
  ('pyarrow\\compat.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\compat.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_init.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\mman.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\mman.h',
   'DATA'),
  ('pyarrow\\_gcsfs.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_gcsfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_auth.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\byte_size.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\byte_size.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\benchmark.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\arrow.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow.lib',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_dict.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_dict.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\parquet\\printer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\printer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor\\converter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\tensor\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client.h',
   'DATA'),
  ('pyarrow\\lib.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\lib.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\stdio.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\stdio.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\platform.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\bool8.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\bool8.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\decimal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_substrait.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compression.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\compression.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\converter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\flight.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\reader.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_cuda.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\_acero.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_acero.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'DATA'),
  ('pyarrow\\_json.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_json.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\error.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\error.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\abi.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\abi.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.cc',
   'DATA'),
  ('pyarrow\\tensor.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tensor.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'DATA'),
  ('pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\simd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\simd.h',
   'DATA'),
  ('pyarrow\\arrow_flight.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compare.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\compare.h',
   'DATA'),
  ('pyarrow\\arrow_substrait.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_substrait.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\gdb.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\xxhasher.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\xxhasher.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\prefetch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\prefetch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\stopwatch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\stopwatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\future.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\future.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\builder.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\types.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table_builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\table_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_nested.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compare.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compare.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\utf8.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\utf8.h',
   'DATA'),
  ('pyarrow\\device.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\device.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\kernel.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\kernel.h',
   'DATA'),
  ('pyarrow\\_compute.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_compute.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\chunker.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\chunker.h',
   'DATA'),
  ('pyarrow\\_dataset.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset.pxd',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\encryption.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\encryption.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hash_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\hash_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension_type.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\io_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\io_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'DATA'),
  ('pyarrow\\config.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\config.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\io.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\double_conversion.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\double_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\print.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_decimal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_util.h',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\queue.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_base.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_base.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow\\lib.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\lib.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array.h',
   'DATA'),
  ('pyarrow\\arrow_dataset.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_dataset.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\caching.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\caching.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\platform.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\transform.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\transform.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\schema.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\schema.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\size_statistics.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\size_statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\memory.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\memory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_traits.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\memory.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\memory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string_builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\string_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\thread_pool.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\thread_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\device.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\spaced.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\spaced.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\_json.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_json.pyx',
   'DATA'),
  ('pyarrow\\_cuda.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_cuda.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_binary.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_binary.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_fixup.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_acero.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_acero.pxd',
   'DATA'),
  ('pyarrow\\_substrait.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_substrait.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'DATA'),
  ('pyarrow\\_csv.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_csv.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\converter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\file.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\file.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_vector.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\trie.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\trie.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\future_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\future_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\visitor.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer_builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\buffer_builder.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_run_end.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_run_end.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\parallel.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\parallel.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\json.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\json.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\float16.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\float16.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\list_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\list_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_test.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\query_context.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\query_context.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\async.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\registry.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\registry.h',
   'DATA'),
  ('pyarrow\\_hdfs.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_hdfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\cast.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\range.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\range.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\map.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\map.h',
   'DATA'),
  ('pyarrow\\_compute.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_compute.pyx',
   'DATA'),
  ('pyarrow\\tests\\extensions.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\extensions.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\_feather.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_feather.pyx',
   'DATA'),
  ('pyarrow\\includes\\libarrow_feather.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_feather.pxd',
   'DATA'),
  ('pyarrow\\includes\\libarrow_python.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_python.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\iterator.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\iterator.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\process.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\process.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\vendored\\CMakeLists.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\vendored\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\builder.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\builder.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\platform.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\page_index.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\page_index.h',
   'DATA'),
  ('pyarrow\\__init__.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison_inc.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison_inc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_fs.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_fs.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\map_node.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\map_node.h',
   'DATA'),
  ('pyarrow\\lib_api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\lib_api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\file_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\discovery.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\discovery.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'DATA'),
  ('pyarrow\\types.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\types.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\api.h',
   'DATA'),
  ('pyarrow\\_parquet.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\options.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\hasher.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\hasher.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\hdfs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\hdfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunk_resolver.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\chunk_resolver.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\vector.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\slow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\slow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'DATA'),
  ('pyarrow\\arrow_acero.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_acero.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\debug.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\debug.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow\\array.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\array.pxi',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\writer.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'DATA'),
  ('pyarrow\\public-api.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\public-api.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\config.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\decimal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\schema.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\schema.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_allocator.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl_allocator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\scalar.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\scalar.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\data.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\data.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encoding.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encoding.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\status.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\status.h',
   'DATA'),
  ('pyarrow\\ipc.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\ipc.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\platform.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_parser.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_parser.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_type_inline.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_type_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\sparse_tensor.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\sparse_tensor.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_compatibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.orc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\api.h',
   'DATA'),
  ('pyarrow\\_dlpack.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dlpack.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\pcg_random.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\pcg_random.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\buffered.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\buffered.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\checked_cast.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\checked_cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\parser.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\README.md',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\README.md',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\io.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\io.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\file_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\extension_type.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\bridge.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\bridge.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\metadata.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\projector.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\projector.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_json.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_json.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_page.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_page.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types_async.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types_async.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion_inc.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion_inc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_data_inline.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_data_inline.h',
   'DATA'),
  ('pyarrow\\tests\\bound_function_visit_strings.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\task_group.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\task_group.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\visibility.h',
   'DATA'),
  ('pyarrow\\arrow_python_parquet_encryption.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\uri.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\uri.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.cc',
   'DATA'),
  ('pyarrow\\arrow_python_flight.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\arrow_python_flight.lib',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport_server.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport_server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ubsan.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\ubsan.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_init.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_init.cc',
   'DATA'),
  ('pyarrow\\scalar.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\scalar.pxi',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_material.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_material.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\stream_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_dict.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_dict.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\type_traits.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\schema.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\api\\schema.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\small_vector.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\small_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\datum.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\datum.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool_test.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\sort.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\sort.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'DATA'),
  ('pyarrow\\tests\\pyarrow_cython_example.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow\\include\\parquet\\statistics.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_writer.h',
   'DATA'),
  ('pyarrow\\gandiva.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\gandiva.pyx',
   'DATA'),
  ('pyarrow\\includes\\libgandiva.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libgandiva.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\pandas-shim.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\pandas-shim.pxi',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\span.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\span.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\ordering.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\ordering.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\converter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logger.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\logger.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\iterators.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'DATA'),
  ('pyarrow\\_fs.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_fs.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\filesystem.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\benchmark_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\opaque.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\opaque.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\executor_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\executor_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_union.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_union.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\align_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\align_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\options.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\interfaces.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\interfaces.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_traits.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\launder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\launder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_base.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_iterator.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl_iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'DATA'),
  ('pyarrow\\_orc.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_base.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\test_common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\test_common.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\serialize.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\serialize.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\serialize.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\serialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\visibility.h',
   'DATA'),
  ('pyarrow\\_csv.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_csv.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\functional.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\functional.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cancel.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\cancel.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\crc32.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\crc32.h',
   'DATA'),
  ('pyarrow\\lib.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\lib.pxd',
   'DATA'),
  ('pyarrow\\_dataset.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\generator.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\generator.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunker.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunker.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\json\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\async.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\time.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\time.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\task_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\task_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_auth_handlers.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_auth_handlers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\test_common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\udf.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet_encryption.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\concatenate.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\concatenate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dict_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\dict_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dispatch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\dispatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tracing.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\tracing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\type_traits.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\concurrency.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\concurrency.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_binary.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_binary.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\schema_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\schema_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\parser.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\strptime.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\strptime.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\middleware.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\base64.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\base64.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'DATA'),
  ('pyarrow\\table.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\table.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\algorithm.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\algorithm.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_flight.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libarrow_flight.pxd',
   'DATA'),
  ('pyarrow\\includes\\common.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\common.pxd',
   'DATA'),
  ('pyarrow\\_s3fs.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_s3fs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cpu_info.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\cpu_info.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\serialize.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\serialize.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_scanner.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\column_scanner.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\ipc.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\table.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\scanner.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\scanner.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\inference.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\datetime.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function_options.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function_options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\iterators.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\stl.h',
   'DATA'),
  ('pyarrow\\includes\\libparquet_encryption.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\exception.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\exception.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\unreachable.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\unreachable.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\test_common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\test_common.h',
   'DATA'),
  ('pyarrow\\_fs.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_fs.pyx',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\reader.h',
   'DATA'),
  ('pyarrow\\_flight.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_flight.pyx',
   'DATA'),
  ('pyarrow\\_acero.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_acero.pyx',
   'DATA'),
  ('pyarrow\\_azurefs.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_azurefs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor_generate.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\visitor_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\regex.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\regex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\endian.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\endian.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\result.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\result.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hashing.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\hashing.h',
   'DATA'),
  ('pyarrow\\_dataset_orc.pyx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_dataset_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\deserialize.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\deserialize.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\record_batch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\record_batch.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\_orc.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_orc.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\math_constants.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\math_constants.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'DATA'),
  ('pyarrow\\parquet.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\parquet.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'DATA'),
  ('pyarrow\\memory.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\memory.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\value_parsing.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\value_parsing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\plan.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\helpers.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ree_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\ree_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_auth.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\stream_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\macros.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\macros.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\buffer.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\diff.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\diff.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\deserialize.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\deserialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pretty_print.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\pretty_print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_nested.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.cc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\deserialize.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\deserialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\validate.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\validate.h',
   'DATA'),
  ('pyarrow\\_cuda.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_cuda.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\device_allocation_type_set.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\device_allocation_type_set.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'DATA'),
  ('pyarrow\\includes\\__init__.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\includes\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\helpers.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\c\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\statistics.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\feather.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\feather.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\random.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tdigest.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\tdigest.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\delimiting.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\delimiting.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\mutex.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\mutex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\parquet_version.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\parquet_version.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\builder.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\partition.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\partition.h',
   'DATA'),
  ('pyarrow\\benchmark.pxi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\benchmark.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\test_common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\test_common.h',
   'DATA'),
  ('pyarrow\\_parquet.pxd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_parquet.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\config.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\union_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\union_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunked_array.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\chunked_array.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib_api.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\uuid.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\extension\\uuid.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_fwd.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_init.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_init.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logging.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\logging.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\math.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\math.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\partition_util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\partition_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\pch.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\acero\\pch.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\visibility.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\compressed.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\io\\compressed.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\util.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\matchers.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\testing\\matchers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\csv.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_time.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_time.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\extension_type.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_flight_server.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_flight_server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\test_common.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\formatting.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\util\\formatting.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_array_inline.h',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\include\\arrow\\visit_array_inline.h',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('numpy-2.2.3.dist-info\\entry_points.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.3.dist-info\\RECORD',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.3.dist-info\\INSTALLER',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.3.dist-info\\METADATA',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.3.dist-info\\DELVEWHEEL',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.3.dist-info\\LICENSE.txt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.3.dist-info\\WHEEL',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy-2.2.3.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'D:\\crawler\\email_page\\build\\GoogleSearchTool\\base_library.zip',
   'DATA')],)
