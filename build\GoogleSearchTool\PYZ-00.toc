('D:\\crawler\\email_page\\build\\GoogleSearchTool\\PYZ-00.pyz',
 [('PIL',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\cmd.py', 'PYMODULE'),
  ('code',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\copy.py',
   'PYMODULE'),
  ('csv', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email_page',
   'D:\\crawler\\email_page\\email_page\\__init__.py',
   'PYMODULE'),
  ('email_page.cookies',
   'D:\\crawler\\email_page\\email_page\\cookies.py',
   'PYMODULE'),
  ('email_page.proxy',
   'D:\\crawler\\email_page\\email_page\\proxy.py',
   'PYMODULE'),
  ('email_page.spider',
   'D:\\crawler\\email_page\\email_page\\spider.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\fractions.py',
   'PYMODULE'),
  ('fsspec',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\__init__.py',
   'PYMODULE'),
  ('fsspec._version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE'),
  ('fsspec.archive',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\archive.py',
   'PYMODULE'),
  ('fsspec.asyn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\asyn.py',
   'PYMODULE'),
  ('fsspec.caching',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE'),
  ('fsspec.callbacks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE'),
  ('fsspec.compression',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE'),
  ('fsspec.config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\config.py',
   'PYMODULE'),
  ('fsspec.conftest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\conftest.py',
   'PYMODULE'),
  ('fsspec.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\core.py',
   'PYMODULE'),
  ('fsspec.dircache',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE'),
  ('fsspec.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE'),
  ('fsspec.fuse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\fuse.py',
   'PYMODULE'),
  ('fsspec.generic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\generic.py',
   'PYMODULE'),
  ('fsspec.gui',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\gui.py',
   'PYMODULE'),
  ('fsspec.implementations',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE'),
  ('fsspec.implementations.arrow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\arrow.py',
   'PYMODULE'),
  ('fsspec.implementations.asyn_wrapper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\asyn_wrapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_mapper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\cache_mapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_metadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\cache_metadata.py',
   'PYMODULE'),
  ('fsspec.implementations.cached',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\cached.py',
   'PYMODULE'),
  ('fsspec.implementations.dask',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\dask.py',
   'PYMODULE'),
  ('fsspec.implementations.data',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\data.py',
   'PYMODULE'),
  ('fsspec.implementations.dbfs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\dbfs.py',
   'PYMODULE'),
  ('fsspec.implementations.dirfs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE'),
  ('fsspec.implementations.ftp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\ftp.py',
   'PYMODULE'),
  ('fsspec.implementations.git',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\git.py',
   'PYMODULE'),
  ('fsspec.implementations.github',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\github.py',
   'PYMODULE'),
  ('fsspec.implementations.http',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\http.py',
   'PYMODULE'),
  ('fsspec.implementations.http_sync',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\http_sync.py',
   'PYMODULE'),
  ('fsspec.implementations.jupyter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\jupyter.py',
   'PYMODULE'),
  ('fsspec.implementations.libarchive',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\libarchive.py',
   'PYMODULE'),
  ('fsspec.implementations.local',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE'),
  ('fsspec.implementations.memory',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\memory.py',
   'PYMODULE'),
  ('fsspec.implementations.reference',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\reference.py',
   'PYMODULE'),
  ('fsspec.implementations.sftp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\sftp.py',
   'PYMODULE'),
  ('fsspec.implementations.smb',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\smb.py',
   'PYMODULE'),
  ('fsspec.implementations.tar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\tar.py',
   'PYMODULE'),
  ('fsspec.implementations.webhdfs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\webhdfs.py',
   'PYMODULE'),
  ('fsspec.implementations.zip',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\implementations\\zip.py',
   'PYMODULE'),
  ('fsspec.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\json.py',
   'PYMODULE'),
  ('fsspec.mapping',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE'),
  ('fsspec.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\parquet.py',
   'PYMODULE'),
  ('fsspec.registry',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE'),
  ('fsspec.spec',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\spec.py',
   'PYMODULE'),
  ('fsspec.transaction',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE'),
  ('fsspec.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE'),
  ('ftplib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\glob.py',
   'PYMODULE'),
  ('greenlet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.collections',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\platform.py',
   'PYMODULE'),
  ('playwright',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\__init__.py',
   'PYMODULE'),
  ('playwright._impl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._accessibility',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'PYMODULE'),
  ('playwright._impl._api_structures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'PYMODULE'),
  ('playwright._impl._artifact',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'PYMODULE'),
  ('playwright._impl._assertions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'PYMODULE'),
  ('playwright._impl._async_base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_async_base.py',
   'PYMODULE'),
  ('playwright._impl._browser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_browser.py',
   'PYMODULE'),
  ('playwright._impl._browser_context',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'PYMODULE'),
  ('playwright._impl._browser_type',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'PYMODULE'),
  ('playwright._impl._cdp_session',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'PYMODULE'),
  ('playwright._impl._clock',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_clock.py',
   'PYMODULE'),
  ('playwright._impl._connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_connection.py',
   'PYMODULE'),
  ('playwright._impl._console_message',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'PYMODULE'),
  ('playwright._impl._dialog',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'PYMODULE'),
  ('playwright._impl._download',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_download.py',
   'PYMODULE'),
  ('playwright._impl._driver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_driver.py',
   'PYMODULE'),
  ('playwright._impl._element_handle',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'PYMODULE'),
  ('playwright._impl._errors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_errors.py',
   'PYMODULE'),
  ('playwright._impl._event_context_manager',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._fetch',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'PYMODULE'),
  ('playwright._impl._file_chooser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'PYMODULE'),
  ('playwright._impl._frame',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_frame.py',
   'PYMODULE'),
  ('playwright._impl._glob',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_glob.py',
   'PYMODULE'),
  ('playwright._impl._greenlets',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_greenlets.py',
   'PYMODULE'),
  ('playwright._impl._har_router',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'PYMODULE'),
  ('playwright._impl._helper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_helper.py',
   'PYMODULE'),
  ('playwright._impl._impl_to_api_mapping',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'PYMODULE'),
  ('playwright._impl._input',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_input.py',
   'PYMODULE'),
  ('playwright._impl._js_handle',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'PYMODULE'),
  ('playwright._impl._json_pipe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'PYMODULE'),
  ('playwright._impl._local_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'PYMODULE'),
  ('playwright._impl._locator',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_locator.py',
   'PYMODULE'),
  ('playwright._impl._map',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_map.py',
   'PYMODULE'),
  ('playwright._impl._network',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_network.py',
   'PYMODULE'),
  ('playwright._impl._object_factory',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'PYMODULE'),
  ('playwright._impl._page',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_page.py',
   'PYMODULE'),
  ('playwright._impl._playwright',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'PYMODULE'),
  ('playwright._impl._selectors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'PYMODULE'),
  ('playwright._impl._set_input_files_helpers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'PYMODULE'),
  ('playwright._impl._str_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'PYMODULE'),
  ('playwright._impl._stream',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_stream.py',
   'PYMODULE'),
  ('playwright._impl._tracing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'PYMODULE'),
  ('playwright._impl._transport',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_transport.py',
   'PYMODULE'),
  ('playwright._impl._video',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_video.py',
   'PYMODULE'),
  ('playwright._impl._waiter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_waiter.py',
   'PYMODULE'),
  ('playwright._impl._web_error',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'PYMODULE'),
  ('playwright._impl._writable_stream',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'PYMODULE'),
  ('playwright._repo_version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\_repo_version.py',
   'PYMODULE'),
  ('playwright.async_api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\async_api\\__init__.py',
   'PYMODULE'),
  ('playwright.async_api._context_manager',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\async_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright.async_api._generated',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\playwright\\async_api\\_generated.py',
   'PYMODULE'),
  ('plistlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pyarrow',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\__init__.py',
   'PYMODULE'),
  ('pyarrow._compute_docstrings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_compute_docstrings.py',
   'PYMODULE'),
  ('pyarrow._generated_version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\_generated_version.py',
   'PYMODULE'),
  ('pyarrow.acero',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\acero.py',
   'PYMODULE'),
  ('pyarrow.benchmark',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\benchmark.py',
   'PYMODULE'),
  ('pyarrow.cffi',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\cffi.py',
   'PYMODULE'),
  ('pyarrow.compute',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\compute.py',
   'PYMODULE'),
  ('pyarrow.conftest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\conftest.py',
   'PYMODULE'),
  ('pyarrow.csv',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\csv.py',
   'PYMODULE'),
  ('pyarrow.cuda',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\cuda.py',
   'PYMODULE'),
  ('pyarrow.dataset',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\dataset.py',
   'PYMODULE'),
  ('pyarrow.feather',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\feather.py',
   'PYMODULE'),
  ('pyarrow.flight',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\flight.py',
   'PYMODULE'),
  ('pyarrow.fs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\fs.py',
   'PYMODULE'),
  ('pyarrow.interchange',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\interchange\\__init__.py',
   'PYMODULE'),
  ('pyarrow.interchange.buffer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\interchange\\buffer.py',
   'PYMODULE'),
  ('pyarrow.interchange.column',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\interchange\\column.py',
   'PYMODULE'),
  ('pyarrow.interchange.dataframe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pyarrow.interchange.from_dataframe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pyarrow.ipc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\ipc.py',
   'PYMODULE'),
  ('pyarrow.json',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\json.py',
   'PYMODULE'),
  ('pyarrow.jvm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\jvm.py',
   'PYMODULE'),
  ('pyarrow.orc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\orc.py',
   'PYMODULE'),
  ('pyarrow.pandas_compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\pandas_compat.py',
   'PYMODULE'),
  ('pyarrow.parquet',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\parquet\\__init__.py',
   'PYMODULE'),
  ('pyarrow.parquet.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\parquet\\core.py',
   'PYMODULE'),
  ('pyarrow.parquet.encryption',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\parquet\\encryption.py',
   'PYMODULE'),
  ('pyarrow.substrait',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\substrait.py',
   'PYMODULE'),
  ('pyarrow.tests',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\__init__.py',
   'PYMODULE'),
  ('pyarrow.tests.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\tests\\util.py',
   'PYMODULE'),
  ('pyarrow.types',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\types.py',
   'PYMODULE'),
  ('pyarrow.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\util.py',
   'PYMODULE'),
  ('pyarrow.vendored',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\vendored\\__init__.py',
   'PYMODULE'),
  ('pyarrow.vendored.docscrape',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\vendored\\docscrape.py',
   'PYMODULE'),
  ('pyarrow.vendored.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyarrow\\vendored\\version.py',
   'PYMODULE'),
  ('pydoc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyee',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pyee.asyncio',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('pyee.base',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('pytz',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tqdm',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\software\\miniconda\\envs\\email_page\\Lib\\tty.py', 'PYMODULE'),
  ('typing',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('xml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\software\\miniconda\\envs\\email_page\\Lib\\zipimport.py',
   'PYMODULE')])
