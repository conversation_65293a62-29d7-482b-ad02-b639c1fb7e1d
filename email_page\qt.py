# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/11 21:09
File Name:qt.py
"""
import sys
import os
import pandas as pd
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout,
                             QWidget, QFileDialog, QLabel, QProgressBar, QTextEdit, QMessageBox,
                             QSpinBox)
from PyQt5.QtCore import QThread, pyqtSignal

from email_page.spider import GoogleSearchThreadManager


# 获取应用程序根目录
def get_app_root():
    """获取应用程序根目录，兼容PyInstaller打包后的情况"""
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的情况
        return os.path.dirname(sys.executable)
    else:
        # 开发环境下的情况
        return os.path.dirname(os.path.abspath(__file__))


# 创建一个全局变量来跟踪是否已经尝试安装浏览器
browser_install_attempted = False


class WorkerThread(QThread):
    """工作线程，用于在后台运行爬虫"""
    update_progress = pyqtSignal(int, int, float)
    update_log = pyqtSignal(str)
    finished = pyqtSignal(bool)

    def __init__(self, excel_file, num_threads, browser_path=None):
        super().__init__()
        self.excel_file = excel_file
        self.num_threads = num_threads
        self.browser_path = browser_path
        self.is_running = False

    def run(self):
        self.is_running = True
        try:
            # 如果有浏览器路径，设置环境变量
            if self.browser_path:
                os.environ["PLAYWRIGHT_BROWSERS_PATH"] = self.browser_path
            # 创建搜索管理器
            manager = GoogleSearchThreadManager(
                num_threads=self.num_threads,
                excel_file=self.excel_file,
                browser_path=self.browser_path,
                update_log=self.update_log
            )

            # 设置进度回调
            def progress_callback(completed, total, progress):
                self.update_progress.emit(completed, total, progress)

            # 设置日志回调
            original_print = print

            def custom_print(*args, **kwargs):
                message = " ".join(map(str, args))
                if "[线程" in message and "查询语句" in message:
                    # 提取查询语句和页数
                    try:
                        parts = message.split("查询语句：")
                        if len(parts) > 1:
                            query_part = parts[1]
                            query = query_part.split(" 总页数:")[0].strip()
                            pages = query_part.split("总页数:")[1].strip().replace("\n", "")
                            self.update_log.emit(f"查询: {query} -> 结果页数: {pages}")
                    except:
                        pass
                original_print(*args, **kwargs)

            # 替换print函数
            import builtins
            builtins.print = custom_print

            manager.set_progress_callback(progress_callback)

            # 启动搜索
            started = manager.start()
            if started:
                manager.wait_completion()
                self.finished.emit(True)
            else:
                self.update_log.emit("没有需要处理的查询")
                self.finished.emit(False)

            # 恢复原始print函数
            builtins.print = original_print

        except Exception as e:
            self.update_log.emit(f"错误: {str(e)}")
            self.finished.emit(False)
            import traceback
            traceback.print_exc()
        finally:
            self.is_running = False


class PlaywrightInstallerThread(QThread):
    """用于在后台安装Playwright浏览器的线程"""
    installation_complete = pyqtSignal(bool, str)
    installation_progress = pyqtSignal(str)

    def run(self):
        global browser_install_attempted

        # 如果已经尝试过安装，不要重复安装
        if browser_install_attempted:
            self.installation_complete.emit(False, "已经尝试过安装，请检查环境")
            return

        try:
            self.installation_progress.emit("正在安装Playwright浏览器...")

            # 确保playwright库已安装
            try:
                import playwright
            except ImportError:
                self.installation_progress.emit("Playwright库未安装，正在安装...")
                try:
                    subprocess.run(
                        [sys.executable, "-m", "pip", "install", "playwright"],
                        check=True,
                        capture_output=True
                    )
                    self.installation_progress.emit("Playwright库安装完成")
                except Exception as e:
                    self.installation_complete.emit(False, f"安装Playwright库失败: {str(e)}")
                    return

            # 安装浏览器
            self.installation_progress.emit("正在下载并安装Playwright浏览器...")

            # 使用默认浏览器路径
            os.environ.pop("PLAYWRIGHT_BROWSERS_PATH", None)

            # 运行安装命令
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "playwright", "install", "chromium"],
                    capture_output=True,
                    text=True,
                    check=False
                )

                if result.returncode == 0:
                    self.installation_progress.emit("Playwright浏览器安装成功")

                    # 获取浏览器安装路径
                    import tempfile
                    from pathlib import Path

                    # 常见的浏览器安装路径
                    possible_paths = [
                        os.path.join(os.path.expanduser("~"), ".cache", "ms-playwright"),  # Linux
                        os.path.join(os.path.expanduser("~"), "AppData", "Local", "ms-playwright"),  # Windows
                        os.path.join(tempfile.gettempdir(), "playwright")  # 临时目录
                    ]

                    # 检查哪个路径存在
                    browser_path = None
                    for path in possible_paths:
                        if os.path.exists(path):
                            browser_path = path
                            break

                    if browser_path:
                        self.installation_progress.emit(f"浏览器安装在: {browser_path}")
                        # 设置环境变量
                        os.environ["PLAYWRIGHT_BROWSERS_PATH"] = browser_path
                        self.installation_complete.emit(True, "Playwright浏览器已成功安装")
                    else:
                        self.installation_complete.emit(False, "无法确定浏览器安装路径")
                else:
                    self.installation_complete.emit(False, f"安装失败: {result.stderr}")
            except Exception as e:
                self.installation_complete.emit(False, f"安装过程出错: {str(e)}")

        except Exception as e:
            self.installation_complete.emit(False, f"安装过程中出错: {str(e)}")
        finally:
            browser_install_attempted = True


class GoogleSearchApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.excel_file = None
        self.worker_thread = None
        self.installer_thread = None

        self.initUI()

        # 自动开始安装浏览器
        # self.install_playwright()

    def initUI(self):
        self.setWindowTitle('谷歌搜索爬虫工具')
        self.setGeometry(300, 300, 800, 600)

        # 创建主布局
        main_layout = QVBoxLayout()

        # 文件选择区域
        file_layout = QHBoxLayout()
        self.file_label = QLabel('未选择文件')
        self.file_btn = QPushButton('选择Excel文件')
        self.file_btn.clicked.connect(self.select_file)
        file_layout.addWidget(self.file_label, 7)
        file_layout.addWidget(self.file_btn, 3)
        main_layout.addLayout(file_layout)

        # 添加线程数配置区域
        thread_layout = QHBoxLayout()
        thread_label = QLabel('线程数:')
        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setMinimum(1)
        self.thread_spinbox.setMaximum(100)  # 设置最大线程数为10
        self.thread_spinbox.setValue(10)  # 默认为4个线程
        self.thread_spinbox.setToolTip('设置爬虫使用的线程数量，建议不超过CPU核心数')
        thread_layout.addWidget(thread_label)
        thread_layout.addWidget(self.thread_spinbox)
        thread_layout.addStretch(1)  # 添加弹性空间
        main_layout.addLayout(thread_layout)
        # 在现有UI中添加浏览器路径选择
        browser_path_layout = QHBoxLayout()
        self.browser_path_label = QLabel('未选择浏览器路径')
        self.browser_path_btn = QPushButton('选择浏览器路径')
        self.browser_path_btn.clicked.connect(self.select_browser_path)
        browser_path_layout.addWidget(self.browser_path_label, 7)
        browser_path_layout.addWidget(self.browser_path_btn, 3)
        main_layout.addLayout(browser_path_layout)  # 在适当位置添加到主布局
        # 运行按钮
        self.run_btn = QPushButton('开始爬取')
        self.run_btn.clicked.connect(self.start_crawling)
        self.run_btn.setEnabled(False)
        main_layout.addWidget(self.run_btn)

        # 添加安装Playwright按钮
        self.install_playwright_btn = QPushButton('安装Playwright浏览器')
        self.install_playwright_btn.clicked.connect(self.install_playwright)
        main_layout.addWidget(self.install_playwright_btn)

        # 进度条
        progress_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel('0/0')
        progress_layout.addWidget(self.progress_bar, 9)
        progress_layout.addWidget(self.progress_label, 1)
        main_layout.addLayout(progress_layout)

        # 日志区域
        log_label = QLabel('运行日志:')
        main_layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        main_layout.addWidget(self.log_text)

        # 添加初始日志
        self.log_text.append("程序已启动，正在初始化...")

        # 设置主窗口
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def select_browser_path(self):
        """让用户选择浏览器路径"""
        browser_path = QFileDialog.getExistingDirectory(self, '选择浏览器目录')
        if browser_path:
            self.browser_path = browser_path
            self.browser_path_label.setText(browser_path)
            os.environ["PLAYWRIGHT_BROWSERS_PATH"] = browser_path
            self.log_text.append(f"已设置浏览器路径: {browser_path}")

            # 验证路径是否有效
            if self.verify_browser_path(browser_path):
                self.log_text.append("浏览器路径有效")
                self.run_btn.setEnabled(self.excel_file is not None)  # 如果已选择Excel文件则启用运行按钮
            else:
                self.log_text.append("警告: 选择的目录可能不包含有效的浏览器")
                QMessageBox.warning(self, '路径验证', '所选目录可能不包含有效的浏览器，爬虫可能无法正常工作')

    def verify_browser_path(self, path):
        """验证所选路径是否包含浏览器文件"""
        # 根据不同操作系统，检查常见的浏览器可执行文件
        common_browsers = [
            "chrome.exe", "chromium.exe",  # Windows
            "chrome", "chromium",  # Linux/Mac
            "Google Chrome.app", "Chromium.app"  # macOS
        ]

        # 递归检查目录中是否存在浏览器可执行文件
        for root, dirs, files in os.walk(path):
            for file in files:
                if file.lower() in [b.lower() for b in common_browsers]:
                    return True

            # 限制递归深度，避免搜索过多目录
            if root.count(os.sep) - path.count(os.sep) > 2:
                break

    def install_playwright(self):
        """安装Playwright浏览器"""
        global browser_install_attempted
        # 检查用户是否已选择自定义路径
        custom_path = os.environ.get("PLAYWRIGHT_BROWSERS_PATH")
        if custom_path and os.path.exists(os.path.join(custom_path, "chromium")):
            self.log_text.append(f"使用自定义浏览器路径: {custom_path}")
            self.install_playwright_btn.setText('浏览器已安装')
            self.install_playwright_btn.setEnabled(False)
            global browser_install_attempted
            browser_install_attempted = True
            return

        if browser_install_attempted:
            self.log_text.append("已经尝试过安装浏览器，不再重复安装")
            return

        if self.installer_thread and self.installer_thread.isRunning():
            self.log_text.append("正在安装中，请稍候...")
            return

        # 禁用按钮
        self.install_playwright_btn.setEnabled(False)
        self.install_playwright_btn.setText('正在安装中...')
        self.log_text.append("开始安装Playwright浏览器...")

        # 创建并启动安装线程
        self.installer_thread = PlaywrightInstallerThread()
        self.installer_thread.installation_progress.connect(self.update_installation_progress)
        self.installer_thread.installation_complete.connect(self.on_installation_complete)
        self.installer_thread.start()

    def update_installation_progress(self, message):
        """更新安装进度信息"""
        self.log_text.append(message)

    def on_installation_complete(self, success, message):
        """Playwright安装完成回调"""
        self.log_text.append(message)

        if success:
            self.install_playwright_btn.setText('浏览器已安装')
            self.install_playwright_btn.setEnabled(False)
            QMessageBox.information(self, '安装成功', 'Playwright浏览器已成功安装！')
        else:
            if "正在安装" not in message:  # 不是进度消息才显示错误
                self.install_playwright_btn.setText('重新安装浏览器')
                self.install_playwright_btn.setEnabled(True)
                QMessageBox.warning(self, '安装失败', f'Playwright浏览器安装失败: {message}')

    def select_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择Excel文件', '', 'Excel文件 (*.xlsx *.xls)')
        if file_path:
            try:
                # 验证Excel文件是否有效
                df = pd.read_excel(file_path)
                if 'query' not in df.columns:
                    QMessageBox.warning(self, '文件错误', 'Excel文件必须包含query列')
                    return

                self.excel_file = file_path
                self.file_label.setText(os.path.basename(file_path))
                self.run_btn.setEnabled(True)
                self.log_text.append(f"已加载文件: {os.path.basename(file_path)}")
                self.log_text.append(f"共有 {len(df)} 条查询数据")

                # 检查pages列
                if 'pages' not in df.columns:
                    df['pages'] = None
                    df.to_excel(file_path, index=False)
                    self.log_text.append("已自动添加pages列")

                # 统计未处理的查询数量
                unprocessed = df['pages'].isna().sum()
                self.log_text.append(f"有 {unprocessed} 条查询待处理")

            except Exception as e:
                QMessageBox.critical(self, '错误', f'无法读取Excel文件: {str(e)}')

    def start_crawling(self):
        if not self.excel_file:
            QMessageBox.warning(self, '错误', '请先选择Excel文件')
            return
        if not self.browser_path:
            QMessageBox.warning(self, '错误', '请先选择浏览器路径')
            return
        if self.worker_thread and self.worker_thread.is_running:
            QMessageBox.information(self, '提示', '爬取任务正在进行中')
            return

        # 获取用户设置的线程数
        num_threads = self.thread_spinbox.value()

        # 创建并启动工作线程
        self.run_btn.setText('正在爬取...')
        self.run_btn.setEnabled(False)
        self.file_btn.setEnabled(False)
        self.thread_spinbox.setEnabled(False)  # 禁用线程数设置
        self.install_playwright_btn.setEnabled(False)  # 禁用安装按钮
        self.progress_bar.setValue(0)
        self.progress_label.setText('0/0')
        self.log_text.append("=== 开始爬取 ===")
        self.log_text.append(f"使用 {num_threads} 个线程")

        self.worker_thread = WorkerThread(self.excel_file, num_threads, self.browser_path)  # 使用用户设置的线程数
        self.worker_thread.update_progress.connect(self.update_progress)
        self.worker_thread.update_log.connect(self.update_log)
        self.worker_thread.finished.connect(self.crawling_finished)
        self.worker_thread.start()

    def update_progress(self, completed, total, progress):
        self.progress_bar.setValue(int(progress))
        self.progress_label.setText(f'{completed}/{total}')

    def update_log(self, message):
        self.log_text.append(message)
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def crawling_finished(self, success):
        self.run_btn.setText('开始爬取')
        self.run_btn.setEnabled(True)
        self.file_btn.setEnabled(True)
        self.thread_spinbox.setEnabled(True)  # 重新启用线程数设置
        self.install_playwright_btn.setEnabled(True)  # 重新启用安装按钮

        if success:
            self.log_text.append("=== 爬取完成 ===")
            QMessageBox.information(self, '完成', '爬取任务已完成')
        else:
            self.log_text.append("=== 爬取终止 ===")

    def closeEvent(self, event):
        if self.worker_thread and self.worker_thread.is_running:
            reply = QMessageBox.question(self, '确认退出',
                                         '爬取任务正在进行中，确定要退出吗？',
                                         QMessageBox.Yes | QMessageBox.No,
                                         QMessageBox.No)
            if reply == QMessageBox.Yes:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def clear_cookies_cache():
    """清除所有cookies缓存文件"""
    try:
        # 清除主目录下的cookies文件夹
        cookies_dir = 'cookies'
        if os.path.exists(cookies_dir):
            import shutil
            shutil.rmtree(cookies_dir)
            print(f"已删除cookies缓存目录: {cookies_dir}")

        # 清除email_page目录下的cookies文件夹
        email_page_cookies_dir = 'email_page/cookies'
        if os.path.exists(email_page_cookies_dir):
            import shutil
            shutil.rmtree(email_page_cookies_dir)
            print(f"已删除cookies缓存目录: {email_page_cookies_dir}")

        # 清除可能存在的单个cookies文件
        possible_cookie_files = [
            'google_cookies.json',
            'email_page/google_cookies.json'
        ]
        for cookie_file in possible_cookie_files:
            if os.path.exists(cookie_file):
                os.remove(cookie_file)
                print(f"已删除cookies文件: {cookie_file}")

        print("cookies缓存清除完成")
    except Exception as e:
        print(f"清除cookies缓存时出错: {e}")


if __name__ == '__main__':
    # 启动时清除所有cookies缓存
    clear_cookies_cache()

    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格，在不同平台上看起来一致
    ex = GoogleSearchApp()
    ex.show()
    sys.exit(app.exec_())
